"""
Unit tests for API routes
"""
import pytest
import json
from unittest.mock import patch, Mock
from app.models.user import User
from app.models.website import Website
from app.models.api_key import Api<PERSON>ey
from app.models.analysis import ContentAnalysis
from tests.conftest import assert_response_success, assert_response_error


class TestWebsiteRoutes:
    """Test website management API routes"""
    
    def test_get_websites_success(self, client, db_session, user, website, api_key):
        """Test successful retrieval of user websites"""
        headers = {'Authorization': f'Bearer {api_key.key_hash}'}
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            response = client.get('/api/websites', headers=headers)
            
            data = assert_response_success(response)
            assert 'data' in data
            assert len(data['data']) == 1
            assert data['data'][0]['domain'] == website.domain
    
    def test_get_websites_unauthorized(self, client, db_session):
        """Test website retrieval without authentication"""
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (None, 'no_authentication')
            
            response = client.get('/api/websites')
            
            assert_response_error(response, 401)
    
    def test_create_website_success(self, client, db_session, user, api_key):
        """Test successful website creation"""
        headers = {
            'Authorization': f'Bearer {api_key.key_hash}',
            'Content-Type': 'application/json'
        }
        
        website_data = {
            'domain': 'newsite.com',
            'title': 'New Test Site',
            'description': 'A new test website',
            'category': 'technology'
        }
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            with patch('app.tasks.analysis_tasks.analyze_website_content.delay') as mock_task:
                mock_task.return_value = Mock(id='task-123')
                
                response = client.post(
                    '/api/websites',
                    data=json.dumps(website_data),
                    headers=headers
                )
                
                data = assert_response_success(response, 201)
                assert 'website_id' in data['data']
                assert data['data']['website']['domain'] == 'newsite.com'
    
    def test_create_website_missing_data(self, client, db_session, user, api_key):
        """Test website creation with missing required data"""
        headers = {
            'Authorization': f'Bearer {api_key.key_hash}',
            'Content-Type': 'application/json'
        }
        
        incomplete_data = {'domain': 'test.com'}  # Missing title
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            response = client.post(
                '/api/websites',
                data=json.dumps(incomplete_data),
                headers=headers
            )
            
            assert_response_error(response, 400)
    
    def test_create_website_duplicate_domain(self, client, db_session, user, website, api_key):
        """Test website creation with duplicate domain"""
        headers = {
            'Authorization': f'Bearer {api_key.key_hash}',
            'Content-Type': 'application/json'
        }
        
        duplicate_data = {
            'domain': website.domain,  # Use existing domain
            'title': 'Duplicate Site'
        }
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            response = client.post(
                '/api/websites',
                data=json.dumps(duplicate_data),
                headers=headers
            )
            
            assert_response_error(response, 409)
    
    def test_get_website_success(self, client, db_session, user, website, api_key):
        """Test successful retrieval of specific website"""
        headers = {'Authorization': f'Bearer {api_key.key_hash}'}
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            with patch('app.services.matching_service.MatchingService.get_match_statistics') as mock_stats:
                mock_stats.return_value = {'total_potential_matches': 5}
                
                response = client.get(f'/api/websites/{website.id}', headers=headers)
                
                data = assert_response_success(response)
                assert data['data']['id'] == website.id
                assert data['data']['domain'] == website.domain
                assert 'match_statistics' in data['data']
    
    def test_get_website_not_found(self, client, db_session, user, api_key):
        """Test retrieval of non-existent website"""
        headers = {'Authorization': f'Bearer {api_key.key_hash}'}
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            response = client.get('/api/websites/99999', headers=headers)
            
            assert_response_error(response, 404)
    
    def test_update_website_success(self, client, db_session, user, website, api_key):
        """Test successful website update"""
        headers = {
            'Authorization': f'Bearer {api_key.key_hash}',
            'Content-Type': 'application/json'
        }
        
        update_data = {
            'title': 'Updated Title',
            'description': 'Updated description'
        }
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            response = client.put(
                f'/api/websites/{website.id}',
                data=json.dumps(update_data),
                headers=headers
            )
            
            data = assert_response_success(response)
            assert data['data']['website']['title'] == 'Updated Title'
    
    def test_delete_website_success(self, client, db_session, user, website, api_key):
        """Test successful website deletion (soft delete)"""
        headers = {'Authorization': f'Bearer {api_key.key_hash}'}
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            response = client.delete(f'/api/websites/{website.id}', headers=headers)
            
            data = assert_response_success(response)
            
            # Verify website was soft deleted
            db_session.refresh(website)
            assert website.status == 'inactive'
    
    def test_analyze_website_with_content(self, client, db_session, user, website, api_key, sample_content_data):
        """Test website analysis with provided content"""
        headers = {
            'Authorization': f'Bearer {api_key.key_hash}',
            'Content-Type': 'application/json'
        }
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            with patch('app.services.content_analysis_service.ContentAnalysisService.analyze_content') as mock_analyze:
                mock_analyze.return_value = {
                    'content_hash': 'test_hash',
                    'quality_score': 8.5,
                    'keyword_analysis': {'primary_keywords': []},
                    'topic_classification': {'primary_topics': []}
                }
                
                response = client.post(
                    f'/api/websites/{website.id}/analyze',
                    data=json.dumps(sample_content_data),
                    headers=headers
                )
                
                data = assert_response_success(response)
                assert 'analysis_id' in data['data']
                assert 'analysis_result' in data['data']
    
    def test_analyze_website_queue_background(self, client, db_session, user, website, api_key):
        """Test website analysis queued for background processing"""
        headers = {
            'Authorization': f'Bearer {api_key.key_hash}',
            'Content-Type': 'application/json'
        }
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            with patch('app.tasks.analysis_tasks.analyze_website_content.delay') as mock_task:
                mock_task.return_value = Mock(id='task-123')
                
                response = client.post(
                    f'/api/websites/{website.id}/analyze',
                    data=json.dumps({}),
                    headers=headers
                )
                
                data = assert_response_success(response, 202)
                assert 'task_id' in data['data']
                assert data['data']['task_id'] == 'task-123'
    
    def test_get_website_analysis(self, client, db_session, user, website, content_analysis, api_key):
        """Test retrieval of website analysis results"""
        headers = {'Authorization': f'Bearer {api_key.key_hash}'}
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            response = client.get(f'/api/websites/{website.id}/analysis', headers=headers)
            
            data = assert_response_success(response)
            assert 'latest_analysis' in data['data']
            assert 'analysis_history' in data['data']
            assert data['data']['latest_analysis']['id'] == content_analysis.id
    
    def test_get_website_matches(self, client, db_session, user, website, api_key):
        """Test retrieval of website backlink matches"""
        headers = {'Authorization': f'Bearer {api_key.key_hash}'}
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            with patch('app.services.matching_service.MatchingService.find_matches') as mock_matches:
                mock_partner = Mock()
                mock_partner.id = 2
                mock_partner.domain = 'partner.com'
                mock_partner.title = 'Partner Site'
                mock_partner.category = 'technology'
                mock_partner.language = 'en'
                
                mock_matches.return_value = [{
                    'partner_website': mock_partner,
                    'match_score': {'total_score': 0.85},
                    'reasons': ['High content similarity'],
                    'estimated_value': 8.5,
                    'mutual_benefit': 0.8
                }]
                
                response = client.get(f'/api/websites/{website.id}/matches', headers=headers)
                
                data = assert_response_success(response)
                assert 'matches' in data['data']
                assert len(data['data']['matches']) == 1
                assert data['data']['matches'][0]['partner_website']['domain'] == 'partner.com'
    
    def test_get_keyword_gaps(self, client, db_session, user, website, content_analysis, api_key):
        """Test keyword gap analysis"""
        headers = {'Authorization': f'Bearer {api_key.key_hash}'}
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            response = client.get(f'/api/websites/{website.id}/keyword-gaps', headers=headers)
            
            data = assert_response_success(response)
            assert 'keyword_gaps' in data['data']
            assert 'content_suggestions' in data['data']
            assert 'analysis_date' in data['data']


class TestAuthRoutes:
    """Test authentication API routes"""
    
    def test_health_check(self, client):
        """Test health check endpoint"""
        response = client.get('/health')
        
        data = assert_response_success(response)
        assert data['status'] == 'healthy'
    
    def test_api_info(self, client):
        """Test API info endpoint"""
        response = client.get('/api')
        
        data = assert_response_success(response)
        assert data['name'] == 'LinkUp Plugin API'
        assert 'endpoints' in data


class TestRateLimiting:
    """Test rate limiting functionality"""
    
    def test_rate_limit_enforcement(self, client, db_session, user, api_key):
        """Test that rate limits are enforced"""
        headers = {'Authorization': f'Bearer {api_key.key_hash}'}
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            with patch('app.services.auth_service.AuthenticationService.check_rate_limit') as mock_rate_limit:
                # First request should succeed
                mock_rate_limit.return_value = (True, 'within_limits')
                response = client.get('/api/websites', headers=headers)
                assert response.status_code == 200
                
                # Subsequent request should fail if rate limited
                mock_rate_limit.return_value = (False, 'hourly_limit_exceeded')
                response = client.get('/api/websites', headers=headers)
                # Note: Actual rate limiting implementation would return 429
                # This test verifies the rate limit check is called


class TestErrorHandling:
    """Test error handling in API routes"""
    
    def test_invalid_json(self, client, db_session, user, api_key):
        """Test handling of invalid JSON data"""
        headers = {
            'Authorization': f'Bearer {api_key.key_hash}',
            'Content-Type': 'application/json'
        }
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            response = client.post(
                '/api/websites',
                data='invalid json',
                headers=headers
            )
            
            assert_response_error(response, 400)
    
    def test_database_error_handling(self, client, db_session, user, api_key):
        """Test handling of database errors"""
        headers = {
            'Authorization': f'Bearer {api_key.key_hash}',
            'Content-Type': 'application/json'
        }
        
        website_data = {
            'domain': 'test.com',
            'title': 'Test Site'
        }
        
        with patch('app.services.auth_service.AuthenticationService.authenticate_request') as mock_auth:
            mock_auth.return_value = (user, 'api_key', api_key)
            
            with patch('app.db.session.commit') as mock_commit:
                mock_commit.side_effect = Exception('Database error')
                
                response = client.post(
                    '/api/websites',
                    data=json.dumps(website_data),
                    headers=headers
                )
                
                assert_response_error(response, 500)
