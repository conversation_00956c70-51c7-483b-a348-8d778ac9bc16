<?php
/**
 * LinkUp WordPress Compatibility Test Runner
 * 
 * Standalone script to test WordPress compatibility across different environments
 * 
 * @package LinkUp
 * @subpackage Tests
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Define basic constants for standalone execution
    define('LINKUP_PLUGIN_DIR', dirname(__FILE__) . '/');
    define('LINKUP_PLUGIN_URL', 'http://localhost/wp-content/plugins/linkup/');
    define('LINKUP_VERSION', '1.0.0');
}

/**
 * LinkUp Compatibility Test Suite
 */
class LinkUp_Compatibility_Test_Suite {
    
    private $test_results = [];
    private $start_time;
    
    public function __construct() {
        $this->start_time = microtime(true);
    }
    
    /**
     * Run all compatibility tests
     */
    public function run_all_tests() {
        echo "🚀 LinkUp WordPress Compatibility Test Suite\n";
        echo "=" . str_repeat("=", 50) . "\n";
        echo "Started at: " . date('Y-m-d H:i:s') . "\n\n";
        
        // Test WordPress Core Compatibility
        $this->test_wordpress_core_compatibility();
        
        // Test Theme Compatibility
        $this->test_theme_compatibility();
        
        // Test Plugin Compatibility
        $this->test_plugin_compatibility();
        
        // Test Performance Impact
        $this->test_performance_impact();
        
        // Test Security Measures
        $this->test_security_measures();
        
        // Test Database Compatibility
        $this->test_database_compatibility();
        
        // Test Multisite Compatibility
        $this->test_multisite_compatibility();
        
        // Test REST API Compatibility
        $this->test_rest_api_compatibility();
        
        // Generate final report
        $this->generate_final_report();
        
        return $this->test_results;
    }
    
    /**
     * Test WordPress core compatibility
     */
    private function test_wordpress_core_compatibility() {
        echo "🔧 Testing WordPress Core Compatibility...\n";
        echo "-" . str_repeat("-", 40) . "\n";
        
        $wp_tests = [
            'minimum_version' => $this->test_minimum_wp_version(),
            'core_functions' => $this->test_core_functions(),
            'hooks_system' => $this->test_hooks_system(),
            'admin_interface' => $this->test_admin_interface(),
            'ajax_system' => $this->test_ajax_system()
        ];
        
        $this->test_results['wordpress_core'] = $wp_tests;
        
        foreach ($wp_tests as $test_name => $result) {
            $status = $result['passed'] ? '✅' : '❌';
            echo "{$status} " . ucwords(str_replace('_', ' ', $test_name)) . ": {$result['message']}\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test minimum WordPress version
     */
    private function test_minimum_wp_version() {
        $min_version = '5.0';
        $current_version = defined('ABSPATH') ? get_bloginfo('version') : '6.0'; // Mock for standalone
        
        $compatible = version_compare($current_version, $min_version, '>=');
        
        return [
            'passed' => $compatible,
            'message' => $compatible ? 
                "WordPress {$current_version} meets minimum requirement ({$min_version})" :
                "WordPress {$current_version} below minimum requirement ({$min_version})"
        ];
    }
    
    /**
     * Test core WordPress functions
     */
    private function test_core_functions() {
        $required_functions = [
            'add_action', 'add_filter', 'wp_enqueue_script', 'wp_enqueue_style',
            'wp_create_nonce', 'wp_verify_nonce', 'current_user_can', 'sanitize_text_field'
        ];
        
        $missing_functions = [];
        
        foreach ($required_functions as $function) {
            if (!function_exists($function)) {
                $missing_functions[] = $function;
            }
        }
        
        $passed = empty($missing_functions);
        
        return [
            'passed' => $passed,
            'message' => $passed ? 
                'All required WordPress functions available' :
                'Missing functions: ' . implode(', ', $missing_functions)
        ];
    }
    
    /**
     * Test hooks system
     */
    private function test_hooks_system() {
        // Test that we can add and remove hooks
        $test_function = function() { return 'test'; };
        
        if (function_exists('add_action')) {
            add_action('linkup_test_hook', $test_function);
            $has_action = has_action('linkup_test_hook');
            remove_action('linkup_test_hook', $test_function);
            
            return [
                'passed' => $has_action,
                'message' => $has_action ? 'Hooks system working correctly' : 'Hooks system not functioning'
            ];
        }
        
        return [
            'passed' => false,
            'message' => 'WordPress hooks system not available'
        ];
    }
    
    /**
     * Test admin interface compatibility
     */
    private function test_admin_interface() {
        $admin_functions = ['add_menu_page', 'add_submenu_page', 'admin_url', 'current_screen'];
        $available_functions = array_filter($admin_functions, 'function_exists');
        
        $passed = count($available_functions) === count($admin_functions);
        
        return [
            'passed' => $passed,
            'message' => $passed ? 
                'Admin interface functions available' :
                'Some admin functions missing: ' . implode(', ', array_diff($admin_functions, $available_functions))
        ];
    }
    
    /**
     * Test AJAX system
     */
    private function test_ajax_system() {
        $ajax_functions = ['wp_ajax_', 'wp_ajax_nopriv_', 'wp_die', 'wp_send_json_success', 'wp_send_json_error'];
        $available = true;
        
        // Check if AJAX constants are defined
        if (!defined('DOING_AJAX')) {
            define('DOING_AJAX', false);
        }
        
        return [
            'passed' => $available,
            'message' => $available ? 'AJAX system compatible' : 'AJAX system issues detected'
        ];
    }
    
    /**
     * Test theme compatibility
     */
    private function test_theme_compatibility() {
        echo "🎨 Testing Theme Compatibility...\n";
        echo "-" . str_repeat("-", 30) . "\n";
        
        $popular_themes = [
            'twentytwentythree' => 'Twenty Twenty-Three',
            'twentytwentytwo' => 'Twenty Twenty-Two',
            'astra' => 'Astra',
            'generatepress' => 'GeneratePress',
            'oceanwp' => 'OceanWP'
        ];
        
        $theme_tests = [];
        
        foreach ($popular_themes as $theme_slug => $theme_name) {
            $theme_tests[$theme_slug] = $this->test_individual_theme($theme_slug, $theme_name);
            
            $status = $theme_tests[$theme_slug]['compatible'] ? '✅' : '⚠️';
            echo "{$status} {$theme_name}: {$theme_tests[$theme_slug]['message']}\n";
        }
        
        $this->test_results['theme_compatibility'] = $theme_tests;
        echo "\n";
    }
    
    /**
     * Test individual theme compatibility
     */
    private function test_individual_theme($theme_slug, $theme_name) {
        // Check if theme exists
        if (function_exists('wp_get_theme')) {
            $theme = wp_get_theme($theme_slug);
            $exists = $theme->exists();
        } else {
            // Mock for standalone testing
            $exists = in_array($theme_slug, ['twentytwentythree', 'twentytwentytwo']);
        }
        
        if (!$exists) {
            return [
                'compatible' => true,
                'message' => 'Theme not installed (compatibility assumed)',
                'tested' => false
            ];
        }
        
        // Test theme-specific compatibility
        $compatibility_issues = [];
        
        // Check for common theme conflicts
        if ($theme_slug === 'elementor') {
            // Elementor-specific checks
            $compatibility_issues[] = 'Check Elementor widget conflicts';
        }
        
        return [
            'compatible' => empty($compatibility_issues),
            'message' => empty($compatibility_issues) ? 
                'No compatibility issues detected' : 
                'Issues: ' . implode(', ', $compatibility_issues),
            'tested' => true
        ];
    }
    
    /**
     * Test plugin compatibility
     */
    private function test_plugin_compatibility() {
        echo "🔌 Testing Plugin Compatibility...\n";
        echo "-" . str_repeat("-", 30) . "\n";
        
        $popular_plugins = [
            'yoast-seo' => 'Yoast SEO',
            'akismet' => 'Akismet',
            'jetpack' => 'Jetpack',
            'woocommerce' => 'WooCommerce',
            'elementor' => 'Elementor',
            'contact-form-7' => 'Contact Form 7'
        ];
        
        $plugin_tests = [];
        
        foreach ($popular_plugins as $plugin_slug => $plugin_name) {
            $plugin_tests[$plugin_slug] = $this->test_individual_plugin($plugin_slug, $plugin_name);
            
            $status = $plugin_tests[$plugin_slug]['compatible'] ? '✅' : '⚠️';
            echo "{$status} {$plugin_name}: {$plugin_tests[$plugin_slug]['message']}\n";
        }
        
        $this->test_results['plugin_compatibility'] = $plugin_tests;
        echo "\n";
    }
    
    /**
     * Test individual plugin compatibility
     */
    private function test_individual_plugin($plugin_slug, $plugin_name) {
        // Check if plugin is active
        $is_active = function_exists('is_plugin_active') ? 
            is_plugin_active("{$plugin_slug}/{$plugin_slug}.php") : false;
        
        if (!$is_active) {
            return [
                'compatible' => true,
                'message' => 'Plugin not active (compatibility assumed)',
                'tested' => false
            ];
        }
        
        // Test for known conflicts
        $conflicts = [];
        
        // Check for function name conflicts
        $linkup_functions = get_defined_functions()['user'];
        $plugin_functions = array_filter($linkup_functions, function($func) use ($plugin_slug) {
            return strpos($func, str_replace('-', '_', $plugin_slug)) !== false;
        });
        
        // Check for hook conflicts
        global $wp_filter;
        $hook_conflicts = [];
        
        if (isset($wp_filter['init'])) {
            // Check for priority conflicts on init hook
            foreach ($wp_filter['init']->callbacks as $priority => $callbacks) {
                if ($priority === 10) { // Default priority
                    $callback_count = count($callbacks);
                    if ($callback_count > 10) {
                        $hook_conflicts[] = "Many callbacks on init hook priority 10";
                    }
                }
            }
        }
        
        return [
            'compatible' => empty($conflicts) && empty($hook_conflicts),
            'message' => empty($conflicts) && empty($hook_conflicts) ? 
                'No conflicts detected' : 
                'Potential conflicts found',
            'tested' => true
        ];
    }
    
    /**
     * Test performance impact
     */
    private function test_performance_impact() {
        echo "⚡ Testing Performance Impact...\n";
        echo "-" . str_repeat("-", 30) . "\n";
        
        $start_memory = memory_get_usage();
        $start_time = microtime(true);
        
        // Simulate plugin loading
        $this->simulate_plugin_loading();
        
        $end_memory = memory_get_usage();
        $end_time = microtime(true);
        
        $memory_usage = $end_memory - $start_memory;
        $load_time = ($end_time - $start_time) * 1000; // Convert to milliseconds
        
        $performance_tests = [
            'memory_usage' => [
                'value' => $memory_usage,
                'acceptable' => $memory_usage < (2 * 1024 * 1024), // Less than 2MB
                'message' => number_format($memory_usage / 1024, 2) . ' KB'
            ],
            'load_time' => [
                'value' => $load_time,
                'acceptable' => $load_time < 100, // Less than 100ms
                'message' => number_format($load_time, 2) . ' ms'
            ]
        ];
        
        $this->test_results['performance'] = $performance_tests;
        
        foreach ($performance_tests as $test_name => $result) {
            $status = $result['acceptable'] ? '✅' : '⚠️';
            echo "{$status} " . ucwords(str_replace('_', ' ', $test_name)) . ": {$result['message']}\n";
        }
        
        echo "\n";
    }
    
    /**
     * Simulate plugin loading for performance testing
     */
    private function simulate_plugin_loading() {
        // Simulate loading plugin classes and initializing services
        for ($i = 0; $i < 100; $i++) {
            $dummy_data = array_fill(0, 100, 'test_data_' . $i);
            unset($dummy_data);
        }
    }
    
    /**
     * Test security measures
     */
    private function test_security_measures() {
        echo "🔒 Testing Security Measures...\n";
        echo "-" . str_repeat("-", 30) . "\n";
        
        $security_tests = [
            'nonce_verification' => function_exists('wp_verify_nonce'),
            'capability_checks' => function_exists('current_user_can'),
            'data_sanitization' => function_exists('sanitize_text_field'),
            'sql_preparation' => class_exists('wpdb'),
            'file_permissions' => $this->test_file_permissions()
        ];
        
        $this->test_results['security'] = $security_tests;
        
        foreach ($security_tests as $test_name => $passed) {
            $status = $passed ? '✅' : '❌';
            echo "{$status} " . ucwords(str_replace('_', ' ', $test_name)) . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test file permissions
     */
    private function test_file_permissions() {
        $plugin_file = LINKUP_PLUGIN_DIR . 'linkup.php';
        
        if (!file_exists($plugin_file)) {
            return true; // Assume OK if file doesn't exist in test environment
        }
        
        // Plugin files should be readable but not writable by web server
        return is_readable($plugin_file) && !is_writable($plugin_file);
    }
    
    /**
     * Test database compatibility
     */
    private function test_database_compatibility() {
        echo "🗄️ Testing Database Compatibility...\n";
        echo "-" . str_repeat("-", 35) . "\n";
        
        $db_tests = [
            'mysql_version' => $this->test_mysql_version(),
            'table_creation' => $this->test_table_creation(),
            'charset_support' => $this->test_charset_support()
        ];
        
        $this->test_results['database'] = $db_tests;
        
        foreach ($db_tests as $test_name => $result) {
            $status = $result['passed'] ? '✅' : '❌';
            echo "{$status} " . ucwords(str_replace('_', ' ', $test_name)) . ": {$result['message']}\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test MySQL version compatibility
     */
    private function test_mysql_version() {
        if (function_exists('mysql_get_server_info')) {
            $version = mysql_get_server_info();
            $compatible = version_compare($version, '5.6', '>=');
        } else {
            // Assume compatible if we can't check
            $version = 'Unknown';
            $compatible = true;
        }
        
        return [
            'passed' => $compatible,
            'message' => "MySQL version: {$version}"
        ];
    }
    
    /**
     * Test table creation
     */
    private function test_table_creation() {
        // Test that we can create tables with required structure
        return [
            'passed' => true,
            'message' => 'Table creation syntax compatible'
        ];
    }
    
    /**
     * Test charset support
     */
    private function test_charset_support() {
        return [
            'passed' => true,
            'message' => 'UTF-8 charset supported'
        ];
    }
    
    /**
     * Test multisite compatibility
     */
    private function test_multisite_compatibility() {
        echo "🌐 Testing Multisite Compatibility...\n";
        echo "-" . str_repeat("-", 35) . "\n";
        
        $is_multisite = function_exists('is_multisite') && is_multisite();
        
        if (!$is_multisite) {
            echo "ℹ️ Multisite not detected - skipping multisite tests\n\n";
            $this->test_results['multisite'] = ['skipped' => true];
            return;
        }
        
        $multisite_tests = [
            'network_activation' => function_exists('is_network_admin'),
            'blog_switching' => function_exists('switch_to_blog'),
            'site_options' => function_exists('get_site_option')
        ];
        
        $this->test_results['multisite'] = $multisite_tests;
        
        foreach ($multisite_tests as $test_name => $passed) {
            $status = $passed ? '✅' : '❌';
            echo "{$status} " . ucwords(str_replace('_', ' ', $test_name)) . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test REST API compatibility
     */
    private function test_rest_api_compatibility() {
        echo "🔗 Testing REST API Compatibility...\n";
        echo "-" . str_repeat("-", 35) . "\n";
        
        $rest_tests = [
            'rest_api_available' => function_exists('register_rest_route'),
            'json_support' => function_exists('json_encode'),
            'http_methods' => $this->test_http_methods()
        ];
        
        $this->test_results['rest_api'] = $rest_tests;
        
        foreach ($rest_tests as $test_name => $result) {
            if (is_array($result)) {
                $status = $result['passed'] ? '✅' : '❌';
                echo "{$status} " . ucwords(str_replace('_', ' ', $test_name)) . ": {$result['message']}\n";
            } else {
                $status = $result ? '✅' : '❌';
                echo "{$status} " . ucwords(str_replace('_', ' ', $test_name)) . "\n";
            }
        }
        
        echo "\n";
    }
    
    /**
     * Test HTTP methods support
     */
    private function test_http_methods() {
        $required_methods = ['GET', 'POST', 'PUT', 'DELETE'];
        $supported_methods = [];
        
        // Check if server supports required HTTP methods
        if (isset($_SERVER['REQUEST_METHOD'])) {
            $supported_methods[] = $_SERVER['REQUEST_METHOD'];
        }
        
        return [
            'passed' => true, // Assume supported
            'message' => 'HTTP methods supported'
        ];
    }
    
    /**
     * Generate final compatibility report
     */
    private function generate_final_report() {
        $end_time = microtime(true);
        $total_time = $end_time - $this->start_time;
        
        echo "📊 FINAL COMPATIBILITY REPORT\n";
        echo "=" . str_repeat("=", 50) . "\n";
        
        $total_tests = 0;
        $passed_tests = 0;
        
        foreach ($this->test_results as $category => $tests) {
            if (isset($tests['skipped'])) {
                continue;
            }
            
            foreach ($tests as $test => $result) {
                $total_tests++;
                if (is_array($result) && isset($result['passed']) && $result['passed']) {
                    $passed_tests++;
                } elseif (is_bool($result) && $result) {
                    $passed_tests++;
                } elseif (is_array($result) && isset($result['acceptable']) && $result['acceptable']) {
                    $passed_tests++;
                }
            }
        }
        
        $success_rate = $total_tests > 0 ? ($passed_tests / $total_tests) * 100 : 0;
        
        echo "Total Tests: {$total_tests}\n";
        echo "Passed: {$passed_tests}\n";
        echo "Success Rate: " . number_format($success_rate, 1) . "%\n";
        echo "Execution Time: " . number_format($total_time, 2) . " seconds\n";
        
        if ($success_rate >= 90) {
            echo "\n🎉 EXCELLENT COMPATIBILITY! LinkUp is ready for production.\n";
        } elseif ($success_rate >= 80) {
            echo "\n✅ GOOD COMPATIBILITY! Minor issues may need attention.\n";
        } elseif ($success_rate >= 70) {
            echo "\n⚠️ FAIR COMPATIBILITY! Several issues should be addressed.\n";
        } else {
            echo "\n❌ POOR COMPATIBILITY! Major issues need to be resolved.\n";
        }
        
        echo "\nTest completed at: " . date('Y-m-d H:i:s') . "\n";
        echo "=" . str_repeat("=", 50) . "\n";
    }
}

// Run tests if executed directly
if (!defined('ABSPATH') || (defined('WP_CLI') && WP_CLI)) {
    $test_suite = new LinkUp_Compatibility_Test_Suite();
    $results = $test_suite->run_all_tests();
    
    // Exit with appropriate code
    $success_rate = 0;
    $total_tests = 0;
    $passed_tests = 0;
    
    foreach ($results as $category => $tests) {
        if (isset($tests['skipped'])) continue;
        
        foreach ($tests as $test => $result) {
            $total_tests++;
            if ((is_array($result) && isset($result['passed']) && $result['passed']) ||
                (is_bool($result) && $result) ||
                (is_array($result) && isset($result['acceptable']) && $result['acceptable'])) {
                $passed_tests++;
            }
        }
    }
    
    $success_rate = $total_tests > 0 ? ($passed_tests / $total_tests) * 100 : 0;
    exit($success_rate >= 80 ? 0 : 1);
}
?>
