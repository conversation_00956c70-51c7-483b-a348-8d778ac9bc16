/**
 * Content Suggestions Admin JavaScript
 * 
 * Handles the frontend functionality for the content suggestions admin interface
 * 
 * @package LinkUp
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    var ContentSuggestions = {
        
        /**
         * Initialize the content suggestions interface
         */
        init: function() {
            this.bindEvents();
            this.loadSuggestions();
        },
        
        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Suggestions page events
            $('#refresh-suggestions').on('click', this.loadSuggestions.bind(this));
            $('#generate-suggestions').on('click', this.generateSuggestions.bind(this));
            $('#suggestion-type-filter, #priority-filter').on('change', this.filterSuggestions.bind(this));
            
            // Modal events
            $(document).on('click', '.suggestion-card', this.openSuggestionModal.bind(this));
            $(document).on('click', '.linkup-modal-close', this.closeModal.bind(this));
            $(document).on('click', '#modal-dismiss', this.dismissSuggestion.bind(this));
            $(document).on('click', '#modal-implement', this.implementSuggestion.bind(this));
            
            // Trending topics events
            $('#refresh-trending').on('click', this.loadTrendingTopics.bind(this));
            $('#niche-filter, #timeframe-filter').on('change', this.loadTrendingTopics.bind(this));
            
            // Content optimizer events
            $('#content-analysis-form').on('submit', this.analyzeContent.bind(this));
            
            // Close modal on outside click
            $(document).on('click', '.linkup-modal', function(e) {
                if (e.target === this) {
                    ContentSuggestions.closeModal();
                }
            });
        },
        
        /**
         * Load content suggestions
         */
        loadSuggestions: function() {
            var $container = $('#suggestions-container');
            var $loading = $('#suggestions-loading');
            var $noSuggestions = $('#no-suggestions');
            
            $loading.show();
            $container.empty();
            $noSuggestions.hide();
            
            var data = {
                action: 'linkup_get_suggestions',
                nonce: linkupContentSuggestions.nonce,
                type_filter: $('#suggestion-type-filter').val(),
                priority_filter: $('#priority-filter').val()
            };
            
            $.post(linkupContentSuggestions.ajaxUrl, data)
                .done(function(response) {
                    $loading.hide();
                    
                    if (response.success && response.data.suggestions && response.data.suggestions.length > 0) {
                        ContentSuggestions.renderSuggestions(response.data.suggestions);
                        ContentSuggestions.updateStats(response.data);
                    } else {
                        $noSuggestions.show();
                    }
                })
                .fail(function() {
                    $loading.hide();
                    ContentSuggestions.showNotice(linkupContentSuggestions.strings.error, 'error');
                });
        },
        
        /**
         * Render suggestions in the grid
         */
        renderSuggestions: function(suggestions) {
            var $container = $('#suggestions-container');
            
            suggestions.forEach(function(suggestion) {
                var $card = ContentSuggestions.createSuggestionCard(suggestion);
                $container.append($card);
            });
        },
        
        /**
         * Create a suggestion card element
         */
        createSuggestionCard: function(suggestion) {
            var priorityClass = 'priority-' + suggestion.priority;
            var typeClass = 'type-' + suggestion.suggestion_type.replace('_', '-');
            
            var $card = $('<div>')
                .addClass('suggestion-card ' + priorityClass + ' ' + typeClass)
                .attr('data-suggestion-id', suggestion.suggestion_id)
                .html(
                    '<div class="suggestion-header">' +
                        '<h3 class="suggestion-title">' + this.escapeHtml(suggestion.title) + '</h3>' +
                        '<span class="priority-badge priority-' + suggestion.priority + '">' + 
                            suggestion.priority.toUpperCase() + 
                        '</span>' +
                    '</div>' +
                    '<div class="suggestion-content">' +
                        '<p class="suggestion-description">' + this.escapeHtml(suggestion.description) + '</p>' +
                        '<div class="suggestion-keywords">' +
                            suggestion.target_keywords.slice(0, 3).map(function(keyword) {
                                return '<span class="keyword-tag">' + ContentSuggestions.escapeHtml(keyword) + '</span>';
                            }).join('') +
                        '</div>' +
                    '</div>' +
                    '<div class="suggestion-metrics">' +
                        '<div class="metric">' +
                            '<span class="metric-label">Opportunity</span>' +
                            '<span class="metric-value">' + Math.round(suggestion.opportunity_score) + '%</span>' +
                        '</div>' +
                        '<div class="metric">' +
                            '<span class="metric-label">Traffic</span>' +
                            '<span class="metric-value">' + suggestion.estimated_traffic + '</span>' +
                        '</div>' +
                        '<div class="metric">' +
                            '<span class="metric-label">Effort</span>' +
                            '<span class="metric-value">' + suggestion.estimated_effort_hours + 'h</span>' +
                        '</div>' +
                    '</div>' +
                    '<div class="suggestion-actions">' +
                        '<button class="button button-small view-details">View Details</button>' +
                    '</div>'
                );
            
            return $card;
        },
        
        /**
         * Open suggestion modal
         */
        openSuggestionModal: function(e) {
            var $card = $(e.currentTarget);
            var suggestionId = $card.data('suggestion-id');
            
            // Find suggestion data (in a real implementation, this would fetch from API)
            var suggestion = this.findSuggestionById(suggestionId);
            
            if (suggestion) {
                this.populateModal(suggestion);
                $('#suggestion-modal').show();
            }
        },
        
        /**
         * Populate modal with suggestion data
         */
        populateModal: function(suggestion) {
            $('#modal-title').text(suggestion.title);
            $('#modal-description').text(suggestion.description);
            $('#modal-rationale').text(suggestion.rationale);
            
            // Keywords
            var $keywords = $('#modal-keywords');
            $keywords.empty();
            suggestion.target_keywords.forEach(function(keyword) {
                $keywords.append('<span class="keyword-tag">' + ContentSuggestions.escapeHtml(keyword) + '</span>');
            });
            
            // Content outline
            var $outline = $('#modal-outline');
            $outline.empty();
            suggestion.content_outline.forEach(function(item) {
                $outline.append('<li>' + ContentSuggestions.escapeHtml(item) + '</li>');
            });
            
            // SEO recommendations
            var $seoRecs = $('#modal-seo-recommendations');
            $seoRecs.empty();
            suggestion.seo_recommendations.forEach(function(rec) {
                $seoRecs.append('<li>' + ContentSuggestions.escapeHtml(rec) + '</li>');
            });
            
            // Action steps
            var $actionSteps = $('#modal-action-steps');
            $actionSteps.empty();
            suggestion.action_steps.forEach(function(step) {
                $actionSteps.append('<li>' + ContentSuggestions.escapeHtml(step) + '</li>');
            });
            
            // Metrics
            $('#modal-opportunity-score').text(Math.round(suggestion.opportunity_score) + '%');
            $('#modal-difficulty').text(Math.round(suggestion.difficulty_score) + '%');
            $('#modal-traffic').text(suggestion.estimated_traffic);
            $('#modal-roi').text(suggestion.roi_estimate + '%');
            
            // Store suggestion ID for actions
            $('#modal-dismiss, #modal-implement').data('suggestion-id', suggestion.suggestion_id);
        },
        
        /**
         * Close modal
         */
        closeModal: function() {
            $('.linkup-modal').hide();
        },
        
        /**
         * Dismiss suggestion
         */
        dismissSuggestion: function(e) {
            if (!confirm(linkupContentSuggestions.strings.confirm_dismiss)) {
                return;
            }
            
            var suggestionId = $(e.target).data('suggestion-id');
            
            var data = {
                action: 'linkup_dismiss_suggestion',
                nonce: linkupContentSuggestions.nonce,
                suggestion_id: suggestionId
            };
            
            $.post(linkupContentSuggestions.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        ContentSuggestions.closeModal();
                        ContentSuggestions.removeSuggestionCard(suggestionId);
                        ContentSuggestions.showNotice(response.data.message, 'success');
                    } else {
                        ContentSuggestions.showNotice(response.data.message, 'error');
                    }
                })
                .fail(function() {
                    ContentSuggestions.showNotice(linkupContentSuggestions.strings.error, 'error');
                });
        },
        
        /**
         * Implement suggestion
         */
        implementSuggestion: function(e) {
            if (!confirm(linkupContentSuggestions.strings.confirm_implement)) {
                return;
            }
            
            var suggestionId = $(e.target).data('suggestion-id');
            
            var data = {
                action: 'linkup_implement_suggestion',
                nonce: linkupContentSuggestions.nonce,
                suggestion_id: suggestionId,
                action_type: 'draft'
            };
            
            $.post(linkupContentSuggestions.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        ContentSuggestions.closeModal();
                        ContentSuggestions.removeSuggestionCard(suggestionId);
                        ContentSuggestions.showNotice(
                            response.data.message + ' <a href="' + response.data.edit_url + '">Edit post</a>',
                            'success'
                        );
                    } else {
                        ContentSuggestions.showNotice(response.data.message, 'error');
                    }
                })
                .fail(function() {
                    ContentSuggestions.showNotice(linkupContentSuggestions.strings.error, 'error');
                });
        },
        
        /**
         * Load trending topics
         */
        loadTrendingTopics: function() {
            var $container = $('#trending-container');
            var $loading = $('#trending-loading');
            
            $loading.show();
            
            var data = {
                action: 'linkup_get_trending_topics',
                nonce: linkupContentSuggestions.nonce,
                niche: $('#niche-filter').val(),
                timeframe: $('#timeframe-filter').val()
            };
            
            $.post(linkupContentSuggestions.ajaxUrl, data)
                .done(function(response) {
                    $loading.hide();
                    
                    if (response.success) {
                        ContentSuggestions.renderTrendingTopics(response.data);
                    } else {
                        ContentSuggestions.showNotice(response.data.message, 'error');
                    }
                })
                .fail(function() {
                    $loading.hide();
                    ContentSuggestions.showNotice(linkupContentSuggestions.strings.error, 'error');
                });
        },
        
        /**
         * Render trending topics
         */
        renderTrendingTopics: function(data) {
            // Render rising topics
            var $risingTopics = $('#rising-topics');
            $risingTopics.empty();
            
            if (data.trending_topics) {
                data.trending_topics.slice(0, 10).forEach(function(topic) {
                    var $topicItem = $('<div class="topic-item">')
                        .html(
                            '<h4>' + ContentSuggestions.escapeHtml(topic.topic) + '</h4>' +
                            '<div class="topic-metrics">' +
                                '<span class="trend-score">Score: ' + Math.round(topic.trend_score) + '</span>' +
                                '<span class="trend-direction ' + topic.trend_direction + '">' + 
                                    topic.trend_direction.charAt(0).toUpperCase() + topic.trend_direction.slice(1) + 
                                '</span>' +
                            '</div>' +
                            '<p>' + ContentSuggestions.escapeHtml(topic.content_opportunities[0] || '') + '</p>'
                        );
                    $risingTopics.append($topicItem);
                });
            }
            
            // Render emerging keywords
            var $emergingKeywords = $('#emerging-keywords');
            $emergingKeywords.empty();
            
            if (data.emerging_keywords) {
                data.emerging_keywords.forEach(function(keyword) {
                    var $keywordTag = $('<span class="trending-keyword">')
                        .text(keyword);
                    $emergingKeywords.append($keywordTag);
                });
            }
            
            // Render content opportunities
            var $opportunities = $('#content-opportunities');
            $opportunities.empty();
            
            if (data.content_gap_opportunities) {
                data.content_gap_opportunities.slice(0, 5).forEach(function(opportunity) {
                    var $opportunityItem = $('<div class="opportunity-item">')
                        .html('<p>' + ContentSuggestions.escapeHtml(opportunity) + '</p>');
                    $opportunities.append($opportunityItem);
                });
            }
            
            // Render insights
            var $insights = $('#trend-insights');
            $insights.empty();
            
            if (data.recommended_actions) {
                data.recommended_actions.forEach(function(action) {
                    var $insightItem = $('<div class="insight-item">')
                        .html('<p>' + ContentSuggestions.escapeHtml(action) + '</p>');
                    $insights.append($insightItem);
                });
            }
        },
        
        /**
         * Analyze content
         */
        analyzeContent: function(e) {
            e.preventDefault();
            
            var $form = $(e.target);
            var $results = $('#analysis-results');
            
            var data = {
                action: 'linkup_analyze_content',
                nonce: linkupContentSuggestions.nonce,
                content_url: $('#content-url').val(),
                target_keywords: $('#target-keywords').val()
            };
            
            $form.find('button[type="submit"]').prop('disabled', true).text('Analyzing...');
            
            $.post(linkupContentSuggestions.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        ContentSuggestions.renderAnalysisResults(response.data);
                        $results.show();
                    } else {
                        ContentSuggestions.showNotice(response.data.message, 'error');
                    }
                })
                .fail(function() {
                    ContentSuggestions.showNotice(linkupContentSuggestions.strings.error, 'error');
                })
                .always(function() {
                    $form.find('button[type="submit"]').prop('disabled', false).text('Analyze Content');
                });
        },
        
        /**
         * Render analysis results
         */
        renderAnalysisResults: function(data) {
            // Update score circles
            this.updateScoreCircle('#overall-score', data.overall_score);
            this.updateScoreCircle('#seo-score', data.seo_score);
            this.updateScoreCircle('#readability-score', data.readability_score);
            this.updateScoreCircle('#structure-score', data.structure_score);
            
            // Render recommendations
            var $recommendations = $('#recommendations-list');
            $recommendations.empty();
            
            if (data.recommendations) {
                data.recommendations.forEach(function(rec) {
                    var $recItem = $('<div class="recommendation-item priority-' + rec.priority + '">')
                        .html(
                            '<h4>' + ContentSuggestions.escapeHtml(rec.title) + '</h4>' +
                            '<p>' + ContentSuggestions.escapeHtml(rec.description) + '</p>' +
                            '<div class="recommendation-action">' +
                                '<strong>Action:</strong> ' + ContentSuggestions.escapeHtml(rec.recommended_action) +
                            '</div>' +
                            '<div class="recommendation-impact">' +
                                '<strong>Expected Impact:</strong> ' + ContentSuggestions.escapeHtml(rec.expected_impact) +
                            '</div>'
                        );
                    $recommendations.append($recItem);
                });
            }
        },
        
        /**
         * Update score circle
         */
        updateScoreCircle: function(selector, score) {
            var $circle = $(selector);
            var $number = $circle.find('.score-number');
            
            $number.text(Math.round(score));
            
            // Add color class based on score
            $circle.removeClass('score-low score-medium score-high');
            if (score >= 80) {
                $circle.addClass('score-high');
            } else if (score >= 60) {
                $circle.addClass('score-medium');
            } else {
                $circle.addClass('score-low');
            }
        },
        
        /**
         * Helper functions
         */
        escapeHtml: function(text) {
            var div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },
        
        findSuggestionById: function(id) {
            // In a real implementation, this would maintain a cache of suggestions
            // For now, return mock data
            return {
                suggestion_id: id,
                title: 'Sample Suggestion',
                description: 'This is a sample suggestion description.',
                rationale: 'This suggestion is based on competitor analysis.',
                target_keywords: ['keyword1', 'keyword2', 'keyword3'],
                content_outline: ['Introduction', 'Main Content', 'Conclusion'],
                seo_recommendations: ['Optimize title', 'Add meta description', 'Include internal links'],
                action_steps: ['Research topic', 'Create outline', 'Write content', 'Optimize for SEO'],
                opportunity_score: 85,
                difficulty_score: 45,
                estimated_traffic: 1500,
                roi_estimate: 150
            };
        },
        
        removeSuggestionCard: function(suggestionId) {
            $('.suggestion-card[data-suggestion-id="' + suggestionId + '"]').fadeOut(300, function() {
                $(this).remove();
            });
        },
        
        updateStats: function(data) {
            $('#total-suggestions').text(data.total_suggestions || 0);
            
            var highPriority = 0;
            var totalTraffic = 0;
            
            if (data.suggestions) {
                data.suggestions.forEach(function(suggestion) {
                    if (suggestion.priority === 'high' || suggestion.priority === 'critical') {
                        highPriority++;
                    }
                    totalTraffic += suggestion.estimated_traffic || 0;
                });
            }
            
            $('#high-priority').text(highPriority);
            $('#estimated-traffic').text(totalTraffic.toLocaleString());
        },
        
        filterSuggestions: function() {
            // In a real implementation, this would filter the displayed suggestions
            // For now, just reload
            this.loadSuggestions();
        },
        
        generateSuggestions: function() {
            // Trigger suggestion generation
            this.showNotice('Generating new suggestions...', 'info');
            setTimeout(function() {
                ContentSuggestions.loadSuggestions();
            }, 2000);
        },
        
        showNotice: function(message, type) {
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible">')
                .html('<p>' + message + '</p>');
            
            $('.wrap').first().prepend($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut();
            }, 5000);
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        ContentSuggestions.init();
    });
    
})(jQuery);
