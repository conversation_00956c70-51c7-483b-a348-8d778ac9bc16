"""
Keyword Research API Integration Service
Integrates with external keyword research APIs for enhanced data
"""
import logging
import requests
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json

from app import db, cache
from app.models.website import Website

logger = logging.getLogger(__name__)


class APIProvider(Enum):
    """Supported API providers"""
    GOOGLE_KEYWORD_PLANNER = "google_keyword_planner"
    SEMRUSH = "semrush"
    AHREFS = "ahrefs"
    UBERSUGGEST = "ubersuggest"
    SERPAPI = "serpapi"
    KEYWORDS_EVERYWHERE = "keywords_everywhere"
    MOCK_API = "mock_api"  # For testing


@dataclass
class KeywordData:
    """Keyword research data structure"""
    keyword: str
    search_volume: int
    keyword_difficulty: float
    cpc: float
    competition: str
    trend_data: List[int]
    related_keywords: List[str]
    questions: List[str]
    long_tail_variations: List[str]
    search_intent: str
    seasonal_trends: Dict[str, float]
    serp_features: List[str]
    api_provider: str
    last_updated: datetime


@dataclass
class CompetitorKeywords:
    """Competitor keyword analysis data"""
    domain: str
    organic_keywords: List[KeywordData]
    paid_keywords: List[KeywordData]
    keyword_gaps: List[str]
    content_gaps: List[str]
    ranking_opportunities: List[Dict]


class KeywordResearchAPIService:
    """Service for integrating with keyword research APIs"""
    
    def __init__(self):
        """Initialize the keyword research API service"""
        self.cache_timeout = 3600 * 24 * 7  # 7 days for keyword data
        self.rate_limits = {
            APIProvider.GOOGLE_KEYWORD_PLANNER: {'requests_per_minute': 60, 'daily_limit': 10000},
            APIProvider.SEMRUSH: {'requests_per_minute': 30, 'daily_limit': 5000},
            APIProvider.AHREFS: {'requests_per_minute': 20, 'daily_limit': 3000},
            APIProvider.UBERSUGGEST: {'requests_per_minute': 50, 'daily_limit': 8000},
            APIProvider.SERPAPI: {'requests_per_minute': 100, 'daily_limit': 15000},
            APIProvider.KEYWORDS_EVERYWHERE: {'requests_per_minute': 40, 'daily_limit': 6000},
            APIProvider.MOCK_API: {'requests_per_minute': 1000, 'daily_limit': 100000}
        }
        
        # API configurations (would be loaded from environment variables)
        self.api_configs = {
            APIProvider.GOOGLE_KEYWORD_PLANNER: {
                'base_url': 'https://googleads.googleapis.com/v13/customers',
                'auth_type': 'oauth2',
                'api_key': None  # Set from environment
            },
            APIProvider.SEMRUSH: {
                'base_url': 'https://api.semrush.com',
                'auth_type': 'api_key',
                'api_key': None  # Set from environment
            },
            APIProvider.AHREFS: {
                'base_url': 'https://apiv2.ahrefs.com',
                'auth_type': 'api_key',
                'api_key': None  # Set from environment
            },
            APIProvider.SERPAPI: {
                'base_url': 'https://serpapi.com/search',
                'auth_type': 'api_key',
                'api_key': None  # Set from environment
            },
            APIProvider.MOCK_API: {
                'base_url': 'http://localhost:8000/mock',
                'auth_type': 'none',
                'api_key': None
            }
        }
        
        # Initialize rate limiting tracking
        self.request_counts = {}
    
    def get_keyword_data(self, keyword: str, provider: APIProvider = APIProvider.MOCK_API, 
                        location: str = 'US', language: str = 'en') -> Optional[KeywordData]:
        """Get comprehensive keyword data from specified provider"""
        try:
            # Check cache first
            cache_key = f"keyword_data_{provider.value}_{keyword}_{location}_{language}"
            cached_data = cache.get(cache_key)
            if cached_data:
                return KeywordData(**cached_data)
            
            # Check rate limits
            if not self._check_rate_limit(provider):
                logger.warning(f"Rate limit exceeded for {provider.value}")
                return None
            
            # Get data from API
            keyword_data = None
            
            if provider == APIProvider.MOCK_API:
                keyword_data = self._get_mock_keyword_data(keyword, location, language)
            elif provider == APIProvider.SEMRUSH:
                keyword_data = self._get_semrush_keyword_data(keyword, location, language)
            elif provider == APIProvider.AHREFS:
                keyword_data = self._get_ahrefs_keyword_data(keyword, location, language)
            elif provider == APIProvider.SERPAPI:
                keyword_data = self._get_serpapi_keyword_data(keyword, location, language)
            elif provider == APIProvider.UBERSUGGEST:
                keyword_data = self._get_ubersuggest_keyword_data(keyword, location, language)
            
            if keyword_data:
                # Cache the result
                cache.set(cache_key, keyword_data.__dict__, timeout=self.cache_timeout)
                
                # Update rate limit tracking
                self._update_rate_limit(provider)
            
            return keyword_data
            
        except Exception as e:
            logger.error(f"Error getting keyword data for '{keyword}': {str(e)}")
            return None
    
    def get_keyword_suggestions(self, seed_keyword: str, provider: APIProvider = APIProvider.MOCK_API,
                               limit: int = 100) -> List[KeywordData]:
        """Get keyword suggestions based on seed keyword"""
        try:
            # Check cache first
            cache_key = f"keyword_suggestions_{provider.value}_{seed_keyword}_{limit}"
            cached_data = cache.get(cache_key)
            if cached_data:
                return [KeywordData(**kw_data) for kw_data in cached_data]
            
            # Check rate limits
            if not self._check_rate_limit(provider):
                logger.warning(f"Rate limit exceeded for {provider.value}")
                return []
            
            suggestions = []
            
            if provider == APIProvider.MOCK_API:
                suggestions = self._get_mock_keyword_suggestions(seed_keyword, limit)
            elif provider == APIProvider.SEMRUSH:
                suggestions = self._get_semrush_keyword_suggestions(seed_keyword, limit)
            elif provider == APIProvider.AHREFS:
                suggestions = self._get_ahrefs_keyword_suggestions(seed_keyword, limit)
            
            if suggestions:
                # Cache the results
                cache.set(cache_key, [kw.__dict__ for kw in suggestions], timeout=self.cache_timeout)
                
                # Update rate limit tracking
                self._update_rate_limit(provider)
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error getting keyword suggestions for '{seed_keyword}': {str(e)}")
            return []
    
    def analyze_competitor_keywords(self, competitor_domain: str, provider: APIProvider = APIProvider.MOCK_API,
                                   limit: int = 1000) -> Optional[CompetitorKeywords]:
        """Analyze competitor keywords and identify opportunities"""
        try:
            # Check cache first
            cache_key = f"competitor_keywords_{provider.value}_{competitor_domain}_{limit}"
            cached_data = cache.get(cache_key)
            if cached_data:
                return CompetitorKeywords(**cached_data)
            
            # Check rate limits
            if not self._check_rate_limit(provider):
                logger.warning(f"Rate limit exceeded for {provider.value}")
                return None
            
            competitor_data = None
            
            if provider == APIProvider.MOCK_API:
                competitor_data = self._get_mock_competitor_keywords(competitor_domain, limit)
            elif provider == APIProvider.SEMRUSH:
                competitor_data = self._get_semrush_competitor_keywords(competitor_domain, limit)
            elif provider == APIProvider.AHREFS:
                competitor_data = self._get_ahrefs_competitor_keywords(competitor_domain, limit)
            
            if competitor_data:
                # Cache the result
                cache.set(cache_key, competitor_data.__dict__, timeout=self.cache_timeout)
                
                # Update rate limit tracking
                self._update_rate_limit(provider)
            
            return competitor_data
            
        except Exception as e:
            logger.error(f"Error analyzing competitor keywords for '{competitor_domain}': {str(e)}")
            return None
    
    def get_trending_keywords(self, niche: str, provider: APIProvider = APIProvider.MOCK_API,
                             timeframe: str = '30d') -> List[KeywordData]:
        """Get trending keywords in a specific niche"""
        try:
            # Check cache first
            cache_key = f"trending_keywords_{provider.value}_{niche}_{timeframe}"
            cached_data = cache.get(cache_key)
            if cached_data:
                return [KeywordData(**kw_data) for kw_data in cached_data]
            
            # Check rate limits
            if not self._check_rate_limit(provider):
                logger.warning(f"Rate limit exceeded for {provider.value}")
                return []
            
            trending_keywords = []
            
            if provider == APIProvider.MOCK_API:
                trending_keywords = self._get_mock_trending_keywords(niche, timeframe)
            elif provider == APIProvider.SERPAPI:
                trending_keywords = self._get_serpapi_trending_keywords(niche, timeframe)
            
            if trending_keywords:
                # Cache the results
                cache.set(cache_key, [kw.__dict__ for kw in trending_keywords], 
                         timeout=self.cache_timeout // 7)  # Shorter cache for trending data
                
                # Update rate limit tracking
                self._update_rate_limit(provider)
            
            return trending_keywords
            
        except Exception as e:
            logger.error(f"Error getting trending keywords for '{niche}': {str(e)}")
            return []
    
    def get_question_keywords(self, topic: str, provider: APIProvider = APIProvider.MOCK_API) -> List[str]:
        """Get question-based keywords for a topic"""
        try:
            # Check cache first
            cache_key = f"question_keywords_{provider.value}_{topic}"
            cached_data = cache.get(cache_key)
            if cached_data:
                return cached_data
            
            # Check rate limits
            if not self._check_rate_limit(provider):
                logger.warning(f"Rate limit exceeded for {provider.value}")
                return []
            
            questions = []
            
            if provider == APIProvider.MOCK_API:
                questions = self._get_mock_question_keywords(topic)
            elif provider == APIProvider.SERPAPI:
                questions = self._get_serpapi_question_keywords(topic)
            
            if questions:
                # Cache the results
                cache.set(cache_key, questions, timeout=self.cache_timeout)
                
                # Update rate limit tracking
                self._update_rate_limit(provider)
            
            return questions
            
        except Exception as e:
            logger.error(f"Error getting question keywords for '{topic}': {str(e)}")
            return []
    
    def _check_rate_limit(self, provider: APIProvider) -> bool:
        """Check if API rate limit allows for another request"""
        try:
            current_time = datetime.now()
            provider_key = provider.value
            
            if provider_key not in self.request_counts:
                self.request_counts[provider_key] = {
                    'minute_requests': [],
                    'daily_requests': 0,
                    'last_reset': current_time.date()
                }
            
            provider_data = self.request_counts[provider_key]
            limits = self.rate_limits[provider]
            
            # Reset daily counter if new day
            if provider_data['last_reset'] != current_time.date():
                provider_data['daily_requests'] = 0
                provider_data['last_reset'] = current_time.date()
            
            # Clean old minute requests
            minute_ago = current_time - timedelta(minutes=1)
            provider_data['minute_requests'] = [
                req_time for req_time in provider_data['minute_requests']
                if req_time > minute_ago
            ]
            
            # Check limits
            if len(provider_data['minute_requests']) >= limits['requests_per_minute']:
                return False
            
            if provider_data['daily_requests'] >= limits['daily_limit']:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking rate limit: {str(e)}")
            return False
    
    def _update_rate_limit(self, provider: APIProvider):
        """Update rate limit tracking after successful request"""
        try:
            current_time = datetime.now()
            provider_key = provider.value
            
            if provider_key in self.request_counts:
                self.request_counts[provider_key]['minute_requests'].append(current_time)
                self.request_counts[provider_key]['daily_requests'] += 1
                
        except Exception as e:
            logger.error(f"Error updating rate limit: {str(e)}")
    
    def _get_mock_keyword_data(self, keyword: str, location: str, language: str) -> KeywordData:
        """Generate mock keyword data for testing"""
        import random
        
        # Generate realistic mock data
        base_volume = random.randint(100, 10000)
        difficulty = random.uniform(20, 80)
        cpc = random.uniform(0.5, 5.0)
        
        # Generate related keywords
        related = [
            f"{keyword} guide",
            f"best {keyword}",
            f"how to {keyword}",
            f"{keyword} tips",
            f"{keyword} tutorial"
        ]
        
        # Generate questions
        questions = [
            f"What is {keyword}?",
            f"How to use {keyword}?",
            f"Why {keyword}?",
            f"When to {keyword}?",
            f"Where to find {keyword}?"
        ]
        
        # Generate long-tail variations
        long_tail = [
            f"{keyword} for beginners",
            f"{keyword} step by step",
            f"{keyword} complete guide",
            f"{keyword} best practices",
            f"{keyword} advanced techniques"
        ]
        
        return KeywordData(
            keyword=keyword,
            search_volume=base_volume,
            keyword_difficulty=difficulty,
            cpc=cpc,
            competition='medium',
            trend_data=[random.randint(80, 120) for _ in range(12)],
            related_keywords=related,
            questions=questions,
            long_tail_variations=long_tail,
            search_intent='informational',
            seasonal_trends={
                'Q1': random.uniform(0.8, 1.2),
                'Q2': random.uniform(0.8, 1.2),
                'Q3': random.uniform(0.8, 1.2),
                'Q4': random.uniform(0.8, 1.2)
            },
            serp_features=['featured_snippet', 'people_also_ask', 'related_searches'],
            api_provider=APIProvider.MOCK_API.value,
            last_updated=datetime.now()
        )

    def _get_mock_keyword_suggestions(self, seed_keyword: str, limit: int) -> List[KeywordData]:
        """Generate mock keyword suggestions"""
        import random

        suggestions = []

        # Generate variations
        prefixes = ['best', 'top', 'how to', 'what is', 'why', 'when', 'where']
        suffixes = ['guide', 'tips', 'tutorial', 'review', 'comparison', 'examples', 'tools']

        for i in range(min(limit, 50)):
            if i < 10:
                # Prefix variations
                variation = f"{random.choice(prefixes)} {seed_keyword}"
            elif i < 20:
                # Suffix variations
                variation = f"{seed_keyword} {random.choice(suffixes)}"
            else:
                # Related terms
                variation = f"{seed_keyword} {random.choice(['alternative', 'solution', 'method', 'approach', 'strategy'])}"

            suggestions.append(self._get_mock_keyword_data(variation, 'US', 'en'))

        return suggestions

    def _get_mock_competitor_keywords(self, domain: str, limit: int) -> CompetitorKeywords:
        """Generate mock competitor keyword analysis"""
        import random

        # Generate organic keywords
        organic_keywords = []
        for i in range(min(limit // 2, 100)):
            keyword = f"competitor keyword {i+1}"
            organic_keywords.append(self._get_mock_keyword_data(keyword, 'US', 'en'))

        # Generate paid keywords
        paid_keywords = []
        for i in range(min(limit // 4, 50)):
            keyword = f"paid keyword {i+1}"
            paid_keywords.append(self._get_mock_keyword_data(keyword, 'US', 'en'))

        # Generate gaps and opportunities
        keyword_gaps = [f"gap keyword {i+1}" for i in range(20)]
        content_gaps = [f"content gap topic {i+1}" for i in range(10)]

        ranking_opportunities = [
            {
                'keyword': f"opportunity keyword {i+1}",
                'current_position': random.randint(11, 50),
                'opportunity_position': random.randint(1, 10),
                'search_volume': random.randint(500, 5000),
                'difficulty': random.uniform(30, 70)
            }
            for i in range(15)
        ]

        return CompetitorKeywords(
            domain=domain,
            organic_keywords=organic_keywords,
            paid_keywords=paid_keywords,
            keyword_gaps=keyword_gaps,
            content_gaps=content_gaps,
            ranking_opportunities=ranking_opportunities
        )

    def _get_mock_trending_keywords(self, niche: str, timeframe: str) -> List[KeywordData]:
        """Generate mock trending keywords"""
        import random

        trending_keywords = []

        # Generate trending terms for the niche
        trending_terms = [
            f"{niche} trends 2024",
            f"new {niche} technology",
            f"{niche} innovations",
            f"future of {niche}",
            f"{niche} predictions",
            f"emerging {niche} tools",
            f"{niche} automation",
            f"ai in {niche}",
            f"{niche} best practices 2024",
            f"latest {niche} updates"
        ]

        for term in trending_terms:
            keyword_data = self._get_mock_keyword_data(term, 'US', 'en')
            # Boost search volume for trending keywords
            keyword_data.search_volume = int(keyword_data.search_volume * random.uniform(1.5, 3.0))
            trending_keywords.append(keyword_data)

        return trending_keywords

    def _get_mock_question_keywords(self, topic: str) -> List[str]:
        """Generate mock question keywords"""
        question_starters = [
            "What is", "How to", "Why", "When", "Where", "Who", "Which",
            "How much", "How many", "How long", "How often", "What are"
        ]

        questions = []
        for starter in question_starters:
            questions.append(f"{starter} {topic}?")
            questions.append(f"{starter} {topic} work?")
            questions.append(f"{starter} {topic} used for?")

        return questions[:20]  # Return top 20 questions

    def _get_semrush_keyword_data(self, keyword: str, location: str, language: str) -> Optional[KeywordData]:
        """Get keyword data from SEMrush API"""
        try:
            config = self.api_configs[APIProvider.SEMRUSH]
            if not config['api_key']:
                logger.warning("SEMrush API key not configured")
                return None

            # SEMrush API endpoint for keyword data
            url = f"{config['base_url']}/analytics/v1/"
            params = {
                'type': 'phrase_this',
                'key': config['api_key'],
                'phrase': keyword,
                'database': location.lower(),
                'export_columns': 'Ph,Nq,Cp,Co,Nr,Td'
            }

            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()

            # Parse SEMrush response (CSV format)
            lines = response.text.strip().split('\n')
            if len(lines) < 2:
                return None

            data = lines[1].split(';')
            if len(data) < 6:
                return None

            return KeywordData(
                keyword=keyword,
                search_volume=int(data[1]) if data[1].isdigit() else 0,
                keyword_difficulty=float(data[5]) if data[5].replace('.', '').isdigit() else 50.0,
                cpc=float(data[2]) if data[2].replace('.', '').isdigit() else 0.0,
                competition='medium',  # SEMrush uses different scale
                trend_data=[],
                related_keywords=[],
                questions=[],
                long_tail_variations=[],
                search_intent='informational',
                seasonal_trends={},
                serp_features=[],
                api_provider=APIProvider.SEMRUSH.value,
                last_updated=datetime.now()
            )

        except Exception as e:
            logger.error(f"Error getting SEMrush data for '{keyword}': {str(e)}")
            return None

    def _get_ahrefs_keyword_data(self, keyword: str, location: str, language: str) -> Optional[KeywordData]:
        """Get keyword data from Ahrefs API"""
        try:
            config = self.api_configs[APIProvider.AHREFS]
            if not config['api_key']:
                logger.warning("Ahrefs API key not configured")
                return None

            # Ahrefs API endpoint for keyword data
            url = f"{config['base_url']}/keywords-explorer/v3/keywords/overview"
            headers = {
                'Authorization': f"Bearer {config['api_key']}",
                'Accept': 'application/json'
            }
            params = {
                'keywords': keyword,
                'country': location.lower()
            }

            response = requests.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()

            if 'keywords' not in data or not data['keywords']:
                return None

            keyword_info = data['keywords'][0]

            return KeywordData(
                keyword=keyword,
                search_volume=keyword_info.get('search_volume', 0),
                keyword_difficulty=keyword_info.get('keyword_difficulty', 50.0),
                cpc=keyword_info.get('cpc', 0.0),
                competition=keyword_info.get('competition', 'medium'),
                trend_data=keyword_info.get('trend_data', []),
                related_keywords=[],
                questions=[],
                long_tail_variations=[],
                search_intent=keyword_info.get('search_intent', 'informational'),
                seasonal_trends={},
                serp_features=keyword_info.get('serp_features', []),
                api_provider=APIProvider.AHREFS.value,
                last_updated=datetime.now()
            )

        except Exception as e:
            logger.error(f"Error getting Ahrefs data for '{keyword}': {str(e)}")
            return None

    def _get_serpapi_keyword_data(self, keyword: str, location: str, language: str) -> Optional[KeywordData]:
        """Get keyword data from SerpAPI"""
        try:
            config = self.api_configs[APIProvider.SERPAPI]
            if not config['api_key']:
                logger.warning("SerpAPI key not configured")
                return None

            # SerpAPI endpoint for search results
            params = {
                'engine': 'google',
                'q': keyword,
                'location': location,
                'hl': language,
                'api_key': config['api_key']
            }

            response = requests.get(config['base_url'], params=params, timeout=30)
            response.raise_for_status()

            data = response.json()

            # Extract SERP features
            serp_features = []
            if 'answer_box' in data:
                serp_features.append('answer_box')
            if 'knowledge_graph' in data:
                serp_features.append('knowledge_graph')
            if 'people_also_ask' in data:
                serp_features.append('people_also_ask')

            # Extract related searches
            related_keywords = []
            if 'related_searches' in data:
                related_keywords = [item['query'] for item in data['related_searches'][:10]]

            # Extract questions from People Also Ask
            questions = []
            if 'people_also_ask' in data:
                questions = [item['question'] for item in data['people_also_ask'][:10]]

            return KeywordData(
                keyword=keyword,
                search_volume=0,  # SerpAPI doesn't provide search volume directly
                keyword_difficulty=50.0,  # Default value
                cpc=0.0,
                competition='medium',
                trend_data=[],
                related_keywords=related_keywords,
                questions=questions,
                long_tail_variations=[],
                search_intent='informational',
                seasonal_trends={},
                serp_features=serp_features,
                api_provider=APIProvider.SERPAPI.value,
                last_updated=datetime.now()
            )

        except Exception as e:
            logger.error(f"Error getting SerpAPI data for '{keyword}': {str(e)}")
            return None
