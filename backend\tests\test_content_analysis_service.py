"""
Unit tests for Content Analysis Service
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from app.services.content_analysis_service import ContentAnalysisService


class TestContentAnalysisService:
    """Test Content Analysis Service functionality"""
    
    @pytest.fixture
    def analysis_service(self):
        """Create content analysis service instance"""
        with patch('app.services.content_analysis_service.spacy.load') as mock_spacy:
            # Mock spaCy model
            mock_nlp = Mock()
            mock_spacy.return_value = mock_nlp
            
            service = ContentAnalysisService()
            service.nlp = mock_nlp
            return service
    
    def test_analyze_content_success(self, analysis_service, sample_content_data):
        """Test successful content analysis"""
        # Mock spaCy processing
        mock_doc = Mock()
        mock_doc.sents = [Mock(text='Test sentence.')]
        mock_doc.__iter__ = Mock(return_value=iter([
            Mock(text='Web', pos_='NOUN', is_alpha=True, is_stop=False, is_space=False, is_punct=False, lemma_='web'),
            <PERSON>ck(text='development', pos_='NOUN', is_alpha=True, is_stop=False, is_space=False, is_punct=False, lemma_='development')
        ]))
        mock_doc.ents = []
        mock_doc.noun_chunks = []
        
        analysis_service.nlp.return_value = mock_doc
        
        result = analysis_service.analyze_content(sample_content_data)
        
        assert 'error' not in result
        assert 'content_hash' in result
        assert 'basic_metrics' in result
        assert 'linguistic_analysis' in result
        assert 'keyword_analysis' in result
        assert 'quality_score' in result
        assert isinstance(result['quality_score'], (int, float))
        assert 0 <= result['quality_score'] <= 10
    
    def test_analyze_content_empty_content(self, analysis_service):
        """Test content analysis with empty content"""
        empty_content = {'content': '', 'title': 'Test'}
        
        result = analysis_service.analyze_content(empty_content)
        
        assert 'error' in result
        assert result['error'] == 'Content too short for analysis'
    
    def test_analyze_content_short_content(self, analysis_service):
        """Test content analysis with very short content"""
        short_content = {'content': 'Short text.', 'title': 'Test'}
        
        result = analysis_service.analyze_content(short_content)
        
        assert 'error' in result
        assert result['error'] == 'Content too short for analysis'
    
    def test_analyze_basic_metrics(self, analysis_service):
        """Test basic metrics analysis"""
        content = "This is a test sentence. This is another test sentence."
        title = "Test Title"
        
        # Mock spaCy document
        mock_tokens = [
            Mock(text='This', is_space=False, is_punct=False, is_alpha=True, lemma_='this'),
            Mock(text='is', is_space=False, is_punct=False, is_alpha=True, lemma_='be'),
            Mock(text='a', is_space=False, is_punct=False, is_alpha=True, lemma_='a'),
            Mock(text='test', is_space=False, is_punct=False, is_alpha=True, lemma_='test'),
        ]
        mock_sents = [
            Mock(text='This is a test sentence.'),
            Mock(text='This is another test sentence.')
        ]
        
        mock_doc = Mock()
        mock_doc.__iter__ = Mock(return_value=iter(mock_tokens))
        mock_doc.sents = mock_sents
        
        analysis_service.nlp.return_value = mock_doc
        
        result = analysis_service._analyze_basic_metrics(content, title)
        
        assert 'word_count' in result
        assert 'character_count' in result
        assert 'sentence_count' in result
        assert 'paragraph_count' in result
        assert result['sentence_count'] == 2
        assert result['character_count'] == len(content)
    
    def test_analyze_keywords(self, analysis_service):
        """Test keyword analysis"""
        content = "Web development is important. Web development requires skills."
        title = "Web Development Guide"
        
        # Mock spaCy document
        mock_tokens = [
            Mock(text='Web', is_stop=False, is_punct=False, is_space=False, is_alpha=True, lemma_='web'),
            Mock(text='development', is_stop=False, is_punct=False, is_space=False, is_alpha=True, lemma_='development'),
            Mock(text='is', is_stop=True, is_punct=False, is_space=False, is_alpha=True, lemma_='be'),
            Mock(text='important', is_stop=False, is_punct=False, is_space=False, is_alpha=True, lemma_='important'),
        ]
        
        mock_chunks = [
            Mock(text='Web development'),
            Mock(text='important skills')
        ]
        
        mock_doc = Mock()
        mock_doc.__iter__ = Mock(return_value=iter(mock_tokens))
        mock_doc.noun_chunks = mock_chunks
        
        analysis_service.nlp.return_value = mock_doc
        
        result = analysis_service._analyze_keywords(content, title)
        
        assert 'primary_keywords' in result
        assert 'secondary_keywords' in result
        assert 'keyword_diversity' in result
        assert isinstance(result['primary_keywords'], list)
        
        # Check that keywords have required fields
        if result['primary_keywords']:
            keyword = result['primary_keywords'][0]
            assert 'keyword' in keyword
            assert 'frequency' in keyword
            assert 'density' in keyword
    
    def test_analyze_readability(self, analysis_service):
        """Test readability analysis"""
        content = "This is a simple sentence. This is another simple sentence for testing readability."
        
        result = analysis_service._analyze_readability(content)
        
        assert 'flesch_reading_ease' in result
        assert 'flesch_kincaid_grade' in result
        assert 'reading_level' in result
        assert 'avg_sentence_length' in result
        assert 'readability_score' in result
        
        assert 0 <= result['flesch_reading_ease'] <= 100
        assert result['readability_score'] >= 0
    
    def test_analyze_structure(self, analysis_service):
        """Test content structure analysis"""
        content = """
        <h1>Main Title</h1>
        <h2>Subtitle</h2>
        <p>This is a paragraph.</p>
        <ul><li>List item</li></ul>
        <img src="test.jpg" alt="Test">
        <a href="http://example.com">Link</a>
        """
        
        result = analysis_service._analyze_structure(content)
        
        assert 'headings' in result
        assert 'lists' in result
        assert 'media_elements' in result
        assert 'structure_score' in result
        
        assert result['headings']['h1'] == 1
        assert result['headings']['h2'] == 1
        assert result['lists']['unordered'] == 1
        assert result['media_elements']['images'] == 1
        assert result['media_elements']['links'] == 1
    
    def test_classify_topics(self, analysis_service):
        """Test topic classification"""
        content = "Technology and software development are important in modern business."
        
        # Mock spaCy document
        mock_tokens = [
            Mock(lemma_='technology', is_alpha=True, is_stop=False),
            Mock(lemma_='software', is_alpha=True, is_stop=False),
            Mock(lemma_='development', is_alpha=True, is_stop=False),
            Mock(lemma_='business', is_alpha=True, is_stop=False),
        ]
        
        mock_doc = Mock()
        mock_doc.__iter__ = Mock(return_value=iter(mock_tokens))
        
        analysis_service.nlp.return_value = mock_doc
        
        result = analysis_service._classify_topics(content)
        
        assert 'primary_topics' in result
        assert 'all_topic_scores' in result
        assert 'topic_diversity' in result
        
        # Should detect technology and business topics
        topics = [topic['topic'] for topic in result['primary_topics']]
        assert 'technology' in topics or 'business' in topics
    
    def test_analyze_seo_factors(self, analysis_service):
        """Test SEO factors analysis"""
        content_data = {
            'title': 'Complete Guide to Web Development',
            'content': 'This is a comprehensive guide about web development with many useful tips.',
            'excerpt': 'A comprehensive guide covering all aspects of web development.',
            'url': 'https://example.com/web-development-guide'
        }
        
        result = analysis_service._analyze_seo_factors(content_data)
        
        assert 'title_analysis' in result
        assert 'meta_description' in result
        assert 'url_analysis' in result
        assert 'content_length' in result
        
        title_analysis = result['title_analysis']
        assert 'length' in title_analysis
        assert 'word_count' in title_analysis
        assert 'score' in title_analysis
    
    def test_calculate_quality_score(self, analysis_service):
        """Test quality score calculation"""
        analysis_result = {
            'basic_metrics': {'word_count': 1000},
            'readability_analysis': {'readability_score': 8.0},
            'content_structure': {'structure_score': 7.0},
            'seo_analysis': {
                'title_analysis': {'score': 8.0},
                'content_length': {'score': 9.0}
            },
            'semantic_analysis': {'semantic_density': 0.7}
        }
        
        score = analysis_service._calculate_quality_score(analysis_result)
        
        assert isinstance(score, float)
        assert 0 <= score <= 10
    
    def test_generate_suggestions(self, analysis_service):
        """Test improvement suggestions generation"""
        analysis_result = {
            'quality_score': 4.0,  # Low quality
            'basic_metrics': {'word_count': 200},  # Short content
            'readability_analysis': {'flesch_reading_ease': 20},  # Poor readability
            'content_structure': {'headings': {'total': 0}},  # No headings
            'seo_analysis': {
                'title_analysis': {'optimal_length': False}
            }
        }
        
        suggestions = analysis_service._generate_suggestions(analysis_result)
        
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0
        
        # Should suggest improvements for identified issues
        suggestion_text = ' '.join(suggestions).lower()
        assert 'content' in suggestion_text or 'improve' in suggestion_text
    
    def test_count_syllables(self, analysis_service):
        """Test syllable counting"""
        assert analysis_service._count_syllables('hello') >= 1
        assert analysis_service._count_syllables('development') >= 3
        assert analysis_service._count_syllables('a') == 1
    
    def test_get_reading_level(self, analysis_service):
        """Test reading level classification"""
        assert analysis_service._get_reading_level(95) == 'Very Easy'
        assert analysis_service._get_reading_level(85) == 'Easy'
        assert analysis_service._get_reading_level(65) == 'Standard'
        assert analysis_service._get_reading_level(45) == 'Fairly Difficult'
        assert analysis_service._get_reading_level(25) == 'Very Difficult'
    
    def test_error_handling(self, analysis_service):
        """Test error handling in content analysis"""
        # Test with malformed content data
        malformed_data = {'invalid': 'data'}
        
        result = analysis_service.analyze_content(malformed_data)
        
        assert 'error' in result
        assert 'Analysis failed' in result['error']
