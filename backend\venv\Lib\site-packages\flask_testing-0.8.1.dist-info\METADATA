Metadata-Version: 2.4
Name: Flask-Testing
Version: 0.8.1
Summary: Unit testing for Flask
Home-page: https://github.com/jarus/flask-testing
Author: <PERSON>
Author-email: <EMAIL>
License: BSD
Platform: any
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Classifier: Topic :: Software Development :: Libraries :: Python Modules
License-File: LICENSE
Requires-Dist: Flask
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: platform
Dynamic: requires-dist
Dynamic: summary


Flask-Testing
-------------

Flask unittest integration.

Links
`````

* `documentation <http://packages.python.org/Flask-Testing>`
* `development version <http://github.com/jarus/flask-testing/zipball/master#egg=Flask-Testing-dev>`

