"""
Unit tests for Authentication Service
"""
import pytest
from unittest.mock import Mo<PERSON>, patch, MagicMock
from flask import <PERSON>lask
from app.services.auth_service import AuthenticationService
from app.models.user import User
from app.models.api_key import <PERSON><PERSON><PERSON><PERSON>
from app import db
import requests


class TestAuthenticationService:
    """Test Authentication Service functionality"""
    
    def test_authenticate_email_password_success(self, app, db_session, user):
        """Test successful email/password authentication"""
        with app.app_context():
            result = AuthenticationService.authenticate_email_password(
                user.email, 'testpassword123'
            )
            
            authenticated_user, auth_method = result
            assert authenticated_user is not None
            assert authenticated_user.id == user.id
            assert auth_method == 'email_password'
    
    def test_authenticate_email_password_invalid_credentials(self, app, db_session, user):
        """Test email/password authentication with invalid credentials"""
        with app.app_context():
            # Wrong password
            result = AuthenticationService.authenticate_email_password(
                user.email, 'wrongpassword'
            )
            
            authenticated_user, error = result
            assert authenticated_user is None
            assert error == 'invalid_credentials'
            
            # Wrong email
            result = AuthenticationService.authenticate_email_password(
                '<EMAIL>', 'testpassword123'
            )
            
            authenticated_user, error = result
            assert authenticated_user is None
            assert error == 'invalid_credentials'
    
    def test_authenticate_email_password_inactive_user(self, app, db_session, user):
        """Test authentication with inactive user"""
        with app.app_context():
            # Deactivate user
            user.is_active = False
            db.session.commit()
            
            result = AuthenticationService.authenticate_email_password(
                user.email, 'testpassword123'
            )
            
            authenticated_user, error = result
            assert authenticated_user is None
            assert error == 'invalid_credentials'
    
    def test_authenticate_api_key_success(self, app, db_session, user, website):
        """Test successful API key authentication"""
        with app.app_context():
            # Generate API key
            raw_key, api_key_record = ApiKey.generate_key(user.id, website.id)
            
            result = AuthenticationService.authenticate_api_key(raw_key)
            
            authenticated_user, auth_method, api_key = result
            assert authenticated_user is not None
            assert authenticated_user.id == user.id
            assert auth_method == 'api_key'
            assert api_key.id == api_key_record.id
    
    def test_authenticate_api_key_invalid(self, app, db_session):
        """Test API key authentication with invalid key"""
        with app.app_context():
            # Invalid format
            result = AuthenticationService.authenticate_api_key('invalid_key')
            authenticated_user, error = result
            assert authenticated_user is None
            assert error == 'invalid_api_key'
            
            # Valid format but non-existent key
            result = AuthenticationService.authenticate_api_key('lup_nonexistent_key_12345')
            authenticated_user, error = result
            assert authenticated_user is None
            assert error == 'invalid_api_key'
    
    def test_authenticate_api_key_ip_restriction(self, app, db_session, user, website):
        """Test API key authentication with IP restrictions"""
        with app.app_context():
            # Generate API key with IP whitelist
            raw_key, api_key_record = ApiKey.generate_key(user.id, website.id)
            api_key_record.ip_whitelist = ['***********']
            db.session.commit()
            
            # Allowed IP should work
            result = AuthenticationService.authenticate_api_key(raw_key, '***********')
            authenticated_user, auth_method, api_key = result
            assert authenticated_user is not None
            
            # Disallowed IP should fail
            result = AuthenticationService.authenticate_api_key(raw_key, '***********')
            authenticated_user, error = result
            assert authenticated_user is None
            assert error == 'ip_not_allowed'
    
    @patch('requests.get')
    def test_authenticate_wordpress_success(self, mock_get, app, db_session):
        """Test successful WordPress authentication"""
        with app.app_context():
            # Mock WordPress API response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                'id': 1,
                'email': '<EMAIL>',
                'first_name': 'WordPress',
                'last_name': 'User'
            }
            mock_get.return_value = mock_response
            
            result = AuthenticationService.authenticate_wordpress(
                'wp_user', 'wp_password', 'https://example.com'
            )
            
            authenticated_user, auth_method = result
            assert authenticated_user is not None
            assert authenticated_user.email == '<EMAIL>'
            assert auth_method == 'wordpress'
            
            # Verify user was created in database
            created_user = User.query.filter_by(email='<EMAIL>').first()
            assert created_user is not None
            assert created_user.is_verified is True
    
    @patch('requests.get')
    def test_authenticate_wordpress_invalid_credentials(self, mock_get, app, db_session):
        """Test WordPress authentication with invalid credentials"""
        with app.app_context():
            # Mock failed WordPress API response
            mock_response = Mock()
            mock_response.status_code = 401
            mock_get.return_value = mock_response
            
            result = AuthenticationService.authenticate_wordpress(
                'wrong_user', 'wrong_password', 'https://example.com'
            )
            
            authenticated_user, error = result
            assert authenticated_user is None
            assert error == 'invalid_credentials'
    
    @patch('requests.get')
    def test_authenticate_wordpress_connection_error(self, mock_get, app, db_session):
        """Test WordPress authentication with connection error"""
        with app.app_context():
            # Mock connection error
            mock_get.side_effect = requests.RequestException('Connection failed')
            
            result = AuthenticationService.authenticate_wordpress(
                'user', 'password', 'https://example.com'
            )
            
            authenticated_user, error = result
            assert authenticated_user is None
            assert error == 'connection_error'
    
    def test_generate_tokens(self, app, db_session, user):
        """Test JWT token generation"""
        with app.app_context():
            tokens = AuthenticationService.generate_tokens(user)
            
            assert tokens is not None
            assert 'access_token' in tokens
            assert 'refresh_token' in tokens
            assert 'expires_in' in tokens
            assert 'token_type' in tokens
            assert tokens['token_type'] == 'Bearer'
    
    def test_register_user_success(self, app, db_session):
        """Test successful user registration"""
        with app.app_context():
            result = AuthenticationService.register_user(
                email='<EMAIL>',
                password='newpassword123',
                first_name='New',
                last_name='User'
            )
            
            user, api_key = result
            assert user is not None
            assert user.email == '<EMAIL>'
            assert user.first_name == 'New'
            assert user.last_name == 'User'
            assert api_key is not None
            assert api_key.startswith('lup_')
            
            # Verify user was created in database
            created_user = User.query.filter_by(email='<EMAIL>').first()
            assert created_user is not None
    
    def test_register_user_existing_email(self, app, db_session, user):
        """Test user registration with existing email"""
        with app.app_context():
            result = AuthenticationService.register_user(
                email=user.email,  # Use existing user's email
                password='password123'
            )
            
            user_result, error = result
            assert user_result is None
            assert error == 'user_exists'
    
    def test_register_user_with_website(self, app, db_session):
        """Test user registration with website URL"""
        with app.app_context():
            result = AuthenticationService.register_user(
                email='<EMAIL>',
                password='password123',
                wp_site_url='https://example.com'
            )
            
            user, api_key = result
            assert user is not None
            
            # Check if website was created
            from app.models.website import Website
            website = Website.query.filter_by(user_id=user.id).first()
            assert website is not None
            assert website.domain == 'example.com'
    
    def test_check_rate_limit_within_limits(self, app, db_session, user):
        """Test rate limit check when within limits"""
        with app.app_context():
            result = AuthenticationService.check_rate_limit(user)
            
            within_limits, status = result
            assert within_limits is True
            assert status == 'within_limits'
    
    def test_check_rate_limit_exceeded(self, app, db_session, user):
        """Test rate limit check when limits exceeded"""
        with app.app_context():
            # Create usage that exceeds hourly limit
            from app.models.usage_stats import UsageStats
            from datetime import date
            
            today = date.today()
            usage = UsageStats(
                user_id=user.id,
                date=today,
                api_requests=2000  # Exceeds free plan hourly limit
            )
            db.session.add(usage)
            db.session.commit()
            
            result = AuthenticationService.check_rate_limit(user)
            
            within_limits, status = result
            assert within_limits is False
            assert status in ['hourly_limit_exceeded', 'daily_limit_exceeded']
    
    def test_validate_request_signature(self, app, db_session, user, website):
        """Test request signature validation"""
        with app.app_context():
            # Generate API key
            raw_key, api_key_record = ApiKey.generate_key(user.id, website.id)
            
            # Create mock request with signature
            mock_request = Mock()
            mock_request.headers = {
                'X-LinkUp-Signature': 'test_signature',
                'X-LinkUp-Timestamp': '1234567890'
            }
            mock_request.method = 'POST'
            mock_request.path = '/api/test'
            mock_request.data = b'{"test": "data"}'
            
            # This would normally validate HMAC signature
            # For testing, we'll just verify the method exists and handles errors gracefully
            result = AuthenticationService.validate_request_signature(mock_request, api_key_record)
            
            # Should return False for invalid signature (expected in test)
            assert isinstance(result, bool)
    
    @patch('app.services.auth_service.request')
    def test_authenticate_request_api_key(self, mock_request, app, db_session, user, website):
        """Test request authentication with API key"""
        with app.app_context():
            # Generate API key
            raw_key, api_key_record = ApiKey.generate_key(user.id, website.id)
            
            # Mock request with API key
            mock_request.headers = {'Authorization': f'Bearer {raw_key}'}
            mock_request.environ = {'HTTP_X_FORWARDED_FOR': '***********'}
            mock_request.remote_addr = '***********'
            mock_request.args = {}
            mock_request.authorization = None
            
            result = AuthenticationService.authenticate_request(mock_request)
            
            authenticated_user, auth_method, api_key = result
            assert authenticated_user is not None
            assert authenticated_user.id == user.id
            assert auth_method == 'api_key'
            assert api_key.id == api_key_record.id
    
    @patch('app.services.auth_service.request')
    def test_authenticate_request_no_auth(self, mock_request, app, db_session):
        """Test request authentication with no authentication provided"""
        with app.app_context():
            # Mock request with no authentication
            mock_request.headers = {}
            mock_request.environ = {}
            mock_request.remote_addr = '***********'
            mock_request.args = {}
            mock_request.authorization = None
            
            result = AuthenticationService.authenticate_request(mock_request)
            
            authenticated_user, error = result
            assert authenticated_user is None
            assert error == 'no_authentication'
