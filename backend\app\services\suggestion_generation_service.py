"""
Suggestion Generation Service
Generates actionable content suggestions based on keyword gaps and opportunities
"""
import logging
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json

from app import db, cache
from app.models.website import Website
from app.models.analysis import ContentAnalysis
from app.services.content_opportunity_service import ContentOpportunityService, ContentOpportunity
from app.services.keyword_gap_service import KeywordGapService

logger = logging.getLogger(__name__)


class SuggestionType(Enum):
    """Types of content suggestions"""
    NEW_CONTENT = "new_content"
    CONTENT_OPTIMIZATION = "content_optimization"
    CONTENT_EXPANSION = "content_expansion"
    KEYWORD_TARGETING = "keyword_targeting"
    TOPIC_CLUSTER = "topic_cluster"
    COMPETITIVE_RESPONSE = "competitive_response"
    TRENDING_OPPORTUNITY = "trending_opportunity"


class Priority(Enum):
    """Priority levels for suggestions"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class ContentSuggestion:
    """Represents a content suggestion"""
    suggestion_id: str
    suggestion_type: SuggestionType
    priority: Priority
    title: str
    description: str
    rationale: str
    target_keywords: List[str]
    primary_keyword: str
    content_type: str
    suggested_length: int
    estimated_effort_hours: int
    estimated_traffic: int
    difficulty_score: float
    opportunity_score: float
    roi_estimate: float
    success_metrics: List[str]
    action_steps: List[str]
    resources_needed: List[str]
    timeline: str
    related_suggestions: List[str]
    competitor_examples: List[Dict]
    content_outline: List[str]
    seo_recommendations: List[str]
    created_at: datetime
    expires_at: Optional[datetime]


class SuggestionGenerationService:
    """Service for generating actionable content suggestions"""
    
    def __init__(self):
        """Initialize the suggestion generation service"""
        self.opportunity_service = ContentOpportunityService()
        self.keyword_gap_service = KeywordGapService()
        self.cache_timeout = 3600 * 4  # 4 hours
        
        # Content templates for different types
        self.content_templates = {
            'blog_post': {
                'outline': [
                    'Introduction and hook',
                    'Problem definition',
                    'Main content sections (3-5)',
                    'Practical examples',
                    'Conclusion and call-to-action'
                ],
                'seo_tips': [
                    'Include target keyword in title and H1',
                    'Use keyword variations in subheadings',
                    'Optimize meta description',
                    'Include internal and external links',
                    'Add relevant images with alt text'
                ]
            },
            'guide': {
                'outline': [
                    'Introduction and overview',
                    'Prerequisites and requirements',
                    'Step-by-step instructions',
                    'Common mistakes to avoid',
                    'Advanced tips and tricks',
                    'Conclusion and next steps'
                ],
                'seo_tips': [
                    'Create comprehensive table of contents',
                    'Use numbered lists for steps',
                    'Include FAQ section',
                    'Add downloadable resources',
                    'Optimize for featured snippets'
                ]
            },
            'listicle': {
                'outline': [
                    'Compelling introduction',
                    'Numbered list items (5-15)',
                    'Brief explanation for each item',
                    'Supporting examples or data',
                    'Summary and takeaways'
                ],
                'seo_tips': [
                    'Use numbers in title',
                    'Create scannable content',
                    'Include relevant statistics',
                    'Add visual elements',
                    'Optimize for social sharing'
                ]
            },
            'comparison': {
                'outline': [
                    'Introduction to comparison',
                    'Criteria for evaluation',
                    'Detailed comparison sections',
                    'Pros and cons analysis',
                    'Recommendations and verdict'
                ],
                'seo_tips': [
                    'Use comparison tables',
                    'Include "vs" in title',
                    'Add product/service images',
                    'Create comparison charts',
                    'Include user reviews/testimonials'
                ]
            }
        }
        
        # Timeline estimates based on content type and effort
        self.timeline_estimates = {
            (1, 8): '1-2 days',
            (9, 16): '3-5 days',
            (17, 24): '1 week',
            (25, 40): '2 weeks',
            (41, 60): '3-4 weeks'
        }
    
    def generate_content_suggestions(self, website_id: int, limit: int = 20) -> Dict[str, Any]:
        """Generate comprehensive content suggestions for a website"""
        try:
            # Check cache first
            cache_key = f"content_suggestions_{website_id}_{limit}"
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result
            
            website = Website.query.get(website_id)
            if not website:
                raise ValueError(f"Website {website_id} not found")
            
            # Get content opportunities
            opportunities_data = self.opportunity_service.score_content_opportunities(website_id)
            
            if 'error' in opportunities_data:
                return opportunities_data
            
            opportunities = opportunities_data.get('opportunities', [])
            
            # Generate suggestions from opportunities
            suggestions = []
            
            for i, opp_data in enumerate(opportunities[:limit]):
                suggestion = self._create_suggestion_from_opportunity(opp_data, website, i)
                if suggestion:
                    suggestions.append(suggestion)
            
            # Generate additional strategic suggestions
            strategic_suggestions = self._generate_strategic_suggestions(website, opportunities_data)
            suggestions.extend(strategic_suggestions)
            
            # Prioritize and rank suggestions
            ranked_suggestions = self._prioritize_suggestions(suggestions)
            
            # Generate implementation roadmap
            roadmap = self._generate_implementation_roadmap(ranked_suggestions)
            
            result = {
                'website_id': website_id,
                'generated_at': datetime.now().isoformat(),
                'total_suggestions': len(ranked_suggestions),
                'suggestions': [self._serialize_suggestion(s) for s in ranked_suggestions],
                'implementation_roadmap': roadmap,
                'summary_insights': self._generate_summary_insights(ranked_suggestions),
                'quick_wins': self._identify_quick_wins(ranked_suggestions),
                'long_term_strategy': self._generate_long_term_strategy(ranked_suggestions)
            }
            
            # Cache the results
            cache.set(cache_key, result, timeout=self.cache_timeout)
            
            return result
            
        except Exception as e:
            logger.error(f"Error generating content suggestions: {str(e)}")
            return {'error': str(e)}
    
    def _create_suggestion_from_opportunity(self, opp_data: Dict, website: Website, index: int) -> Optional[ContentSuggestion]:
        """Create a content suggestion from an opportunity"""
        try:
            # Determine suggestion type based on opportunity type
            suggestion_type = self._map_opportunity_to_suggestion_type(opp_data.get('opportunity_type'))
            
            # Determine priority based on scores
            priority = self._determine_suggestion_priority(opp_data)
            
            # Generate content outline
            content_outline = self._generate_content_outline(
                opp_data.get('content_type', 'blog_post'),
                opp_data.get('primary_keyword', ''),
                opp_data.get('target_keywords', [])
            )
            
            # Generate SEO recommendations
            seo_recommendations = self._generate_seo_recommendations(
                opp_data.get('content_type', 'blog_post'),
                opp_data.get('primary_keyword', ''),
                opp_data.get('target_keywords', [])
            )
            
            # Generate action steps
            action_steps = self._generate_action_steps(opp_data, website)
            
            # Estimate timeline
            timeline = self._estimate_timeline(opp_data.get('estimated_effort_hours', 8))
            
            suggestion = ContentSuggestion(
                suggestion_id=f"sug_{website.id}_{index}",
                suggestion_type=suggestion_type,
                priority=priority,
                title=opp_data.get('title', 'Content Opportunity'),
                description=opp_data.get('description', ''),
                rationale=self._generate_rationale(opp_data),
                target_keywords=opp_data.get('target_keywords', []),
                primary_keyword=opp_data.get('primary_keyword', ''),
                content_type=opp_data.get('content_type', 'blog_post'),
                suggested_length=opp_data.get('suggested_length', 1500),
                estimated_effort_hours=opp_data.get('estimated_effort_hours', 8),
                estimated_traffic=opp_data.get('estimated_traffic', 0),
                difficulty_score=opp_data.get('difficulty_score', 50.0),
                opportunity_score=opp_data.get('opportunity_score', 50.0),
                roi_estimate=opp_data.get('roi_estimate', 0.0),
                success_metrics=self._generate_success_metrics(opp_data),
                action_steps=action_steps,
                resources_needed=self._identify_resources_needed(opp_data),
                timeline=timeline,
                related_suggestions=[],  # Will be populated later
                competitor_examples=self._extract_competitor_examples(opp_data),
                content_outline=content_outline,
                seo_recommendations=seo_recommendations,
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(days=30)  # Suggestions expire after 30 days
            )
            
            return suggestion
            
        except Exception as e:
            logger.error(f"Error creating suggestion from opportunity: {str(e)}")
            return None
    
    def _generate_strategic_suggestions(self, website: Website, opportunities_data: Dict) -> List[ContentSuggestion]:
        """Generate strategic content suggestions beyond individual opportunities"""
        strategic_suggestions = []
        
        try:
            opportunities = opportunities_data.get('opportunities', [])
            
            # Topic cluster suggestion
            if len(opportunities) >= 5:
                cluster_suggestion = self._create_topic_cluster_suggestion(opportunities, website)
                if cluster_suggestion:
                    strategic_suggestions.append(cluster_suggestion)
            
            # Content calendar suggestion
            calendar_suggestion = self._create_content_calendar_suggestion(opportunities, website)
            if calendar_suggestion:
                strategic_suggestions.append(calendar_suggestion)
            
            # Competitive response suggestion
            competitive_suggestion = self._create_competitive_response_suggestion(opportunities, website)
            if competitive_suggestion:
                strategic_suggestions.append(competitive_suggestion)
            
            return strategic_suggestions
            
        except Exception as e:
            logger.error(f"Error generating strategic suggestions: {str(e)}")
            return []
    
    def _map_opportunity_to_suggestion_type(self, opportunity_type: str) -> SuggestionType:
        """Map opportunity type to suggestion type"""
        mapping = {
            'keyword_targeting': SuggestionType.KEYWORD_TARGETING,
            'topic_expansion': SuggestionType.CONTENT_EXPANSION,
            'long_tail_capture': SuggestionType.NEW_CONTENT,
            'semantic_expansion': SuggestionType.CONTENT_OPTIMIZATION,
            'competitor_gap': SuggestionType.COMPETITIVE_RESPONSE,
            'trending_topic': SuggestionType.TRENDING_OPPORTUNITY
        }
        
        return mapping.get(opportunity_type, SuggestionType.NEW_CONTENT)
    
    def _determine_suggestion_priority(self, opp_data: Dict) -> Priority:
        """Determine priority based on opportunity scores"""
        opportunity_score = opp_data.get('opportunity_score', 0)
        impact_score = opp_data.get('impact_score', 0)
        urgency_score = opp_data.get('urgency_score', 0)
        
        # Calculate weighted priority score
        priority_score = (opportunity_score * 0.4 + impact_score * 0.3 + urgency_score * 0.3)
        
        if priority_score >= 80:
            return Priority.CRITICAL
        elif priority_score >= 65:
            return Priority.HIGH
        elif priority_score >= 40:
            return Priority.MEDIUM
        else:
            return Priority.LOW
    
    def _generate_content_outline(self, content_type: str, primary_keyword: str, target_keywords: List[str]) -> List[str]:
        """Generate content outline based on content type and keywords"""
        try:
            template = self.content_templates.get(content_type, self.content_templates['blog_post'])
            outline = template['outline'].copy()
            
            # Customize outline based on keywords
            if primary_keyword:
                # Add keyword-specific sections
                if 'how' in primary_keyword.lower():
                    outline.insert(-1, f"Step-by-step guide for {primary_keyword}")
                elif 'best' in primary_keyword.lower():
                    outline.insert(-1, f"Top recommendations for {primary_keyword}")
                elif 'vs' in primary_keyword.lower():
                    outline.insert(-1, f"Detailed comparison: {primary_keyword}")
            
            return outline
            
        except Exception as e:
            logger.error(f"Error generating content outline: {str(e)}")
            return ['Introduction', 'Main content', 'Conclusion']
    
    def _generate_seo_recommendations(self, content_type: str, primary_keyword: str, target_keywords: List[str]) -> List[str]:
        """Generate SEO recommendations for the content"""
        try:
            template = self.content_templates.get(content_type, self.content_templates['blog_post'])
            seo_tips = template['seo_tips'].copy()
            
            # Add keyword-specific SEO recommendations
            if primary_keyword:
                seo_tips.extend([
                    f"Target keyword density: 1-2% for '{primary_keyword}'",
                    f"Include '{primary_keyword}' in first 100 words",
                    f"Use related keywords: {', '.join(target_keywords[:3])}"
                ])
            
            return seo_tips
            
        except Exception as e:
            logger.error(f"Error generating SEO recommendations: {str(e)}")
            return ['Optimize for target keywords', 'Include relevant links', 'Add meta description']
    
    def _generate_action_steps(self, opp_data: Dict, website: Website) -> List[str]:
        """Generate specific action steps for implementing the suggestion"""
        steps = [
            "1. Conduct keyword research and validate search volume",
            "2. Analyze top-ranking competitor content",
            "3. Create detailed content outline",
            "4. Write and optimize content",
            "5. Add relevant images and media",
            "6. Optimize for SEO (title, meta, headings)",
            "7. Publish and promote content",
            "8. Monitor performance and rankings"
        ]
        
        # Customize based on content type
        content_type = opp_data.get('content_type', 'blog_post')
        if content_type == 'guide':
            steps.insert(4, "4.5. Create downloadable resources or templates")
        elif content_type == 'comparison':
            steps.insert(4, "4.5. Create comparison tables and charts")
        elif content_type == 'listicle':
            steps.insert(4, "4.5. Gather supporting statistics and examples")
        
        return steps
    
    def _estimate_timeline(self, effort_hours: int) -> str:
        """Estimate timeline based on effort hours"""
        for (min_hours, max_hours), timeline in self.timeline_estimates.items():
            if min_hours <= effort_hours <= max_hours:
                return timeline
        
        if effort_hours > 60:
            return '1+ months'
        else:
            return '1-2 days'
    
    def _generate_rationale(self, opp_data: Dict) -> str:
        """Generate rationale for the suggestion"""
        opportunity_score = opp_data.get('opportunity_score', 0)
        estimated_traffic = opp_data.get('estimated_traffic', 0)
        competition_level = opp_data.get('competition_level', 'medium')
        
        rationale = f"This opportunity scores {opportunity_score:.1f}/100 with potential for {estimated_traffic} monthly visitors. "
        rationale += f"Competition level is {competition_level}, making it "
        
        if competition_level == 'low':
            rationale += "an excellent opportunity for quick wins."
        elif competition_level == 'medium':
            rationale += "a balanced opportunity with good potential."
        else:
            rationale += "challenging but potentially high-reward."
        
        return rationale

    def _generate_success_metrics(self, opp_data: Dict) -> List[str]:
        """Generate success metrics for tracking the suggestion"""
        metrics = [
            f"Target ranking: Top 10 for '{opp_data.get('primary_keyword', 'target keyword')}'",
            f"Traffic goal: {opp_data.get('estimated_traffic', 100)}+ monthly organic visitors",
            "Engagement: 2+ minutes average time on page",
            "Conversion: Track leads/sales from content"
        ]

        # Add content-specific metrics
        content_type = opp_data.get('content_type', 'blog_post')
        if content_type == 'guide':
            metrics.append("Downloads: Track guide/resource downloads")
        elif content_type == 'comparison':
            metrics.append("Click-through: Track clicks to compared products/services")
        elif content_type == 'listicle':
            metrics.append("Social shares: Track social media engagement")

        return metrics

    def _identify_resources_needed(self, opp_data: Dict) -> List[str]:
        """Identify resources needed for implementing the suggestion"""
        resources = [
            "Content writer or copywriter",
            "SEO specialist for optimization",
            "Graphic designer for images/visuals"
        ]

        # Add content-specific resources
        content_type = opp_data.get('content_type', 'blog_post')
        if content_type == 'guide':
            resources.extend([
                "Subject matter expert for technical accuracy",
                "Designer for downloadable templates"
            ])
        elif content_type == 'comparison':
            resources.extend([
                "Product research specialist",
                "Data analyst for comparison metrics"
            ])
        elif content_type == 'video_script':
            resources.extend([
                "Video producer",
                "Video editor"
            ])

        return resources

    def _extract_competitor_examples(self, opp_data: Dict) -> List[Dict]:
        """Extract competitor examples from opportunity data"""
        # This would typically extract from the opportunity data
        # For now, return placeholder structure
        return [
            {
                'domain': 'competitor1.com',
                'title': 'Example competitor content',
                'url': 'https://competitor1.com/example',
                'ranking_position': 3,
                'estimated_traffic': 500
            }
        ]

    def _create_topic_cluster_suggestion(self, opportunities: List[Dict], website: Website) -> Optional[ContentSuggestion]:
        """Create a topic cluster strategy suggestion"""
        try:
            # Group opportunities by semantic similarity
            clusters = self._group_opportunities_by_topic(opportunities)

            if not clusters:
                return None

            # Find the largest cluster
            largest_cluster = max(clusters.items(), key=lambda x: len(x[1]))
            topic, cluster_opportunities = largest_cluster

            if len(cluster_opportunities) < 3:
                return None

            # Create cluster suggestion
            suggestion = ContentSuggestion(
                suggestion_id=f"cluster_{website.id}",
                suggestion_type=SuggestionType.TOPIC_CLUSTER,
                priority=Priority.HIGH,
                title=f"Create topic cluster around '{topic}'",
                description=f"Develop a comprehensive topic cluster with {len(cluster_opportunities)} related pieces of content",
                rationale=f"Building a topic cluster around '{topic}' will establish topical authority and improve overall rankings",
                target_keywords=[opp.get('primary_keyword', '') for opp in cluster_opportunities],
                primary_keyword=topic,
                content_type='pillar_page',
                suggested_length=4000,
                estimated_effort_hours=40,
                estimated_traffic=sum(opp.get('estimated_traffic', 0) for opp in cluster_opportunities),
                difficulty_score=60.0,
                opportunity_score=85.0,
                roi_estimate=200.0,
                success_metrics=[
                    f"Rank in top 5 for '{topic}' and related terms",
                    "Increase overall domain authority",
                    "Improve internal linking structure"
                ],
                action_steps=[
                    "1. Create comprehensive pillar page for main topic",
                    "2. Develop supporting cluster content",
                    "3. Implement strategic internal linking",
                    "4. Monitor cluster performance"
                ],
                resources_needed=[
                    "Senior content strategist",
                    "SEO specialist",
                    "Content writers (2-3)",
                    "Web developer for internal linking"
                ],
                timeline='4-6 weeks',
                related_suggestions=[opp.get('opportunity_id', '') for opp in cluster_opportunities],
                competitor_examples=[],
                content_outline=[
                    'Comprehensive topic overview',
                    'Key subtopics and concepts',
                    'Links to supporting content',
                    'Expert insights and analysis',
                    'Actionable takeaways'
                ],
                seo_recommendations=[
                    'Create topic-focused URL structure',
                    'Implement strategic internal linking',
                    'Optimize for topic authority',
                    'Use schema markup for content clusters'
                ],
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(days=60)
            )

            return suggestion

        except Exception as e:
            logger.error(f"Error creating topic cluster suggestion: {str(e)}")
            return None

    def _create_content_calendar_suggestion(self, opportunities: List[Dict], website: Website) -> Optional[ContentSuggestion]:
        """Create a content calendar strategy suggestion"""
        try:
            if len(opportunities) < 5:
                return None

            # Prioritize opportunities for calendar
            high_priority_opps = [opp for opp in opportunities if opp.get('opportunity_score', 0) > 60]

            suggestion = ContentSuggestion(
                suggestion_id=f"calendar_{website.id}",
                suggestion_type=SuggestionType.NEW_CONTENT,
                priority=Priority.MEDIUM,
                title="Develop strategic content calendar",
                description=f"Create a 3-month content calendar with {len(high_priority_opps)} high-priority pieces",
                rationale="A strategic content calendar ensures consistent publishing and maximizes SEO impact",
                target_keywords=[],
                primary_keyword="content strategy",
                content_type='strategy',
                suggested_length=0,
                estimated_effort_hours=16,
                estimated_traffic=sum(opp.get('estimated_traffic', 0) for opp in high_priority_opps),
                difficulty_score=30.0,
                opportunity_score=75.0,
                roi_estimate=150.0,
                success_metrics=[
                    "Consistent weekly publishing schedule",
                    "Improved overall organic traffic",
                    "Better content performance tracking"
                ],
                action_steps=[
                    "1. Prioritize content opportunities",
                    "2. Create publishing schedule",
                    "3. Assign content creation tasks",
                    "4. Set up performance tracking"
                ],
                resources_needed=[
                    "Content strategist",
                    "Project manager",
                    "Content creation team"
                ],
                timeline='2-3 weeks to plan, 3 months to execute',
                related_suggestions=[],
                competitor_examples=[],
                content_outline=[
                    'Content audit and gap analysis',
                    'Priority-based content roadmap',
                    'Publishing schedule and deadlines',
                    'Performance tracking framework'
                ],
                seo_recommendations=[
                    'Balance keyword difficulty across calendar',
                    'Plan internal linking opportunities',
                    'Schedule content updates and refreshes'
                ],
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(days=45)
            )

            return suggestion

        except Exception as e:
            logger.error(f"Error creating content calendar suggestion: {str(e)}")
            return None

    def _create_competitive_response_suggestion(self, opportunities: List[Dict], website: Website) -> Optional[ContentSuggestion]:
        """Create a competitive response strategy suggestion"""
        try:
            # Find opportunities with high competitor usage
            competitive_opps = [
                opp for opp in opportunities
                if opp.get('competition_level') == 'high' and opp.get('opportunity_score', 0) > 50
            ]

            if len(competitive_opps) < 2:
                return None

            suggestion = ContentSuggestion(
                suggestion_id=f"competitive_{website.id}",
                suggestion_type=SuggestionType.COMPETITIVE_RESPONSE,
                priority=Priority.HIGH,
                title="Develop competitive response strategy",
                description=f"Create superior content to outrank competitors in {len(competitive_opps)} high-value areas",
                rationale="Targeting competitor weaknesses with superior content can capture significant market share",
                target_keywords=[opp.get('primary_keyword', '') for opp in competitive_opps[:5]],
                primary_keyword="competitive strategy",
                content_type='strategy',
                suggested_length=0,
                estimated_effort_hours=60,
                estimated_traffic=sum(opp.get('estimated_traffic', 0) for opp in competitive_opps),
                difficulty_score=75.0,
                opportunity_score=80.0,
                roi_estimate=250.0,
                success_metrics=[
                    "Outrank competitors for target keywords",
                    "Capture competitor traffic",
                    "Improve brand visibility"
                ],
                action_steps=[
                    "1. Analyze competitor content gaps",
                    "2. Create superior content strategy",
                    "3. Develop high-quality content",
                    "4. Implement aggressive promotion"
                ],
                resources_needed=[
                    "Competitive intelligence analyst",
                    "Senior content strategist",
                    "Expert content creators",
                    "SEO and promotion specialists"
                ],
                timeline='6-8 weeks',
                related_suggestions=[],
                competitor_examples=[],
                content_outline=[
                    'Competitor content analysis',
                    'Content gap identification',
                    'Superior content strategy',
                    'Execution and promotion plan'
                ],
                seo_recommendations=[
                    'Target competitor keyword gaps',
                    'Create more comprehensive content',
                    'Build superior backlink profile',
                    'Optimize for user experience'
                ],
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(days=30)
            )

            return suggestion

        except Exception as e:
            logger.error(f"Error creating competitive response suggestion: {str(e)}")
            return None
