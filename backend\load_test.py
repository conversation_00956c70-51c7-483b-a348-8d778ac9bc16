#!/usr/bin/env python3
"""
Load Testing Script for LinkUp Backend
Simulates high-load scenarios to test system performance and scalability
"""
import asyncio
import aiohttp
import time
import json
import statistics
import sys
import os
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import queue
import psutil

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))


class LoadTestConfig:
    """Configuration for load testing"""
    
    def __init__(self):
        # Test configuration
        self.base_url = 'http://localhost:5000'  # Backend API URL
        self.concurrent_users = 10
        self.requests_per_user = 20
        self.ramp_up_time = 30  # seconds
        self.test_duration = 300  # 5 minutes
        
        # Performance thresholds
        self.max_response_time = 5.0  # seconds
        self.max_error_rate = 5.0  # percentage
        self.min_throughput = 10  # requests per second
        
        # Test endpoints
        self.endpoints = [
            {
                'path': '/api/content-suggestions',
                'method': 'GET',
                'params': {'website_id': 1},
                'weight': 30  # 30% of requests
            },
            {
                'path': '/api/keyword-gaps',
                'method': 'POST',
                'data': {'website_id': 1, 'competitor_domains': ['competitor1.com']},
                'weight': 25
            },
            {
                'path': '/api/trending-topics',
                'method': 'GET',
                'params': {'niche': 'technology', 'timeframe': '7d'},
                'weight': 20
            },
            {
                'path': '/api/content-analysis',
                'method': 'POST',
                'data': {'content_url': 'https://example.com/content', 'target_keywords': ['test']},
                'weight': 15
            },
            {
                'path': '/api/health',
                'method': 'GET',
                'weight': 10
            }
        ]


class LoadTestResult:
    """Container for load test results"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.response_times = []
        self.errors = []
        self.throughput_data = []
        self.resource_usage = []


class LoadTester:
    """Main load testing class"""
    
    def __init__(self, config: LoadTestConfig):
        self.config = config
        self.result = LoadTestResult()
        self.request_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.stop_event = threading.Event()
        
    async def run_load_test(self):
        """Run the complete load test"""
        print("🚀 LinkUp Load Testing Suite")
        print("=" * 50)
        print(f"Test Configuration:")
        print(f"  Concurrent Users: {self.config.concurrent_users}")
        print(f"  Requests per User: {self.config.requests_per_user}")
        print(f"  Test Duration: {self.config.test_duration}s")
        print(f"  Base URL: {self.config.base_url}")
        print()
        
        # Start resource monitoring
        resource_monitor = threading.Thread(target=self._monitor_resources)
        resource_monitor.daemon = True
        resource_monitor.start()
        
        # Start throughput monitoring
        throughput_monitor = threading.Thread(target=self._monitor_throughput)
        throughput_monitor.daemon = True
        throughput_monitor.start()
        
        self.result.start_time = time.time()
        
        try:
            # Run load test with asyncio
            await self._run_async_load_test()
        except KeyboardInterrupt:
            print("\n⚠️ Load test interrupted by user")
        finally:
            self.stop_event.set()
            self.result.end_time = time.time()
        
        # Generate and display results
        self._generate_load_test_report()
        
        return self.result
    
    async def _run_async_load_test(self):
        """Run asynchronous load test"""
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=50)
        timeout = aiohttp.ClientTimeout(total=30)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # Create tasks for concurrent users
            tasks = []
            
            for user_id in range(self.config.concurrent_users):
                # Stagger user start times for ramp-up
                delay = (user_id * self.config.ramp_up_time) / self.config.concurrent_users
                task = asyncio.create_task(self._simulate_user(session, user_id, delay))
                tasks.append(task)
            
            # Wait for all users to complete or timeout
            try:
                await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=self.config.test_duration + self.config.ramp_up_time
                )
            except asyncio.TimeoutError:
                print("⏰ Load test timed out")
                # Cancel remaining tasks
                for task in tasks:
                    if not task.done():
                        task.cancel()
    
    async def _simulate_user(self, session: aiohttp.ClientSession, user_id: int, delay: float):
        """Simulate a single user's behavior"""
        # Wait for ramp-up delay
        await asyncio.sleep(delay)
        
        print(f"👤 User {user_id} started")
        
        for request_num in range(self.config.requests_per_user):
            if self.stop_event.is_set():
                break
            
            # Select endpoint based on weights
            endpoint = self._select_weighted_endpoint()
            
            # Make request
            await self._make_request(session, endpoint, user_id, request_num)
            
            # Random think time between requests (0.5-2 seconds)
            think_time = 0.5 + (1.5 * (hash(f"{user_id}_{request_num}") % 100) / 100)
            await asyncio.sleep(think_time)
        
        print(f"✅ User {user_id} completed")
    
    def _select_weighted_endpoint(self):
        """Select endpoint based on configured weights"""
        import random
        
        total_weight = sum(ep['weight'] for ep in self.config.endpoints)
        random_weight = random.randint(1, total_weight)
        
        current_weight = 0
        for endpoint in self.config.endpoints:
            current_weight += endpoint['weight']
            if random_weight <= current_weight:
                return endpoint
        
        return self.config.endpoints[0]  # Fallback
    
    async def _make_request(self, session: aiohttp.ClientSession, endpoint: dict, user_id: int, request_num: int):
        """Make a single HTTP request"""
        url = f"{self.config.base_url}{endpoint['path']}"
        method = endpoint['method']
        
        start_time = time.time()
        
        try:
            if method == 'GET':
                params = endpoint.get('params', {})
                async with session.get(url, params=params) as response:
                    await response.text()  # Read response body
                    status_code = response.status
            
            elif method == 'POST':
                data = endpoint.get('data', {})
                headers = {'Content-Type': 'application/json'}
                async with session.post(url, json=data, headers=headers) as response:
                    await response.text()  # Read response body
                    status_code = response.status
            
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # Record result
            self.result.total_requests += 1
            self.result.response_times.append(response_time)
            
            if 200 <= status_code < 400:
                self.result.successful_requests += 1
            else:
                self.result.failed_requests += 1
                self.result.errors.append({
                    'user_id': user_id,
                    'request_num': request_num,
                    'endpoint': endpoint['path'],
                    'status_code': status_code,
                    'response_time': response_time
                })
        
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            
            self.result.total_requests += 1
            self.result.failed_requests += 1
            self.result.response_times.append(response_time)
            self.result.errors.append({
                'user_id': user_id,
                'request_num': request_num,
                'endpoint': endpoint['path'],
                'error': str(e),
                'response_time': response_time
            })
    
    def _monitor_resources(self):
        """Monitor system resource usage during test"""
        process = psutil.Process()
        
        while not self.stop_event.is_set():
            try:
                cpu_percent = process.cpu_percent()
                memory_info = process.memory_info()
                
                self.result.resource_usage.append({
                    'timestamp': time.time(),
                    'cpu_percent': cpu_percent,
                    'memory_rss': memory_info.rss,
                    'memory_vms': memory_info.vms
                })
                
                time.sleep(1)  # Sample every second
            except Exception as e:
                print(f"Resource monitoring error: {e}")
                break
    
    def _monitor_throughput(self):
        """Monitor request throughput during test"""
        last_request_count = 0
        
        while not self.stop_event.is_set():
            time.sleep(5)  # Sample every 5 seconds
            
            current_request_count = self.result.total_requests
            requests_in_period = current_request_count - last_request_count
            throughput = requests_in_period / 5.0  # requests per second
            
            self.result.throughput_data.append({
                'timestamp': time.time(),
                'throughput': throughput,
                'total_requests': current_request_count
            })
            
            last_request_count = current_request_count
    
    def _generate_load_test_report(self):
        """Generate comprehensive load test report"""
        print("\n📊 LOAD TEST RESULTS")
        print("=" * 50)
        
        # Basic statistics
        total_time = self.result.end_time - self.result.start_time
        success_rate = (self.result.successful_requests / self.result.total_requests * 100) if self.result.total_requests > 0 else 0
        error_rate = (self.result.failed_requests / self.result.total_requests * 100) if self.result.total_requests > 0 else 0
        avg_throughput = self.result.total_requests / total_time if total_time > 0 else 0
        
        print(f"Test Duration: {total_time:.2f} seconds")
        print(f"Total Requests: {self.result.total_requests}")
        print(f"Successful Requests: {self.result.successful_requests}")
        print(f"Failed Requests: {self.result.failed_requests}")
        print(f"Success Rate: {success_rate:.2f}%")
        print(f"Error Rate: {error_rate:.2f}%")
        print(f"Average Throughput: {avg_throughput:.2f} req/s")
        print()
        
        # Response time statistics
        if self.result.response_times:
            print("Response Time Statistics:")
            print("-" * 25)
            print(f"Average: {statistics.mean(self.result.response_times):.3f}s")
            print(f"Median: {statistics.median(self.result.response_times):.3f}s")
            print(f"Min: {min(self.result.response_times):.3f}s")
            print(f"Max: {max(self.result.response_times):.3f}s")
            
            # Percentiles
            sorted_times = sorted(self.result.response_times)
            p95 = sorted_times[int(0.95 * len(sorted_times))]
            p99 = sorted_times[int(0.99 * len(sorted_times))]
            
            print(f"95th Percentile: {p95:.3f}s")
            print(f"99th Percentile: {p99:.3f}s")
            print()
        
        # Resource usage statistics
        if self.result.resource_usage:
            cpu_usage = [r['cpu_percent'] for r in self.result.resource_usage]
            memory_usage = [r['memory_rss'] / 1024 / 1024 for r in self.result.resource_usage]  # MB
            
            print("Resource Usage Statistics:")
            print("-" * 26)
            print(f"Average CPU: {statistics.mean(cpu_usage):.1f}%")
            print(f"Peak CPU: {max(cpu_usage):.1f}%")
            print(f"Average Memory: {statistics.mean(memory_usage):.1f} MB")
            print(f"Peak Memory: {max(memory_usage):.1f} MB")
            print()
        
        # Throughput statistics
        if self.result.throughput_data:
            throughputs = [t['throughput'] for t in self.result.throughput_data]
            print("Throughput Statistics:")
            print("-" * 21)
            print(f"Average Throughput: {statistics.mean(throughputs):.2f} req/s")
            print(f"Peak Throughput: {max(throughputs):.2f} req/s")
            print(f"Min Throughput: {min(throughputs):.2f} req/s")
            print()
        
        # Error analysis
        if self.result.errors:
            print("Error Analysis:")
            print("-" * 15)
            
            # Group errors by type
            error_types = {}
            for error in self.result.errors:
                error_key = error.get('status_code', error.get('error', 'Unknown'))
                error_types[error_key] = error_types.get(error_key, 0) + 1
            
            for error_type, count in error_types.items():
                percentage = (count / len(self.result.errors)) * 100
                print(f"  {error_type}: {count} ({percentage:.1f}%)")
            print()
        
        # Performance assessment
        print("Performance Assessment:")
        print("-" * 23)
        
        # Check against thresholds
        avg_response_time = statistics.mean(self.result.response_times) if self.result.response_times else 0
        
        assessments = []
        
        if avg_response_time <= self.config.max_response_time:
            assessments.append("✅ Response time within acceptable limits")
        else:
            assessments.append(f"❌ Response time too high ({avg_response_time:.2f}s > {self.config.max_response_time}s)")
        
        if error_rate <= self.config.max_error_rate:
            assessments.append("✅ Error rate within acceptable limits")
        else:
            assessments.append(f"❌ Error rate too high ({error_rate:.1f}% > {self.config.max_error_rate}%)")
        
        if avg_throughput >= self.config.min_throughput:
            assessments.append("✅ Throughput meets minimum requirements")
        else:
            assessments.append(f"❌ Throughput below minimum ({avg_throughput:.1f} < {self.config.min_throughput} req/s)")
        
        for assessment in assessments:
            print(assessment)
        
        print()
        
        # Overall verdict
        all_passed = all("✅" in assessment for assessment in assessments)
        
        if all_passed:
            print("🎉 LOAD TEST PASSED! System performs well under load.")
        else:
            print("⚠️ LOAD TEST FAILED! Performance optimization needed.")
        
        print("=" * 50)


async def main():
    """Main function to run load tests"""
    # Parse command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == '--help':
            print("LinkUp Load Testing Script")
            print("Usage: python load_test.py [--light|--medium|--heavy]")
            print("  --light:  10 users, 10 requests each")
            print("  --medium: 25 users, 20 requests each")
            print("  --heavy:  50 users, 50 requests each")
            return
    
    # Configure test based on arguments
    config = LoadTestConfig()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--light':
            config.concurrent_users = 10
            config.requests_per_user = 10
            config.test_duration = 120
        elif sys.argv[1] == '--medium':
            config.concurrent_users = 25
            config.requests_per_user = 20
            config.test_duration = 300
        elif sys.argv[1] == '--heavy':
            config.concurrent_users = 50
            config.requests_per_user = 50
            config.test_duration = 600
    
    # Run load test
    tester = LoadTester(config)
    result = await tester.run_load_test()
    
    # Exit with appropriate code
    success_rate = (result.successful_requests / result.total_requests * 100) if result.total_requests > 0 else 0
    exit(0 if success_rate >= 95 else 1)


if __name__ == '__main__':
    asyncio.run(main())
