"""
Decorators for LinkUp Plugin
"""
import functools
import time
from flask import request, jsonify, g
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity
from app.services.auth_service import AuthenticationService
from app.models.usage_stats import UsageStats
import logging

logger = logging.getLogger(__name__)


def require_auth(f):
    """Decorator to require authentication"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Try JWT authentication first
            verify_jwt_in_request()
            user_id = get_jwt_identity()
            if user_id:
                from app.models.user import User
                g.current_user = User.query.get(user_id)
                if g.current_user and g.current_user.is_active:
                    return f(*args, **kwargs)
        except Exception:
            pass
        
        # Try API key authentication
        result = AuthenticationService.authenticate_request(request)
        if len(result) == 3:  # Successful authentication
            user, auth_method, api_key = result
            g.current_user = user
            g.auth_method = auth_method
            g.api_key = api_key
            return f(*args, **kwargs)
        
        return jsonify({
            'success': False,
            'error': 'Authentication required'
        }), 401
    
    return decorated_function


def require_admin(f):
    """Decorator to require admin role"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(g, 'current_user') or not g.current_user:
            return jsonify({
                'success': False,
                'error': 'Authentication required'
            }), 401
        
        if g.current_user.role != 'admin':
            return jsonify({
                'success': False,
                'error': 'Admin access required'
            }), 403
        
        return f(*args, **kwargs)
    
    return decorated_function


def rate_limit(max_requests=100, per_seconds=3600):
    """Decorator for rate limiting"""
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            if not hasattr(g, 'current_user') or not g.current_user:
                return f(*args, **kwargs)
            
            # Check rate limit
            within_limits, status = AuthenticationService.check_rate_limit(g.current_user)
            if not within_limits:
                return jsonify({
                    'success': False,
                    'error': f'Rate limit exceeded: {status}'
                }), 429
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def log_api_usage(f):
    """Decorator to log API usage"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = f(*args, **kwargs)
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            # Log successful API call
            if hasattr(g, 'current_user') and g.current_user:
                website_id = getattr(g, 'current_website_id', None)
                UsageStats.record_api_request(
                    g.current_user.id,
                    website_id,
                    response_time
                )
            
            return result
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            
            # Log failed API call
            if hasattr(g, 'current_user') and g.current_user:
                website_id = getattr(g, 'current_website_id', None)
                UsageStats.record_api_request(
                    g.current_user.id,
                    website_id,
                    response_time,
                    error=str(e)
                )
            
            raise
    
    return decorated_function


def validate_json(*required_fields):
    """Decorator to validate JSON request data"""
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return jsonify({
                    'success': False,
                    'error': 'Request must be JSON'
                }), 400
            
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'error': 'Invalid JSON data'
                }), 400
            
            # Check required fields
            missing_fields = []
            for field in required_fields:
                if field not in data or data[field] is None or data[field] == '':
                    missing_fields.append(field)
            
            if missing_fields:
                return jsonify({
                    'success': False,
                    'error': f'Missing required fields: {", ".join(missing_fields)}'
                }), 400
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def handle_errors(f):
    """Decorator to handle common errors"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValueError as e:
            logger.error(f"ValueError in {f.__name__}: {str(e)}")
            return jsonify({
                'success': False,
                'error': 'Invalid input data'
            }), 400
        except KeyError as e:
            logger.error(f"KeyError in {f.__name__}: {str(e)}")
            return jsonify({
                'success': False,
                'error': f'Missing required field: {str(e)}'
            }), 400
        except Exception as e:
            logger.error(f"Unexpected error in {f.__name__}: {str(e)}")
            return jsonify({
                'success': False,
                'error': 'Internal server error'
            }), 500
    
    return decorated_function


def cache_result(timeout=300):
    """Decorator to cache function results"""
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # Simple caching implementation
            # In a real application, you'd use Redis or similar
            cache_key = f"{f.__name__}:{hash(str(args) + str(kwargs))}"
            
            # For now, just execute the function
            # TODO: Implement actual caching with Redis
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def require_plan(*allowed_plans):
    """Decorator to require specific user plans"""
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            if not hasattr(g, 'current_user') or not g.current_user:
                return jsonify({
                    'success': False,
                    'error': 'Authentication required'
                }), 401
            
            if g.current_user.plan not in allowed_plans:
                return jsonify({
                    'success': False,
                    'error': f'This feature requires one of these plans: {", ".join(allowed_plans)}'
                }), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def measure_performance(f):
    """Decorator to measure function performance"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        result = f(*args, **kwargs)
        end_time = time.time()
        
        execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
        logger.info(f"Function {f.__name__} executed in {execution_time:.2f}ms")
        
        return result
    
    return decorated_function


def retry(max_attempts=3, delay=1):
    """Decorator to retry function execution on failure"""
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return f(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        time.sleep(delay)
                        logger.warning(f"Attempt {attempt + 1} failed for {f.__name__}: {str(e)}")
                    else:
                        logger.error(f"All {max_attempts} attempts failed for {f.__name__}")
            
            raise last_exception
        
        return decorated_function
    return decorator
