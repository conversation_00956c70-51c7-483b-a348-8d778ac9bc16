"""
Integration Tests for Sprint 7 Features
Tests the integration between different Sprint 7 services and components
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime, timedelta
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.competitor_analysis_service import CompetitorAnalysisService
from app.services.keyword_gap_service import KeywordGapService, GapType
from app.services.content_opportunity_service import ContentOpportunityService
from app.services.suggestion_generation_service import SuggestionGenerationService
from app.services.keyword_research_api_service import KeywordResearchAPIService, APIProvider
from app.services.content_optimization_service import ContentOptimizationService
from app.services.trending_topics_service import TrendingTopicsService, TrendTimeframe


class TestSprintSevenIntegration(unittest.TestCase):
    """Integration tests for Sprint 7 services working together"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.competitor_service = CompetitorAnalysisService()
        self.keyword_gap_service = KeywordGapService()
        self.opportunity_service = ContentOpportunityService()
        self.suggestion_service = SuggestionGenerationService()
        self.keyword_api_service = KeywordResearchAPIService()
        self.optimization_service = ContentOptimizationService()
        self.trending_service = TrendingTopicsService()
        
        # Mock website
        self.mock_website = Mock()
        self.mock_website.id = 1
        self.mock_website.domain = 'example.com'
        self.mock_website.category = 'technology'
        self.mock_website.domain_authority = 50
    
    @patch('app.models.website.Website.query')
    @patch('app.services.competitor_analysis_service.cache')
    def test_competitor_analysis_to_keyword_gaps_integration(self, mock_cache, mock_query):
        """Test integration from competitor analysis to keyword gap identification"""
        # Setup
        mock_query.get.return_value = self.mock_website
        mock_cache.get.return_value = None
        
        # Mock competitor analysis result
        competitor_analysis = {
            'analysis_summary': {
                'competitors_analyzed': 3,
                'total_keywords_found': 150,
                'content_gaps_identified': 25
            },
            'competitor_details': {
                'competitor1.com': {
                    'keyword_analysis': {
                        'content_keywords': [
                            {'keyword': 'ai automation', 'frequency': 15, 'context': 'technology'},
                            {'keyword': 'machine learning tools', 'frequency': 10, 'context': 'software'}
                        ]
                    }
                },
                'competitor2.com': {
                    'keyword_analysis': {
                        'content_keywords': [
                            {'keyword': 'ai automation', 'frequency': 12, 'context': 'technology'},
                            {'keyword': 'data science', 'frequency': 8, 'context': 'analytics'}
                        ]
                    }
                }
            }
        }
        
        # Mock the competitor analysis service to return our test data
        with patch.object(self.competitor_service, 'analyze_competitors', return_value=competitor_analysis):
            # Test keyword gap service using competitor analysis
            with patch.object(self.keyword_gap_service, '_get_competitor_analysis', return_value=competitor_analysis):
                gaps = self.keyword_gap_service.analyze_keyword_gaps(1, ['competitor1.com', 'competitor2.com'])
        
        # Assertions
        self.assertIn('keyword_gaps', gaps)
        self.assertIsInstance(gaps['keyword_gaps'], list)
        
        # Should identify 'ai automation' as a gap since it appears in multiple competitors
        keyword_gaps = gaps['keyword_gaps']
        ai_automation_gap = next((gap for gap in keyword_gaps if 'ai automation' in gap['keyword']), None)
        self.assertIsNotNone(ai_automation_gap, "Should identify 'ai automation' as a keyword gap")
    
    def test_keyword_gaps_to_content_opportunities_integration(self):
        """Test integration from keyword gaps to content opportunities"""
        # Create mock keyword gaps
        mock_keyword_gaps = [
            {
                'keyword': 'ai automation tools',
                'gap_type': 'missing_keyword',
                'opportunity_score': 85.0,
                'difficulty_score': 45.0,
                'search_volume_estimate': 2000,
                'competitor_usage': ['competitor1.com', 'competitor2.com'],
                'suggested_content_type': 'guide',
                'priority_level': 'high',
                'reasoning': 'High opportunity keyword with moderate competition',
                'related_keywords': ['ai tools', 'automation software'],
                'semantic_cluster': 'technology'
            }
        ]
        
        # Mock the opportunity service to use keyword gaps
        with patch.object(self.opportunity_service.keyword_gap_service, 'analyze_keyword_gaps') as mock_gaps:
            mock_gaps.return_value = {
                'keyword_gaps': mock_keyword_gaps,
                'content_gaps': [],
                'long_tail_opportunities': [],
                'semantic_gaps': []
            }
            
            # Test content opportunity scoring
            opportunities = self.opportunity_service.score_content_opportunities(1)
        
        # Assertions
        self.assertIn('opportunities', opportunities)
        self.assertGreater(len(opportunities['opportunities']), 0)
        
        # Check that the keyword gap was converted to an opportunity
        first_opportunity = opportunities['opportunities'][0]
        self.assertIn('ai automation tools', first_opportunity['title'])
        self.assertEqual(first_opportunity['opportunity_score'], 85.0)
    
    def test_content_opportunities_to_suggestions_integration(self):
        """Test integration from content opportunities to actionable suggestions"""
        # Mock content opportunities
        mock_opportunities = {
            'opportunities': [
                {
                    'opportunity_id': 'opp_1',
                    'opportunity_type': 'keyword_targeting',
                    'title': 'Target "ai automation tools" keyword',
                    'description': 'Create content optimized for ai automation tools',
                    'target_keywords': ['ai automation tools', 'automation software'],
                    'primary_keyword': 'ai automation tools',
                    'opportunity_score': 85.0,
                    'difficulty_score': 45.0,
                    'impact_score': 75.0,
                    'urgency_score': 80.0,
                    'roi_estimate': 150.0,
                    'estimated_traffic': 2000,
                    'competition_level': 'medium',
                    'content_type': 'guide',
                    'suggested_length': 3000,
                    'estimated_effort_hours': 16
                }
            ]
        }
        
        # Mock the opportunity service
        with patch.object(self.suggestion_service.opportunity_service, 'score_content_opportunities') as mock_opps:
            mock_opps.return_value = mock_opportunities
            
            # Test suggestion generation
            suggestions = self.suggestion_service.generate_content_suggestions(1)
        
        # Assertions
        self.assertIn('suggestions', suggestions)
        self.assertGreater(len(suggestions['suggestions']), 0)
        
        # Check that opportunity was converted to actionable suggestion
        first_suggestion = suggestions['suggestions'][0]
        self.assertIn('content_outline', first_suggestion)
        self.assertIn('seo_recommendations', first_suggestion)
        self.assertIn('action_steps', first_suggestion)
        self.assertGreater(len(first_suggestion['action_steps']), 5)
    
    def test_keyword_research_api_integration(self):
        """Test integration with keyword research APIs"""
        # Test mock API integration
        keyword_data = self.keyword_api_service.get_keyword_data('ai automation', APIProvider.MOCK_API)
        
        # Assertions
        self.assertIsNotNone(keyword_data)
        self.assertEqual(keyword_data.keyword, 'ai automation')
        self.assertGreater(keyword_data.search_volume, 0)
        self.assertGreater(len(keyword_data.related_keywords), 0)
        self.assertGreater(len(keyword_data.questions), 0)
        
        # Test keyword suggestions
        suggestions = self.keyword_api_service.get_keyword_suggestions('ai automation', APIProvider.MOCK_API, 10)
        
        self.assertIsInstance(suggestions, list)
        self.assertLessEqual(len(suggestions), 10)
        self.assertTrue(all('ai automation' in kw.keyword or 'automation' in kw.keyword for kw in suggestions[:3]))
    
    def test_content_optimization_integration(self):
        """Test content optimization service integration"""
        # Test content with optimization opportunities
        test_content = """
# AI Automation Guide

This is a short guide about AI automation. AI automation is important.

## Benefits
- Saves time
- Reduces errors

AI automation tools are useful.
"""
        
        target_keywords = ['ai automation', 'automation tools']
        
        # Test content analysis
        analysis = self.optimization_service.analyze_content(
            'https://example.com/ai-automation-guide',
            test_content,
            target_keywords
        )
        
        # Assertions
        self.assertGreater(analysis.overall_score, 0)
        self.assertGreater(len(analysis.recommendations), 0)
        
        # Should identify content length issue
        length_rec = next((rec for rec in analysis.recommendations if 'short' in rec.title.lower()), None)
        self.assertIsNotNone(length_rec, "Should identify content length issue")
        
        # Should have keyword density analysis
        self.assertIn('ai automation', analysis.keyword_density)
    
    def test_trending_topics_integration(self):
        """Test trending topics service integration"""
        # Test trending topics identification
        trends = self.trending_service.identify_trending_topics('technology', TrendTimeframe.LAST_7D)
        
        # Assertions
        self.assertEqual(trends.niche, 'technology')
        self.assertGreater(len(trends.trending_topics), 0)
        self.assertGreater(len(trends.emerging_keywords), 0)
        self.assertGreater(len(trends.recommended_actions), 0)
        
        # Check trending topic properties
        first_topic = trends.trending_topics[0]
        self.assertIn('technology', first_topic.topic.lower())
        self.assertGreater(first_topic.trend_score, 0)
        self.assertGreater(len(first_topic.content_opportunities), 0)
    
    def test_end_to_end_workflow_integration(self):
        """Test complete end-to-end workflow integration"""
        # This test simulates the complete workflow from competitor analysis to suggestions
        
        # Step 1: Mock competitor analysis
        competitor_analysis = {
            'competitor_details': {
                'competitor1.com': {
                    'keyword_analysis': {
                        'content_keywords': [
                            {'keyword': 'ai automation best practices', 'frequency': 20, 'context': 'guide'}
                        ]
                    }
                }
            }
        }
        
        # Step 2: Mock keyword gap analysis
        with patch.object(self.keyword_gap_service, '_get_competitor_analysis', return_value=competitor_analysis):
            with patch.object(self.keyword_gap_service, '_get_source_keywords', return_value=set()):
                gaps = self.keyword_gap_service.analyze_keyword_gaps(1, ['competitor1.com'])
        
        # Step 3: Mock content opportunity scoring
        with patch.object(self.opportunity_service.keyword_gap_service, 'analyze_keyword_gaps', return_value=gaps):
            opportunities = self.opportunity_service.score_content_opportunities(1)
        
        # Step 4: Generate suggestions
        with patch.object(self.suggestion_service.opportunity_service, 'score_content_opportunities', return_value=opportunities):
            suggestions = self.suggestion_service.generate_content_suggestions(1)
        
        # Assertions for complete workflow
        self.assertIn('suggestions', suggestions)
        
        if suggestions['suggestions']:
            suggestion = suggestions['suggestions'][0]
            
            # Should have all required suggestion components
            required_fields = [
                'title', 'description', 'target_keywords', 'content_outline',
                'seo_recommendations', 'action_steps', 'opportunity_score'
            ]
            
            for field in required_fields:
                self.assertIn(field, suggestion, f"Suggestion should have {field}")
            
            # Should have actionable content
            self.assertGreater(len(suggestion['action_steps']), 3)
            self.assertGreater(len(suggestion['content_outline']), 2)
            self.assertGreater(len(suggestion['seo_recommendations']), 2)
    
    def test_api_error_handling_integration(self):
        """Test error handling across integrated services"""
        # Test with invalid website ID
        with patch('app.models.website.Website.query') as mock_query:
            mock_query.get.return_value = None
            
            # Should handle missing website gracefully
            gaps = self.keyword_gap_service.analyze_keyword_gaps(999)
            self.assertIn('error', gaps)
            
            opportunities = self.opportunity_service.score_content_opportunities(999)
            self.assertIn('error', opportunities)
            
            suggestions = self.suggestion_service.generate_content_suggestions(999)
            self.assertIn('error', suggestions)
    
    def test_caching_integration(self):
        """Test caching behavior across services"""
        # Test that services properly use caching
        with patch('app.services.competitor_analysis_service.cache') as mock_cache:
            mock_cache.get.return_value = None
            
            # First call should miss cache
            self.competitor_service.analyze_competitors(1)
            mock_cache.get.assert_called()
            mock_cache.set.assert_called()
        
        with patch('app.services.keyword_gap_service.cache') as mock_cache:
            mock_cache.get.return_value = None
            
            # First call should miss cache
            self.keyword_gap_service.analyze_keyword_gaps(1)
            mock_cache.get.assert_called()
            mock_cache.set.assert_called()
    
    def test_performance_integration(self):
        """Test performance characteristics of integrated services"""
        import time
        
        # Test that services complete within reasonable time
        start_time = time.time()
        
        # Run a simplified workflow
        with patch.object(self.keyword_gap_service, '_get_competitor_analysis', return_value={'competitor_details': {}}):
            gaps = self.keyword_gap_service.analyze_keyword_gaps(1, ['competitor1.com'])
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete within 5 seconds (generous for unit tests)
        self.assertLess(execution_time, 5.0, "Keyword gap analysis should complete quickly")
        
        # Test trending topics performance
        start_time = time.time()
        trends = self.trending_service.identify_trending_topics('technology')
        end_time = time.time()
        execution_time = end_time - start_time
        
        self.assertLess(execution_time, 3.0, "Trending topics analysis should complete quickly")


class TestAPIEndpointIntegration(unittest.TestCase):
    """Integration tests for API endpoints with Sprint 7 services"""
    
    def setUp(self):
        """Set up test fixtures for API testing"""
        # This would typically set up a test Flask app
        # For now, we'll test the service integration patterns
        pass
    
    def test_api_response_format_integration(self):
        """Test that services return API-compatible response formats"""
        # Test keyword gap service response format
        keyword_service = KeywordGapService()
        
        with patch.object(keyword_service, '_get_competitor_analysis', return_value={'competitor_details': {}}):
            response = keyword_service.analyze_keyword_gaps(1)
        
        # Should have standard API response structure
        expected_keys = ['keyword_gaps', 'content_gaps', 'analysis_summary']
        for key in expected_keys:
            self.assertIn(key, response, f"Response should contain {key}")
        
        # Test content opportunity service response format
        opportunity_service = ContentOpportunityService()
        
        with patch.object(opportunity_service.keyword_gap_service, 'analyze_keyword_gaps') as mock_gaps:
            mock_gaps.return_value = {'keyword_gaps': [], 'content_gaps': [], 'long_tail_opportunities': []}
            response = opportunity_service.score_content_opportunities(1)
        
        expected_keys = ['opportunities', 'summary_metrics', 'recommendations']
        for key in expected_keys:
            self.assertIn(key, response, f"Response should contain {key}")
    
    def test_data_serialization_integration(self):
        """Test that complex objects can be serialized for API responses"""
        # Test that service responses are JSON serializable
        trending_service = TrendingTopicsService()
        trends = trending_service.identify_trending_topics('technology')
        
        # Should be able to convert to dict for JSON serialization
        trends_dict = trends.__dict__
        
        # Test JSON serialization
        try:
            json.dumps(trends_dict, default=str)  # Use str for datetime objects
        except TypeError as e:
            self.fail(f"Trending topics response should be JSON serializable: {e}")


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add integration test cases
    test_suite.addTest(unittest.makeSuite(TestSprintSevenIntegration))
    test_suite.addTest(unittest.makeSuite(TestAPIEndpointIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"SPRINT 7 INTEGRATION TEST RESULTS")
    print(f"{'='*60}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print(f"\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    # Exit with appropriate code
    exit_code = 0 if len(result.failures) == 0 and len(result.errors) == 0 else 1
    exit(exit_code)
