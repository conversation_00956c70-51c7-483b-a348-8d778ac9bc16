"""
Basic tests to verify test setup and core functionality
"""
import pytest
import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Set environment for testing
os.environ['FLASK_ENV'] = 'testing'
os.environ['FLASK_CONFIG'] = 'testing'


class TestBasicSetup:
    """Test basic application setup and configuration"""

    def test_python_path(self):
        """Test that Python path is set correctly"""
        assert str(backend_dir) in sys.path

    def test_environment_variables(self):
        """Test that environment variables are set"""
        assert os.environ.get('FLASK_ENV') == 'testing'
        assert os.environ.get('FLASK_CONFIG') == 'testing'

    def test_basic_math(self):
        """Test basic functionality"""
        assert 1 + 1 == 2
        assert 2 * 3 == 6

    def test_string_operations(self):
        """Test string operations"""
        test_string = "LinkUp Plugin"
        assert "LinkUp" in test_string
        assert test_string.lower() == "linkup plugin"


class TestBasicFunctionality:
    """Test basic functionality without Flask dependencies"""

    def test_list_operations(self):
        """Test list operations"""
        test_list = [1, 2, 3, 4, 5]
        assert len(test_list) == 5
        assert 3 in test_list
        assert test_list[0] == 1
        assert test_list[-1] == 5

    def test_dictionary_operations(self):
        """Test dictionary operations"""
        test_dict = {'name': 'LinkUp', 'type': 'plugin', 'version': '1.0'}
        assert test_dict['name'] == 'LinkUp'
        assert 'type' in test_dict
        assert len(test_dict) == 3

    def test_file_path_operations(self):
        """Test file path operations"""
        test_path = Path(__file__)
        assert test_path.exists()
        assert test_path.suffix == '.py'
        assert 'test_basic' in test_path.name
