"""
Website model for LinkUp Plugin
"""
from app import db
from datetime import datetime
import uuid


class Website(db.Model):
    """Website model for storing website information"""
    
    __tablename__ = 'websites'
    
    # Primary key
    id = db.Column(db.Integer, primary_key=True)
    
    # Foreign keys
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    
    # Website information
    domain = db.Column(db.String(255), unique=True, nullable=False, index=True)
    title = db.Column(db.String(500), nullable=False)
    description = db.Column(db.Text)
    url = db.Column(db.String(500))
    
    # Categorization
    category = db.Column(db.String(100))
    language = db.Column(db.String(10), default='en')
    
    # Status and metadata
    status = db.Column(db.String(50), default='pending_analysis')  # pending_analysis, active, inactive, analysis_failed
    
    # SEO metrics
    domain_authority = db.Column(db.Integer, default=0)
    page_authority = db.Column(db.Integer, default=0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_analyzed_at = db.Column(db.DateTime)
    
    # Relationships
    user = db.relationship('User', back_populates='websites')
    api_keys = db.relationship('ApiKey', back_populates='website', lazy=True, cascade='all, delete-orphan')
    analyses = db.relationship('ContentAnalysis', back_populates='website', lazy=True, cascade='all, delete-orphan')
    usage_stats = db.relationship('UsageStats', back_populates='website', lazy=True)
    
    def __init__(self, **kwargs):
        """Initialize website"""
        super(Website, self).__init__(**kwargs)
        
        # Auto-generate URL if not provided
        if not self.url and self.domain:
            self.url = f'https://{self.domain}'
    
    def __repr__(self):
        return f'<Website {self.domain}>'
    
    def to_dict(self):
        """Convert website to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'domain': self.domain,
            'title': self.title,
            'description': self.description,
            'url': self.url,
            'category': self.category,
            'language': self.language,
            'status': self.status,
            'domain_authority': self.domain_authority,
            'page_authority': self.page_authority,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_analyzed_at': self.last_analyzed_at.isoformat() if self.last_analyzed_at else None
        }
    
    def get_backlinks_count(self):
        """Get total number of backlinks for this website"""
        # This would query the backlinks table when implemented
        return 0
    
    def get_active_backlinks_count(self):
        """Get number of active backlinks for this website"""
        # This would query the backlinks table when implemented
        return 0
    
    def get_latest_analysis(self):
        """Get the latest content analysis for this website"""
        return self.analyses.order_by(ContentAnalysis.analyzed_at.desc()).first()
    
    def update_seo_metrics(self, domain_authority=None, page_authority=None):
        """Update SEO metrics for the website"""
        if domain_authority is not None:
            self.domain_authority = domain_authority
        if page_authority is not None:
            self.page_authority = page_authority
        
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    @classmethod
    def get_by_domain(cls, domain):
        """Get website by domain"""
        return cls.query.filter_by(domain=domain).first()
    
    @classmethod
    def get_active_websites(cls):
        """Get all active websites"""
        return cls.query.filter_by(status='active').all()
    
    @classmethod
    def get_user_websites(cls, user_id):
        """Get all websites for a specific user"""
        return cls.query.filter_by(user_id=user_id).all()
