"""
Competitor Content Analysis Service
Analyzes competitor content to identify gaps and opportunities
"""
import logging
import requests
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import re
from urllib.parse import urljoin, urlparse
import time

# Handle optional dependencies
try:
    import numpy as np
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False
    # Fallback implementations
    class TfidfVectorizerFallback:
        def fit_transform(self, texts):
            return [[len(text.split()) for text in texts]]
    TfidfVectorizer = TfidfVectorizerFallback
    def cosine_similarity(a, b):
        return [[0.5]]

try:
    from bs4 import BeautifulSoup
    HAS_BS4 = True
except ImportError:
    HAS_BS4 = False

from app import db, cache
from app.models.website import Website
from app.models.analysis import ContentAnalysis
from app.services.advanced_content_matching import AdvancedContentMatcher

logger = logging.getLogger(__name__)


class CompetitorAnalysisService:
    """Service for analyzing competitor content and identifying opportunities"""
    
    def __init__(self):
        """Initialize the competitor analysis service"""
        self.content_matcher = AdvancedContentMatcher()
        self.cache_timeout = 3600 * 24  # 24 hours
        
        # Analysis settings
        self.max_competitors = 10
        self.max_pages_per_competitor = 50
        self.min_content_length = 300
        self.request_timeout = 30
        self.request_delay = 1  # Delay between requests
        
        # Content extraction patterns
        self.content_selectors = [
            'article', 'main', '.content', '#content',
            '.post-content', '.entry-content', '.article-content'
        ]
        
        # Headers for web scraping
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
    
    def analyze_competitors(self, website_id: int, competitor_domains: List[str] = None) -> Dict[str, Any]:
        """Analyze competitors for a given website"""
        try:
            website = Website.query.get(website_id)
            if not website:
                raise ValueError(f"Website {website_id} not found")
            
            # Get competitor domains if not provided
            if not competitor_domains:
                competitor_domains = self._discover_competitors(website)
            
            # Analyze each competitor
            competitor_analyses = {}
            for domain in competitor_domains[:self.max_competitors]:
                try:
                    analysis = self._analyze_competitor_domain(domain, website)
                    if analysis:
                        competitor_analyses[domain] = analysis
                        time.sleep(self.request_delay)  # Rate limiting
                except Exception as e:
                    logger.warning(f"Failed to analyze competitor {domain}: {str(e)}")
                    continue
            
            # Generate comprehensive analysis
            comprehensive_analysis = self._generate_comprehensive_analysis(
                website, competitor_analyses
            )
            
            # Cache the results
            cache_key = f"competitor_analysis_{website_id}"
            cache.set(cache_key, comprehensive_analysis, timeout=self.cache_timeout)
            
            return comprehensive_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing competitors: {str(e)}")
            return {'error': str(e)}
    
    def _discover_competitors(self, website: Website) -> List[str]:
        """Discover competitor domains based on website category and content"""
        try:
            # Get websites in the same category
            similar_websites = Website.query.filter(
                Website.category == website.category,
                Website.id != website.id,
                Website.status == 'active'
            ).order_by(Website.domain_authority.desc()).limit(20).all()
            
            competitor_domains = [w.domain for w in similar_websites]
            
            # Add some well-known competitors based on category
            category_competitors = self._get_category_competitors(website.category)
            competitor_domains.extend(category_competitors)
            
            # Remove duplicates and return
            return list(set(competitor_domains))
            
        except Exception as e:
            logger.error(f"Error discovering competitors: {str(e)}")
            return []
    
    def _get_category_competitors(self, category: str) -> List[str]:
        """Get well-known competitors for a category"""
        category_competitors = {
            'technology': [
                'techcrunch.com', 'wired.com', 'theverge.com', 'ars-technica.com',
                'engadget.com', 'gizmodo.com', 'mashable.com'
            ],
            'business': [
                'forbes.com', 'bloomberg.com', 'businessinsider.com', 'entrepreneur.com',
                'inc.com', 'fastcompany.com', 'harvard.edu'
            ],
            'health': [
                'webmd.com', 'healthline.com', 'mayoclinic.org', 'medicalnewstoday.com',
                'health.com', 'prevention.com'
            ],
            'finance': [
                'investopedia.com', 'fool.com', 'marketwatch.com', 'cnbc.com',
                'bloomberg.com', 'reuters.com'
            ],
            'lifestyle': [
                'buzzfeed.com', 'huffpost.com', 'refinery29.com', 'popsugar.com',
                'elle.com', 'vogue.com'
            ]
        }
        
        return category_competitors.get(category, [])
    
    def _analyze_competitor_domain(self, domain: str, source_website: Website) -> Optional[Dict]:
        """Analyze a single competitor domain"""
        try:
            # Get competitor website info
            competitor = Website.query.filter_by(domain=domain).first()
            
            # Scrape competitor content
            content_data = self._scrape_competitor_content(domain)
            
            if not content_data:
                return None
            
            # Analyze content
            analysis = {
                'domain': domain,
                'competitor_info': {
                    'domain_authority': getattr(competitor, 'domain_authority', 0) if competitor else 0,
                    'category': getattr(competitor, 'category', 'unknown') if competitor else 'unknown',
                    'title': getattr(competitor, 'title', domain) if competitor else domain
                },
                'content_analysis': self._analyze_competitor_content(content_data, source_website),
                'keyword_analysis': self._analyze_competitor_keywords(content_data),
                'content_gaps': self._identify_content_gaps(content_data, source_website),
                'analyzed_at': datetime.utcnow().isoformat()
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing competitor domain {domain}: {str(e)}")
            return None
    
    def _scrape_competitor_content(self, domain: str) -> Optional[Dict]:
        """Scrape content from competitor website"""
        if not HAS_BS4:
            logger.warning("BeautifulSoup not available, skipping content scraping")
            return None
        
        try:
            # Get main page content
            url = f"https://{domain}"
            response = requests.get(url, headers=self.headers, timeout=self.request_timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract main content
            main_content = self._extract_main_content(soup)
            
            # Extract metadata
            metadata = self._extract_metadata(soup)
            
            # Get additional pages (blog posts, articles)
            additional_content = self._scrape_additional_pages(domain, soup)
            
            return {
                'main_content': main_content,
                'metadata': metadata,
                'additional_content': additional_content,
                'total_pages_analyzed': len(additional_content) + 1
            }
            
        except Exception as e:
            logger.error(f"Error scraping content from {domain}: {str(e)}")
            return None
    
    def _extract_main_content(self, soup: BeautifulSoup) -> str:
        """Extract main content from webpage"""
        content_parts = []
        
        # Try different content selectors
        for selector in self.content_selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text(strip=True)
                if len(text) > self.min_content_length:
                    content_parts.append(text)
                    break
            if content_parts:
                break
        
        # Fallback to body content
        if not content_parts:
            body = soup.find('body')
            if body:
                # Remove script and style elements
                for script in body(["script", "style", "nav", "footer", "header"]):
                    script.decompose()
                content_parts.append(body.get_text(strip=True))
        
        return ' '.join(content_parts)
    
    def _extract_metadata(self, soup: BeautifulSoup) -> Dict:
        """Extract metadata from webpage"""
        metadata = {}
        
        # Title
        title_tag = soup.find('title')
        if title_tag:
            metadata['title'] = title_tag.get_text(strip=True)
        
        # Meta description
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            metadata['description'] = meta_desc.get('content', '')
        
        # Meta keywords
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        if meta_keywords:
            metadata['keywords'] = meta_keywords.get('content', '')
        
        # H1 tags
        h1_tags = soup.find_all('h1')
        metadata['h1_tags'] = [h1.get_text(strip=True) for h1 in h1_tags]
        
        # H2 tags
        h2_tags = soup.find_all('h2')
        metadata['h2_tags'] = [h2.get_text(strip=True) for h2 in h2_tags[:10]]  # Limit to first 10
        
        return metadata
    
    def _scrape_additional_pages(self, domain: str, main_soup: BeautifulSoup) -> List[Dict]:
        """Scrape additional pages from the website"""
        additional_content = []
        
        try:
            # Find blog/article links
            links = self._find_content_links(main_soup, domain)
            
            for link in links[:self.max_pages_per_competitor]:
                try:
                    response = requests.get(link, headers=self.headers, timeout=self.request_timeout)
                    response.raise_for_status()
                    
                    soup = BeautifulSoup(response.content, 'html.parser')
                    content = self._extract_main_content(soup)
                    metadata = self._extract_metadata(soup)
                    
                    if len(content) > self.min_content_length:
                        additional_content.append({
                            'url': link,
                            'content': content,
                            'metadata': metadata
                        })
                    
                    time.sleep(self.request_delay)  # Rate limiting
                    
                except Exception as e:
                    logger.warning(f"Failed to scrape {link}: {str(e)}")
                    continue
            
        except Exception as e:
            logger.error(f"Error scraping additional pages: {str(e)}")
        
        return additional_content
    
    def _find_content_links(self, soup: BeautifulSoup, domain: str) -> List[str]:
        """Find links to content pages (blog posts, articles)"""
        content_links = []
        
        # Common patterns for content URLs
        content_patterns = [
            r'/blog/', r'/article/', r'/post/', r'/news/',
            r'/insights/', r'/resources/', r'/guides/'
        ]
        
        # Find all links
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link['href']
            
            # Convert relative URLs to absolute
            if href.startswith('/'):
                href = f"https://{domain}{href}"
            elif not href.startswith('http'):
                continue
            
            # Check if it's a content URL
            if any(pattern in href for pattern in content_patterns):
                content_links.append(href)
        
        return list(set(content_links))  # Remove duplicates
    
    def _analyze_competitor_content(self, content_data: Dict, source_website: Website) -> Dict:
        """Analyze competitor content for insights"""
        try:
            all_content = content_data['main_content']
            
            # Add additional content
            for page in content_data.get('additional_content', []):
                all_content += ' ' + page['content']
            
            # Get source website content for comparison
            source_analysis = ContentAnalysis.query.filter_by(
                website_id=source_website.id
            ).order_by(ContentAnalysis.analyzed_at.desc()).first()
            
            source_content = ""
            if source_analysis and source_analysis.content:
                source_content = source_analysis.content
            
            # Calculate content similarity
            similarity_score = 0.0
            if source_content and HAS_SKLEARN:
                try:
                    vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
                    tfidf_matrix = vectorizer.fit_transform([source_content, all_content])
                    similarity_matrix = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])
                    similarity_score = similarity_matrix[0][0]
                except Exception as e:
                    logger.warning(f"Error calculating similarity: {str(e)}")
            
            # Analyze content characteristics
            content_analysis = {
                'total_content_length': len(all_content),
                'average_page_length': len(all_content) / max(content_data.get('total_pages_analyzed', 1), 1),
                'content_similarity_to_source': similarity_score,
                'content_topics': self._extract_content_topics(all_content),
                'content_quality_indicators': self._assess_content_quality(content_data),
                'content_structure': self._analyze_content_structure(content_data)
            }
            
            return content_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing competitor content: {str(e)}")
            return {}
    
    def _extract_content_topics(self, content: str) -> List[Dict]:
        """Extract main topics from content"""
        try:
            # Simple topic extraction using keyword frequency
            words = re.findall(r'\b[a-zA-Z]{4,}\b', content.lower())
            word_freq = Counter(words)
            
            # Filter out common words
            stop_words = {
                'that', 'this', 'with', 'from', 'they', 'been', 'have', 'their',
                'said', 'each', 'which', 'them', 'than', 'many', 'some', 'time',
                'very', 'when', 'much', 'new', 'also', 'any', 'these', 'way',
                'well', 'because', 'through', 'during', 'before', 'after'
            }
            
            filtered_words = {word: freq for word, freq in word_freq.items() 
                            if word not in stop_words and freq > 2}
            
            # Get top topics
            top_topics = sorted(filtered_words.items(), key=lambda x: x[1], reverse=True)[:20]
            
            return [{'topic': topic, 'frequency': freq} for topic, freq in top_topics]
            
        except Exception as e:
            logger.error(f"Error extracting topics: {str(e)}")
            return []
    
    def _assess_content_quality(self, content_data: Dict) -> Dict:
        """Assess content quality indicators"""
        quality_indicators = {
            'has_meta_description': bool(content_data.get('metadata', {}).get('description')),
            'has_h1_tags': len(content_data.get('metadata', {}).get('h1_tags', [])) > 0,
            'has_h2_tags': len(content_data.get('metadata', {}).get('h2_tags', [])) > 0,
            'average_content_length': 0,
            'content_depth_score': 0
        }
        
        # Calculate average content length
        total_length = len(content_data.get('main_content', ''))
        page_count = 1
        
        for page in content_data.get('additional_content', []):
            total_length += len(page.get('content', ''))
            page_count += 1
        
        quality_indicators['average_content_length'] = total_length / page_count
        
        # Content depth score (based on structure and length)
        depth_score = 0
        if quality_indicators['average_content_length'] > 1000:
            depth_score += 2
        elif quality_indicators['average_content_length'] > 500:
            depth_score += 1
        
        if quality_indicators['has_h1_tags']:
            depth_score += 1
        if quality_indicators['has_h2_tags']:
            depth_score += 1
        if quality_indicators['has_meta_description']:
            depth_score += 1
        
        quality_indicators['content_depth_score'] = depth_score
        
        return quality_indicators
    
    def _analyze_content_structure(self, content_data: Dict) -> Dict:
        """Analyze content structure patterns"""
        structure = {
            'uses_headings': False,
            'heading_hierarchy': [],
            'content_sections': 0,
            'has_structured_content': False
        }
        
        metadata = content_data.get('metadata', {})
        
        # Check heading usage
        h1_tags = metadata.get('h1_tags', [])
        h2_tags = metadata.get('h2_tags', [])
        
        if h1_tags or h2_tags:
            structure['uses_headings'] = True
            structure['heading_hierarchy'] = {
                'h1_count': len(h1_tags),
                'h2_count': len(h2_tags),
                'h1_tags': h1_tags[:5],  # First 5 H1s
                'h2_tags': h2_tags[:10]  # First 10 H2s
            }
        
        # Estimate content sections
        main_content = content_data.get('main_content', '')
        structure['content_sections'] = len(re.split(r'\n\s*\n', main_content))
        
        # Determine if content is well-structured
        structure['has_structured_content'] = (
            structure['uses_headings'] and 
            structure['content_sections'] > 3 and
            len(main_content) > 500
        )
        
        return structure

    def _analyze_competitor_keywords(self, content_data: Dict) -> Dict:
        """Analyze competitor keywords and phrases"""
        try:
            all_content = content_data['main_content']

            # Add additional content
            for page in content_data.get('additional_content', []):
                all_content += ' ' + page['content']

            # Extract keywords from metadata
            meta_keywords = []
            metadata = content_data.get('metadata', {})
            if metadata.get('keywords'):
                meta_keywords = [kw.strip() for kw in metadata['keywords'].split(',')]

            # Extract keywords from titles and headings
            title_keywords = []
            if metadata.get('title'):
                title_keywords = re.findall(r'\b[a-zA-Z]{3,}\b', metadata['title'].lower())

            heading_keywords = []
            for h1 in metadata.get('h1_tags', []):
                heading_keywords.extend(re.findall(r'\b[a-zA-Z]{3,}\b', h1.lower()))
            for h2 in metadata.get('h2_tags', []):
                heading_keywords.extend(re.findall(r'\b[a-zA-Z]{3,}\b', h2.lower()))

            # Extract content keywords using frequency analysis
            content_keywords = self._extract_keywords_from_content(all_content)

            # Extract long-tail keywords (phrases)
            long_tail_keywords = self._extract_long_tail_keywords(all_content)

            keyword_analysis = {
                'meta_keywords': meta_keywords,
                'title_keywords': title_keywords,
                'heading_keywords': list(set(heading_keywords)),
                'content_keywords': content_keywords,
                'long_tail_keywords': long_tail_keywords,
                'total_unique_keywords': len(set(meta_keywords + title_keywords + heading_keywords + [kw['keyword'] for kw in content_keywords]))
            }

            return keyword_analysis

        except Exception as e:
            logger.error(f"Error analyzing competitor keywords: {str(e)}")
            return {}

    def _extract_keywords_from_content(self, content: str) -> List[Dict]:
        """Extract keywords from content using frequency analysis"""
        try:
            # Clean and tokenize content
            words = re.findall(r'\b[a-zA-Z]{3,}\b', content.lower())

            # Filter out common stop words
            stop_words = {
                'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
                'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
                'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy',
                'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use', 'way', 'will',
                'that', 'this', 'with', 'from', 'they', 'been', 'have', 'their',
                'said', 'each', 'which', 'them', 'than', 'many', 'some', 'time',
                'very', 'when', 'much', 'also', 'any', 'these', 'well', 'because',
                'through', 'during', 'before', 'after', 'about', 'would', 'there',
                'could', 'other', 'more', 'what', 'know', 'just', 'first', 'into',
                'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can',
                'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here',
                'through', 'when', 'where', 'why', 'how', 'all', 'any', 'both',
                'each', 'few', 'more', 'most', 'other', 'some', 'such', 'only',
                'own', 'same', 'than', 'too', 'very'
            }

            # Count word frequencies
            word_freq = Counter([word for word in words if word not in stop_words and len(word) > 3])

            # Calculate keyword scores based on frequency and length
            keywords = []
            total_words = len(words)

            for word, freq in word_freq.items():
                if freq >= 3:  # Minimum frequency threshold
                    score = freq / total_words * 1000  # Normalize to per-thousand
                    keywords.append({
                        'keyword': word,
                        'frequency': freq,
                        'score': round(score, 3),
                        'density': round((freq / total_words) * 100, 2)
                    })

            # Sort by score and return top keywords
            keywords.sort(key=lambda x: x['score'], reverse=True)
            return keywords[:50]  # Top 50 keywords

        except Exception as e:
            logger.error(f"Error extracting keywords from content: {str(e)}")
            return []

    def _extract_long_tail_keywords(self, content: str) -> List[Dict]:
        """Extract long-tail keyword phrases"""
        try:
            # Split content into sentences
            sentences = re.split(r'[.!?]+', content)

            # Extract 2-4 word phrases
            phrases = []
            for sentence in sentences:
                words = re.findall(r'\b[a-zA-Z]{3,}\b', sentence.lower())

                # Generate 2-word phrases
                for i in range(len(words) - 1):
                    phrase = ' '.join(words[i:i+2])
                    phrases.append(phrase)

                # Generate 3-word phrases
                for i in range(len(words) - 2):
                    phrase = ' '.join(words[i:i+3])
                    phrases.append(phrase)

                # Generate 4-word phrases
                for i in range(len(words) - 3):
                    phrase = ' '.join(words[i:i+4])
                    phrases.append(phrase)

            # Count phrase frequencies
            phrase_freq = Counter(phrases)

            # Filter and score phrases
            long_tail_keywords = []
            for phrase, freq in phrase_freq.items():
                if freq >= 2 and len(phrase.split()) >= 2:  # Minimum frequency and length
                    long_tail_keywords.append({
                        'phrase': phrase,
                        'frequency': freq,
                        'word_count': len(phrase.split())
                    })

            # Sort by frequency and return top phrases
            long_tail_keywords.sort(key=lambda x: x['frequency'], reverse=True)
            return long_tail_keywords[:30]  # Top 30 long-tail keywords

        except Exception as e:
            logger.error(f"Error extracting long-tail keywords: {str(e)}")
            return []

    def _identify_content_gaps(self, competitor_content: Dict, source_website: Website) -> Dict:
        """Identify content gaps between competitor and source website"""
        try:
            # Get source website content analysis
            source_analysis = ContentAnalysis.query.filter_by(
                website_id=source_website.id
            ).order_by(ContentAnalysis.analyzed_at.desc()).first()

            if not source_analysis:
                return {'error': 'No content analysis available for source website'}

            # Extract source keywords
            source_keywords = set()
            if source_analysis.keywords:
                for kw in source_analysis.keywords.get('primary_keywords', []):
                    source_keywords.add(kw.get('keyword', '').lower())

            # Extract competitor keywords
            competitor_keywords = set()
            keyword_analysis = self._analyze_competitor_keywords(competitor_content)

            for kw in keyword_analysis.get('content_keywords', []):
                competitor_keywords.add(kw['keyword'])

            for kw in keyword_analysis.get('meta_keywords', []):
                competitor_keywords.add(kw.lower())

            # Identify gaps
            missing_keywords = competitor_keywords - source_keywords
            unique_keywords = source_keywords - competitor_keywords
            common_keywords = source_keywords & competitor_keywords

            # Analyze topic gaps
            competitor_topics = self._extract_content_topics(competitor_content['main_content'])
            source_topics = set()
            if source_analysis.topics:
                for topic in source_analysis.topics.get('primary_topics', []):
                    source_topics.add(topic.get('topic', '').lower())

            competitor_topic_set = {topic['topic'] for topic in competitor_topics}
            missing_topics = competitor_topic_set - source_topics

            content_gaps = {
                'keyword_gaps': {
                    'missing_keywords': list(missing_keywords)[:20],  # Top 20 missing
                    'unique_keywords': list(unique_keywords)[:20],    # Top 20 unique to source
                    'common_keywords': list(common_keywords)[:20],    # Top 20 common
                    'gap_percentage': round((len(missing_keywords) / max(len(competitor_keywords), 1)) * 100, 2)
                },
                'topic_gaps': {
                    'missing_topics': list(missing_topics)[:10],
                    'competitor_topics': competitor_topics[:10],
                    'topic_overlap_percentage': round((len(source_topics & competitor_topic_set) / max(len(competitor_topic_set), 1)) * 100, 2)
                },
                'content_length_gap': {
                    'competitor_avg_length': competitor_content.get('total_content_length', 0) / max(competitor_content.get('total_pages_analyzed', 1), 1),
                    'source_content_length': len(source_analysis.content) if source_analysis.content else 0
                }
            }

            return content_gaps

        except Exception as e:
            logger.error(f"Error identifying content gaps: {str(e)}")
            return {'error': str(e)}

    def _generate_comprehensive_analysis(self, source_website: Website, competitor_analyses: Dict) -> Dict:
        """Generate comprehensive analysis from all competitor data"""
        try:
            if not competitor_analyses:
                return {'error': 'No competitor analyses available'}

            # Aggregate keyword data
            all_competitor_keywords = set()
            all_competitor_topics = set()
            total_content_length = 0
            quality_scores = []

            for domain, analysis in competitor_analyses.items():
                # Collect keywords
                keyword_analysis = analysis.get('keyword_analysis', {})
                for kw in keyword_analysis.get('content_keywords', []):
                    all_competitor_keywords.add(kw['keyword'])

                # Collect topics
                content_analysis = analysis.get('content_analysis', {})
                for topic in content_analysis.get('content_topics', []):
                    all_competitor_topics.add(topic['topic'])

                # Collect metrics
                total_content_length += content_analysis.get('total_content_length', 0)

                quality_indicators = content_analysis.get('content_quality_indicators', {})
                quality_scores.append(quality_indicators.get('content_depth_score', 0))

            # Calculate averages
            avg_content_length = total_content_length / len(competitor_analyses)
            avg_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0

            # Generate insights
            comprehensive_analysis = {
                'analysis_summary': {
                    'competitors_analyzed': len(competitor_analyses),
                    'total_keywords_found': len(all_competitor_keywords),
                    'total_topics_found': len(all_competitor_topics),
                    'average_content_length': round(avg_content_length, 0),
                    'average_quality_score': round(avg_quality_score, 2),
                    'analysis_date': datetime.utcnow().isoformat()
                },
                'competitor_details': competitor_analyses,
                'aggregated_insights': {
                    'top_competitor_keywords': list(all_competitor_keywords)[:50],
                    'top_competitor_topics': list(all_competitor_topics)[:20],
                    'content_opportunities': self._generate_content_opportunities(competitor_analyses, source_website),
                    'competitive_advantages': self._identify_competitive_advantages(competitor_analyses, source_website)
                }
            }

            return comprehensive_analysis

        except Exception as e:
            logger.error(f"Error generating comprehensive analysis: {str(e)}")
            return {'error': str(e)}

    def _generate_content_opportunities(self, competitor_analyses: Dict, source_website: Website) -> List[Dict]:
        """Generate content opportunities based on competitor analysis"""
        opportunities = []

        try:
            # Collect all content gaps
            all_missing_keywords = set()
            all_missing_topics = set()

            for domain, analysis in competitor_analyses.items():
                content_gaps = analysis.get('content_gaps', {})

                keyword_gaps = content_gaps.get('keyword_gaps', {})
                for keyword in keyword_gaps.get('missing_keywords', []):
                    all_missing_keywords.add(keyword)

                topic_gaps = content_gaps.get('topic_gaps', {})
                for topic in topic_gaps.get('missing_topics', []):
                    all_missing_topics.add(topic)

            # Generate keyword opportunities
            for keyword in list(all_missing_keywords)[:20]:
                opportunities.append({
                    'type': 'keyword_opportunity',
                    'keyword': keyword,
                    'opportunity_score': self._calculate_keyword_opportunity_score(keyword, competitor_analyses),
                    'suggested_action': f"Create content targeting '{keyword}'",
                    'priority': 'high' if len([a for a in competitor_analyses.values()
                                             if keyword in str(a.get('keyword_analysis', {}))]) > 2 else 'medium'
                })

            # Generate topic opportunities
            for topic in list(all_missing_topics)[:10]:
                opportunities.append({
                    'type': 'topic_opportunity',
                    'topic': topic,
                    'opportunity_score': self._calculate_topic_opportunity_score(topic, competitor_analyses),
                    'suggested_action': f"Develop content around '{topic}' topic",
                    'priority': 'high' if len([a for a in competitor_analyses.values()
                                             if topic in str(a.get('content_analysis', {}))]) > 1 else 'medium'
                })

            # Sort by opportunity score
            opportunities.sort(key=lambda x: x['opportunity_score'], reverse=True)

            return opportunities[:30]  # Top 30 opportunities

        except Exception as e:
            logger.error(f"Error generating content opportunities: {str(e)}")
            return []

    def _calculate_keyword_opportunity_score(self, keyword: str, competitor_analyses: Dict) -> float:
        """Calculate opportunity score for a keyword"""
        try:
            # Count how many competitors use this keyword
            competitor_count = 0
            total_frequency = 0

            for analysis in competitor_analyses.values():
                keyword_analysis = analysis.get('keyword_analysis', {})
                for kw in keyword_analysis.get('content_keywords', []):
                    if kw['keyword'] == keyword:
                        competitor_count += 1
                        total_frequency += kw['frequency']
                        break

            # Calculate score based on usage frequency and competitor adoption
            base_score = competitor_count * 10  # More competitors = higher opportunity
            frequency_bonus = min(total_frequency / max(competitor_count, 1), 20)  # Frequency bonus

            return round(base_score + frequency_bonus, 2)

        except Exception as e:
            logger.error(f"Error calculating keyword opportunity score: {str(e)}")
            return 0.0

    def _calculate_topic_opportunity_score(self, topic: str, competitor_analyses: Dict) -> float:
        """Calculate opportunity score for a topic"""
        try:
            # Count how many competitors cover this topic
            competitor_count = 0
            total_frequency = 0

            for analysis in competitor_analyses.values():
                content_analysis = analysis.get('content_analysis', {})
                for topic_data in content_analysis.get('content_topics', []):
                    if topic_data['topic'] == topic:
                        competitor_count += 1
                        total_frequency += topic_data['frequency']
                        break

            # Calculate score
            base_score = competitor_count * 15  # Topics are more valuable than individual keywords
            frequency_bonus = min(total_frequency / max(competitor_count, 1), 25)

            return round(base_score + frequency_bonus, 2)

        except Exception as e:
            logger.error(f"Error calculating topic opportunity score: {str(e)}")
            return 0.0

    def _identify_competitive_advantages(self, competitor_analyses: Dict, source_website: Website) -> List[Dict]:
        """Identify competitive advantages of the source website"""
        advantages = []

        try:
            # Get source website analysis
            source_analysis = ContentAnalysis.query.filter_by(
                website_id=source_website.id
            ).order_by(ContentAnalysis.analyzed_at.desc()).first()

            if not source_analysis:
                return advantages

            # Compare domain authority
            source_da = getattr(source_website, 'domain_authority', 0)
            competitor_das = []

            for analysis in competitor_analyses.values():
                competitor_info = analysis.get('competitor_info', {})
                competitor_das.append(competitor_info.get('domain_authority', 0))

            avg_competitor_da = sum(competitor_das) / len(competitor_das) if competitor_das else 0

            if source_da > avg_competitor_da:
                advantages.append({
                    'type': 'domain_authority',
                    'advantage': f"Higher domain authority ({source_da} vs {avg_competitor_da:.1f} average)",
                    'strength': 'high' if source_da > avg_competitor_da * 1.2 else 'medium'
                })

            # Compare content quality
            source_quality = source_analysis.quality_score or 0
            competitor_qualities = []

            for analysis in competitor_analyses.values():
                content_analysis = analysis.get('content_analysis', {})
                quality_indicators = content_analysis.get('content_quality_indicators', {})
                competitor_qualities.append(quality_indicators.get('content_depth_score', 0))

            avg_competitor_quality = sum(competitor_qualities) / len(competitor_qualities) if competitor_qualities else 0

            if source_quality > avg_competitor_quality:
                advantages.append({
                    'type': 'content_quality',
                    'advantage': f"Higher content quality score ({source_quality} vs {avg_competitor_quality:.1f} average)",
                    'strength': 'high' if source_quality > avg_competitor_quality * 1.3 else 'medium'
                })

            return advantages

        except Exception as e:
            logger.error(f"Error identifying competitive advantages: {str(e)}")
            return []
