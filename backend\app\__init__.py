"""
LinkUp Plugin Backend Application Factory
"""
import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import J<PERSON><PERSON>anager
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_caching import Cache
from celery import Celery
import sentry_sdk
from sentry_sdk.integrations.flask import FlaskIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()
cors = CORS()
limiter = Limiter(key_func=get_remote_address)
cache = Cache()
celery = Celery(__name__)


def create_app(config_name=None):
    """Application factory pattern"""
    app = Flask(__name__)
    
    # Load configuration
    config_name = config_name or os.getenv('FLASK_ENV', 'development')
    app.config.from_object(f'app.config.{config_name.title()}Config')
    
    # Initialize Sentry for error tracking
    if app.config.get('SENTRY_DSN'):
        sentry_sdk.init(
            dsn=app.config['SENTRY_DSN'],
            integrations=[
                FlaskIntegration(),
                SqlalchemyIntegration(),
            ],
            traces_sample_rate=0.1,
        )
    
    # Initialize extensions with app
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)
    cors.init_app(app, origins=app.config.get('CORS_ORIGINS', ['*']))
    limiter.init_app(app)
    cache.init_app(app)
    
    # Initialize Celery
    init_celery(app)
    
    # Register blueprints
    from app.routes import auth, websites, backlinks, analytics, admin
    app.register_blueprint(auth, url_prefix='/api/auth')
    app.register_blueprint(websites, url_prefix='/api/websites')
    app.register_blueprint(backlinks, url_prefix='/api/backlinks')
    app.register_blueprint(analytics, url_prefix='/api/analytics')
    app.register_blueprint(admin, url_prefix='/api/admin')
    
    # Register error handlers
    from app.utils.error_handlers import register_error_handlers
    register_error_handlers(app)
    
    # Health check endpoint
    @app.route('/health')
    def health_check():
        return {'status': 'healthy', 'version': app.config.get('VERSION', '1.0.0')}
    
    # API info endpoint
    @app.route('/api')
    def api_info():
        return {
            'name': 'LinkUp Plugin API',
            'version': app.config.get('VERSION', '1.0.0'),
            'description': 'AI-powered WordPress backlink exchange system',
            'endpoints': {
                'auth': '/api/auth',
                'websites': '/api/websites',
                'backlinks': '/api/backlinks',
                'analytics': '/api/analytics',
                'admin': '/api/admin'
            }
        }
    
    return app


def init_celery(app):
    """Initialize Celery with Flask app context"""
    celery.conf.update(
        broker_url=app.config['REDIS_URL'],
        result_backend=app.config['REDIS_URL'],
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,
        task_routes={
            'app.tasks.analysis.*': {'queue': 'analysis'},
            'app.tasks.delivery.*': {'queue': 'delivery'},
            'app.tasks.monitoring.*': {'queue': 'monitoring'},
        },
        beat_schedule={
            'process-pending-backlinks': {
                'task': 'app.tasks.delivery.process_pending_backlinks',
                'schedule': 300.0,  # Every 5 minutes
            },
            'analyze-new-content': {
                'task': 'app.tasks.analysis.analyze_pending_content',
                'schedule': 600.0,  # Every 10 minutes
            },
            'update-analytics': {
                'task': 'app.tasks.monitoring.update_analytics',
                'schedule': 3600.0,  # Every hour
            },
            'cleanup-old-data': {
                'task': 'app.tasks.monitoring.cleanup_old_data',
                'schedule': 86400.0,  # Daily
            },
        }
    )
    
    class ContextTask(celery.Task):
        """Make celery tasks work with Flask app context"""
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)
    
    celery.Task = ContextTask
    return celery


# Import models to ensure they're registered with SQLAlchemy
from app.models import user, website, backlink, analysis, api_key, usage_stats
