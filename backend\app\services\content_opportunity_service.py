"""
Content Opportunity Scoring Service
Advanced scoring system to rank content opportunities by potential value and impact
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum
import math

from app import db, cache
from app.models.website import Website
from app.models.analysis import ContentAnalysis
from app.services.keyword_gap_service import KeywordGapService, KeywordGap, ContentGap

logger = logging.getLogger(__name__)


class OpportunityType(Enum):
    """Types of content opportunities"""
    KEYWORD_TARGETING = "keyword_targeting"
    TOPIC_EXPANSION = "topic_expansion"
    CONTENT_REFRESH = "content_refresh"
    COMPETITOR_GAP = "competitor_gap"
    TRENDING_TOPIC = "trending_topic"
    LONG_TAIL_CAPTURE = "long_tail_capture"
    SEMANTIC_EXPANSION = "semantic_expansion"


@dataclass
class ContentOpportunity:
    """Represents a scored content opportunity"""
    opportunity_id: str
    opportunity_type: OpportunityType
    title: str
    description: str
    target_keywords: List[str]
    primary_keyword: str
    opportunity_score: float
    difficulty_score: float
    impact_score: float
    urgency_score: float
    roi_estimate: float
    estimated_traffic: int
    competition_level: str
    content_type: str
    suggested_length: int
    estimated_effort_hours: int
    priority_rank: int
    reasoning: List[str]
    success_factors: List[str]
    risks: List[str]
    related_opportunities: List[str]
    created_at: datetime


class ContentOpportunityService:
    """Service for scoring and ranking content opportunities"""
    
    def __init__(self):
        """Initialize the content opportunity service"""
        self.keyword_gap_service = KeywordGapService()
        self.cache_timeout = 3600 * 6  # 6 hours
        
        # Scoring weights for different factors
        self.scoring_weights = {
            'search_volume': 0.25,
            'keyword_difficulty': 0.20,
            'competitor_gap': 0.15,
            'content_quality_potential': 0.15,
            'business_relevance': 0.10,
            'trend_factor': 0.10,
            'user_intent_match': 0.05
        }
        
        # Content type effort estimates (hours)
        self.effort_estimates = {
            'blog_post': 8,
            'guide': 16,
            'tutorial': 12,
            'listicle': 6,
            'comparison': 10,
            'review': 8,
            'pillar_page': 24,
            'case_study': 12,
            'infographic': 16,
            'video_script': 10
        }
        
        # Content length recommendations (words)
        self.length_recommendations = {
            'blog_post': 1500,
            'guide': 3000,
            'tutorial': 2000,
            'listicle': 1200,
            'comparison': 2500,
            'review': 1800,
            'pillar_page': 4000,
            'case_study': 2200,
            'infographic': 500,
            'video_script': 800
        }
    
    def score_content_opportunities(self, website_id: int, competitor_domains: List[str] = None) -> Dict[str, Any]:
        """Score and rank all content opportunities for a website"""
        try:
            # Check cache first
            cache_key = f"content_opportunities_{website_id}_{hash(str(competitor_domains))}"
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result
            
            website = Website.query.get(website_id)
            if not website:
                raise ValueError(f"Website {website_id} not found")
            
            # Get keyword gap analysis
            gap_analysis = self.keyword_gap_service.analyze_keyword_gaps(website_id, competitor_domains)
            
            if 'error' in gap_analysis:
                return gap_analysis
            
            # Score different types of opportunities
            opportunities = []
            
            # Score keyword targeting opportunities
            keyword_opportunities = self._score_keyword_opportunities(
                gap_analysis.get('keyword_gaps', []), website
            )
            opportunities.extend(keyword_opportunities)
            
            # Score content gap opportunities
            content_opportunities = self._score_content_gap_opportunities(
                gap_analysis.get('content_gaps', []), website
            )
            opportunities.extend(content_opportunities)
            
            # Score long-tail opportunities
            long_tail_opportunities = self._score_long_tail_opportunities(
                gap_analysis.get('long_tail_opportunities', []), website
            )
            opportunities.extend(long_tail_opportunities)
            
            # Score semantic expansion opportunities
            semantic_opportunities = self._score_semantic_opportunities(
                gap_analysis.get('semantic_gaps', []), website
            )
            opportunities.extend(semantic_opportunities)
            
            # Rank all opportunities
            ranked_opportunities = self._rank_opportunities(opportunities)
            
            # Generate insights and recommendations
            insights = self._generate_opportunity_insights(ranked_opportunities, website)
            
            result = {
                'website_id': website_id,
                'analysis_date': datetime.now().isoformat(),
                'total_opportunities': len(ranked_opportunities),
                'opportunities': [self._serialize_opportunity(opp) for opp in ranked_opportunities],
                'insights': insights,
                'summary_metrics': self._calculate_opportunity_metrics(ranked_opportunities),
                'recommendations': self._generate_priority_recommendations(ranked_opportunities)
            }
            
            # Cache the results
            cache.set(cache_key, result, timeout=self.cache_timeout)
            
            return result
            
        except Exception as e:
            logger.error(f"Error scoring content opportunities: {str(e)}")
            return {'error': str(e)}
    
    def _score_keyword_opportunities(self, keyword_gaps: List[KeywordGap], website: Website) -> List[ContentOpportunity]:
        """Score keyword targeting opportunities"""
        opportunities = []
        
        try:
            for i, gap in enumerate(keyword_gaps):
                # Calculate comprehensive opportunity score
                opportunity_score = self._calculate_comprehensive_opportunity_score(gap, website)
                
                # Calculate impact score
                impact_score = self._calculate_impact_score(gap, website)
                
                # Calculate urgency score
                urgency_score = self._calculate_urgency_score(gap)
                
                # Estimate ROI
                roi_estimate = self._estimate_roi(gap, opportunity_score, impact_score)
                
                # Create opportunity
                opportunity = ContentOpportunity(
                    opportunity_id=f"kw_{website.id}_{i}",
                    opportunity_type=OpportunityType.KEYWORD_TARGETING,
                    title=f"Target '{gap.keyword}' keyword",
                    description=f"Create content optimized for '{gap.keyword}' to capture {gap.search_volume_estimate} monthly searches",
                    target_keywords=[gap.keyword] + gap.related_keywords[:3],
                    primary_keyword=gap.keyword,
                    opportunity_score=opportunity_score,
                    difficulty_score=gap.difficulty_score,
                    impact_score=impact_score,
                    urgency_score=urgency_score,
                    roi_estimate=roi_estimate,
                    estimated_traffic=self._estimate_traffic_potential(gap),
                    competition_level=self._determine_competition_level(gap.difficulty_score),
                    content_type=gap.suggested_content_type,
                    suggested_length=self.length_recommendations.get(gap.suggested_content_type, 1500),
                    estimated_effort_hours=self.effort_estimates.get(gap.suggested_content_type, 8),
                    priority_rank=0,  # Will be set during ranking
                    reasoning=self._generate_opportunity_reasoning(gap),
                    success_factors=self._identify_success_factors(gap),
                    risks=self._identify_risks(gap),
                    related_opportunities=[],  # Will be populated later
                    created_at=datetime.now()
                )
                
                opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"Error scoring keyword opportunities: {str(e)}")
            return []
    
    def _score_content_gap_opportunities(self, content_gaps: List[ContentGap], website: Website) -> List[ContentOpportunity]:
        """Score content gap opportunities"""
        opportunities = []
        
        try:
            for i, gap in enumerate(content_gaps):
                # Calculate scores
                opportunity_score = self._calculate_content_gap_opportunity_score(gap, website)
                difficulty_score = self._estimate_content_difficulty(gap)
                impact_score = self._calculate_content_impact_score(gap)
                urgency_score = self._calculate_content_urgency_score(gap)
                roi_estimate = self._estimate_content_roi(gap, opportunity_score)
                
                # Create opportunity
                opportunity = ContentOpportunity(
                    opportunity_id=f"cg_{website.id}_{i}",
                    opportunity_type=OpportunityType.TOPIC_EXPANSION,
                    title=f"Expand content on '{gap.topic}' topic",
                    description=f"Create comprehensive content covering '{gap.topic}' to fill competitor gap",
                    target_keywords=gap.target_keywords[:5],
                    primary_keyword=gap.target_keywords[0] if gap.target_keywords else gap.topic,
                    opportunity_score=opportunity_score,
                    difficulty_score=difficulty_score,
                    impact_score=impact_score,
                    urgency_score=urgency_score,
                    roi_estimate=roi_estimate,
                    estimated_traffic=self._estimate_content_traffic_potential(gap),
                    competition_level=self._determine_content_competition_level(gap),
                    content_type=gap.content_type,
                    suggested_length=self.length_recommendations.get(gap.content_type, 2000),
                    estimated_effort_hours=self.effort_estimates.get(gap.content_type, 12),
                    priority_rank=0,
                    reasoning=self._generate_content_gap_reasoning(gap),
                    success_factors=self._identify_content_success_factors(gap),
                    risks=self._identify_content_risks(gap),
                    related_opportunities=[],
                    created_at=datetime.now()
                )
                
                opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"Error scoring content gap opportunities: {str(e)}")
            return []
    
    def _score_long_tail_opportunities(self, long_tail_gaps: List[KeywordGap], website: Website) -> List[ContentOpportunity]:
        """Score long-tail keyword opportunities"""
        opportunities = []
        
        try:
            for i, gap in enumerate(long_tail_gaps):
                # Long-tail keywords typically have lower difficulty but also lower volume
                opportunity_score = self._calculate_long_tail_opportunity_score(gap, website)
                
                opportunity = ContentOpportunity(
                    opportunity_id=f"lt_{website.id}_{i}",
                    opportunity_type=OpportunityType.LONG_TAIL_CAPTURE,
                    title=f"Target long-tail phrase '{gap.keyword}'",
                    description=f"Create focused content for long-tail keyword '{gap.keyword}'",
                    target_keywords=[gap.keyword],
                    primary_keyword=gap.keyword,
                    opportunity_score=opportunity_score,
                    difficulty_score=gap.difficulty_score * 0.7,  # Long-tail is typically easier
                    impact_score=self._calculate_long_tail_impact_score(gap),
                    urgency_score=self._calculate_long_tail_urgency_score(gap),
                    roi_estimate=self._estimate_long_tail_roi(gap),
                    estimated_traffic=gap.search_volume_estimate,
                    competition_level=self._determine_competition_level(gap.difficulty_score * 0.7),
                    content_type=gap.suggested_content_type,
                    suggested_length=self.length_recommendations.get(gap.suggested_content_type, 1200),
                    estimated_effort_hours=self.effort_estimates.get(gap.suggested_content_type, 6),
                    priority_rank=0,
                    reasoning=self._generate_long_tail_reasoning(gap),
                    success_factors=self._identify_long_tail_success_factors(gap),
                    risks=self._identify_long_tail_risks(gap),
                    related_opportunities=[],
                    created_at=datetime.now()
                )
                
                opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"Error scoring long-tail opportunities: {str(e)}")
            return []
    
    def _score_semantic_opportunities(self, semantic_gaps: List[KeywordGap], website: Website) -> List[ContentOpportunity]:
        """Score semantic expansion opportunities"""
        opportunities = []
        
        try:
            for i, gap in enumerate(semantic_gaps):
                opportunity_score = self._calculate_semantic_opportunity_score(gap, website)
                
                opportunity = ContentOpportunity(
                    opportunity_id=f"sem_{website.id}_{i}",
                    opportunity_type=OpportunityType.SEMANTIC_EXPANSION,
                    title=f"Semantic expansion for '{gap.keyword}'",
                    description=f"Expand semantic coverage in '{gap.semantic_cluster}' cluster",
                    target_keywords=[gap.keyword] + gap.related_keywords[:2],
                    primary_keyword=gap.keyword,
                    opportunity_score=opportunity_score,
                    difficulty_score=gap.difficulty_score,
                    impact_score=self._calculate_semantic_impact_score(gap),
                    urgency_score=self._calculate_semantic_urgency_score(gap),
                    roi_estimate=self._estimate_semantic_roi(gap),
                    estimated_traffic=gap.search_volume_estimate,
                    competition_level=self._determine_competition_level(gap.difficulty_score),
                    content_type=gap.suggested_content_type,
                    suggested_length=self.length_recommendations.get(gap.suggested_content_type, 1800),
                    estimated_effort_hours=self.effort_estimates.get(gap.suggested_content_type, 10),
                    priority_rank=0,
                    reasoning=self._generate_semantic_reasoning(gap),
                    success_factors=self._identify_semantic_success_factors(gap),
                    risks=self._identify_semantic_risks(gap),
                    related_opportunities=[],
                    created_at=datetime.now()
                )
                
                opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"Error scoring semantic opportunities: {str(e)}")
            return []

    def _calculate_comprehensive_opportunity_score(self, gap: KeywordGap, website: Website) -> float:
        """Calculate comprehensive opportunity score for a keyword gap"""
        try:
            # Base score from gap analysis
            base_score = gap.opportunity_score

            # Search volume factor (0-25 points)
            volume_score = min(math.log10(max(gap.search_volume_estimate, 1)) * 8, 25)

            # Difficulty factor (inverse, 0-20 points)
            difficulty_factor = max(0, (100 - gap.difficulty_score) / 5)

            # Business relevance factor (0-15 points)
            relevance_score = self._calculate_business_relevance(gap.keyword, website)

            # Trend factor (0-10 points)
            trend_score = self._calculate_trend_factor(gap.keyword)

            # Competition gap factor (0-15 points)
            competition_gap_score = min(len(gap.competitor_usage) * 3, 15)

            # User intent match factor (0-10 points)
            intent_score = self._calculate_user_intent_score(gap.keyword)

            total_score = (
                base_score * 0.3 +
                volume_score * self.scoring_weights['search_volume'] * 4 +
                difficulty_factor * self.scoring_weights['keyword_difficulty'] * 4 +
                relevance_score * self.scoring_weights['business_relevance'] * 4 +
                trend_score * self.scoring_weights['trend_factor'] * 4 +
                competition_gap_score * self.scoring_weights['competitor_gap'] * 4 +
                intent_score * self.scoring_weights['user_intent_match'] * 4
            )

            return round(min(total_score, 100), 2)

        except Exception as e:
            logger.error(f"Error calculating comprehensive opportunity score: {str(e)}")
            return gap.opportunity_score

    def _calculate_impact_score(self, gap: KeywordGap, website: Website) -> float:
        """Calculate potential impact score"""
        try:
            # Traffic impact (0-40 points)
            traffic_impact = min(gap.search_volume_estimate / 50, 40)

            # Authority impact (0-30 points)
            authority_impact = self._calculate_authority_impact(gap, website)

            # Business impact (0-30 points)
            business_impact = self._calculate_business_impact(gap, website)

            total_impact = traffic_impact + authority_impact + business_impact
            return round(min(total_impact, 100), 2)

        except Exception as e:
            logger.error(f"Error calculating impact score: {str(e)}")
            return 50.0

    def _calculate_urgency_score(self, gap: KeywordGap) -> float:
        """Calculate urgency score based on competitive factors"""
        try:
            # Competitor usage urgency (0-40 points)
            competitor_urgency = min(len(gap.competitor_usage) * 8, 40)

            # Difficulty trend urgency (0-30 points)
            # Assume easier keywords become harder over time
            difficulty_urgency = max(0, (50 - gap.difficulty_score) / 2)

            # Opportunity type urgency (0-30 points)
            type_urgency = {
                'missing_keyword': 30,
                'trending_keyword': 25,
                'opportunity_keyword': 20,
                'underoptimized_keyword': 15,
                'long_tail_opportunity': 10,
                'semantic_gap': 10
            }.get(gap.gap_type.value, 15)

            total_urgency = competitor_urgency + difficulty_urgency + type_urgency
            return round(min(total_urgency, 100), 2)

        except Exception as e:
            logger.error(f"Error calculating urgency score: {str(e)}")
            return 50.0

    def _estimate_roi(self, gap: KeywordGap, opportunity_score: float, impact_score: float) -> float:
        """Estimate ROI for keyword opportunity"""
        try:
            # Estimate potential value
            potential_traffic = gap.search_volume_estimate * 0.1  # Assume 10% CTR
            traffic_value = potential_traffic * 2  # $2 per visitor estimate

            # Estimate cost (content creation + promotion)
            content_cost = self.effort_estimates.get(gap.suggested_content_type, 8) * 50  # $50/hour
            promotion_cost = content_cost * 0.5  # 50% of content cost for promotion
            total_cost = content_cost + promotion_cost

            # Calculate ROI
            if total_cost > 0:
                roi = ((traffic_value * 12) - total_cost) / total_cost * 100  # Annual ROI
                return round(max(roi, -100), 2)  # Cap at -100% minimum

            return 0.0

        except Exception as e:
            logger.error(f"Error estimating ROI: {str(e)}")
            return 0.0

    def _calculate_business_relevance(self, keyword: str, website: Website) -> float:
        """Calculate business relevance score"""
        try:
            # Check if keyword relates to website category
            category_relevance = 0
            if website.category and website.category.lower() in keyword.lower():
                category_relevance = 15

            # Check for commercial intent
            commercial_terms = ['buy', 'price', 'cost', 'review', 'best', 'top']
            commercial_relevance = 0
            if any(term in keyword.lower() for term in commercial_terms):
                commercial_relevance = 10

            # Check for informational intent (good for authority building)
            info_terms = ['how', 'what', 'why', 'guide', 'tutorial']
            info_relevance = 0
            if any(term in keyword.lower() for term in info_terms):
                info_relevance = 8

            return category_relevance + commercial_relevance + info_relevance

        except Exception as e:
            logger.error(f"Error calculating business relevance: {str(e)}")
            return 5.0

    def _calculate_trend_factor(self, keyword: str) -> float:
        """Calculate trend factor (simplified implementation)"""
        try:
            # This would typically integrate with Google Trends API
            # For now, use keyword characteristics as proxy

            trending_indicators = ['2024', '2025', 'new', 'latest', 'trending', 'ai', 'automation']
            trend_score = 0

            for indicator in trending_indicators:
                if indicator in keyword.lower():
                    trend_score += 2

            return min(trend_score, 10)

        except Exception as e:
            logger.error(f"Error calculating trend factor: {str(e)}")
            return 5.0

    def _calculate_user_intent_score(self, keyword: str) -> float:
        """Calculate user intent clarity score"""
        try:
            # Clear intent keywords score higher
            intent_patterns = {
                'how to': 10,
                'what is': 8,
                'best': 9,
                'review': 8,
                'vs': 7,
                'guide': 8,
                'tutorial': 9,
                'tips': 7
            }

            for pattern, score in intent_patterns.items():
                if pattern in keyword.lower():
                    return score

            return 5  # Default score for unclear intent

        except Exception as e:
            logger.error(f"Error calculating user intent score: {str(e)}")
            return 5.0
