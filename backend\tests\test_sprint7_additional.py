"""
Additional Unit Tests for Sprint 7 Features
Tests for SuggestionGenerationService, ContentOptimizationService, and TrendingTopicsService
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.suggestion_generation_service import (
    SuggestionGenerationService, SuggestionType, Priority, ContentSuggestion
)
from app.services.content_optimization_service import (
    ContentOptimizationService, OptimizationType, OptimizationRecommendation
)
from app.services.trending_topics_service import (
    TrendingTopicsService, TrendSource, TrendTimeframe, TrendingTopic
)


class TestSuggestionGenerationService(unittest.TestCase):
    """Test cases for SuggestionGenerationService"""
    
    def setUp(self):
        self.service = SuggestionGenerationService()
        self.mock_website = Mock()
        self.mock_website.id = 1
        self.mock_website.domain = 'example.com'
    
    def test_init(self):
        """Test service initialization"""
        self.assertIsInstance(self.service, SuggestionGenerationService)
        self.assertIn('blog_post', self.service.content_templates)
        self.assertIn('guide', self.service.content_templates)
        self.assertIn((1, 8), self.service.timeline_estimates)
    
    @patch('app.services.suggestion_generation_service.cache')
    @patch('app.models.website.Website.query')
    def test_generate_content_suggestions_cached(self, mock_query, mock_cache):
        """Test content suggestion generation with cached results"""
        # Setup
        mock_query.get.return_value = self.mock_website
        cached_result = {'cached': True, 'suggestions': []}
        mock_cache.get.return_value = cached_result
        
        # Execute
        result = self.service.generate_content_suggestions(1)
        
        # Assert
        self.assertEqual(result, cached_result)
    
    def test_map_opportunity_to_suggestion_type(self):
        """Test mapping opportunity types to suggestion types"""
        self.assertEqual(
            self.service._map_opportunity_to_suggestion_type('keyword_targeting'),
            SuggestionType.KEYWORD_TARGETING
        )
        self.assertEqual(
            self.service._map_opportunity_to_suggestion_type('topic_expansion'),
            SuggestionType.CONTENT_EXPANSION
        )
        self.assertEqual(
            self.service._map_opportunity_to_suggestion_type('unknown_type'),
            SuggestionType.NEW_CONTENT
        )
    
    def test_determine_suggestion_priority(self):
        """Test suggestion priority determination"""
        # High priority opportunity
        high_opp = {
            'opportunity_score': 90,
            'impact_score': 85,
            'urgency_score': 80
        }
        self.assertEqual(self.service._determine_suggestion_priority(high_opp), Priority.CRITICAL)
        
        # Medium priority opportunity
        medium_opp = {
            'opportunity_score': 60,
            'impact_score': 55,
            'urgency_score': 50
        }
        self.assertEqual(self.service._determine_suggestion_priority(medium_opp), Priority.MEDIUM)
        
        # Low priority opportunity
        low_opp = {
            'opportunity_score': 30,
            'impact_score': 25,
            'urgency_score': 20
        }
        self.assertEqual(self.service._determine_suggestion_priority(low_opp), Priority.LOW)
    
    def test_generate_content_outline(self):
        """Test content outline generation"""
        # Test blog post outline
        blog_outline = self.service._generate_content_outline(
            'blog_post', 'how to optimize seo', ['seo', 'optimization']
        )
        self.assertIsInstance(blog_outline, list)
        self.assertGreater(len(blog_outline), 3)
        
        # Test guide outline
        guide_outline = self.service._generate_content_outline(
            'guide', 'seo guide', ['seo', 'guide']
        )
        self.assertIsInstance(guide_outline, list)
        self.assertTrue(any('step' in item.lower() for item in guide_outline))
    
    def test_generate_seo_recommendations(self):
        """Test SEO recommendations generation"""
        seo_recs = self.service._generate_seo_recommendations(
            'blog_post', 'seo optimization', ['seo', 'optimization', 'tips']
        )
        
        self.assertIsInstance(seo_recs, list)
        self.assertGreater(len(seo_recs), 3)
        self.assertTrue(any('seo optimization' in rec for rec in seo_recs))
        self.assertTrue(any('keyword density' in rec for rec in seo_recs))
    
    def test_generate_action_steps(self):
        """Test action steps generation"""
        opp_data = {
            'content_type': 'guide',
            'primary_keyword': 'seo guide'
        }
        
        steps = self.service._generate_action_steps(opp_data, self.mock_website)
        
        self.assertIsInstance(steps, list)
        self.assertGreater(len(steps), 5)
        self.assertTrue(any('keyword research' in step.lower() for step in steps))
        self.assertTrue(any('content outline' in step.lower() for step in steps))
        
        # Test guide-specific steps
        self.assertTrue(any('downloadable' in step.lower() for step in steps))
    
    def test_estimate_timeline(self):
        """Test timeline estimation"""
        self.assertEqual(self.service._estimate_timeline(5), '1-2 days')
        self.assertEqual(self.service._estimate_timeline(15), '3-5 days')
        self.assertEqual(self.service._estimate_timeline(25), '2 weeks')
        self.assertEqual(self.service._estimate_timeline(70), '1+ months')
    
    def test_generate_rationale(self):
        """Test rationale generation"""
        opp_data = {
            'opportunity_score': 85.5,
            'estimated_traffic': 1500,
            'competition_level': 'medium'
        }
        
        rationale = self.service._generate_rationale(opp_data)
        
        self.assertIn('85.5', rationale)
        self.assertIn('1500', rationale)
        self.assertIn('medium', rationale)
        self.assertIn('balanced opportunity', rationale)


class TestContentOptimizationService(unittest.TestCase):
    """Test cases for ContentOptimizationService"""
    
    def setUp(self):
        self.service = ContentOptimizationService()
    
    def test_init(self):
        """Test service initialization"""
        self.assertIsInstance(self.service, ContentOptimizationService)
        self.assertIn('min_content_length', self.service.thresholds)
        self.assertIn('title_length', self.service.seo_best_practices)
    
    def test_extract_content_metadata(self):
        """Test content metadata extraction"""
        content = """# Main Title
        
## Subtitle 1
Some content here.

## Subtitle 2
More content with [internal link](/page) and [external link](https://example.com).

![Alt text](image.jpg)
"""
        
        metadata = self.service._extract_content_metadata(content)
        
        self.assertIn('title', metadata)
        self.assertEqual(metadata['title'], 'Main Title')
        self.assertEqual(len(metadata['h1_tags']), 1)
        self.assertEqual(len(metadata['h2_tags']), 2)
        self.assertEqual(len(metadata['internal_links']), 1)
        self.assertEqual(len(metadata['external_links']), 1)
        self.assertEqual(len(metadata['images']), 1)
        self.assertEqual(metadata['images'][0]['alt'], 'Alt text')
    
    def test_analyze_content_structure(self):
        """Test content structure analysis"""
        content = """This is the first paragraph.

This is the second paragraph with multiple sentences. It has more than one sentence to test sentence counting.

This is a third paragraph.

- Bullet point 1
- Bullet point 2

1. Numbered item 1
2. Numbered item 2
"""
        
        structure = self.service._analyze_content_structure(content)
        
        self.assertIn('paragraph_count', structure)
        self.assertIn('sentence_count', structure)
        self.assertIn('word_count', structure)
        self.assertIn('bullet_lists', structure)
        self.assertIn('numbered_lists', structure)
        
        self.assertEqual(structure['paragraph_count'], 3)
        self.assertGreater(structure['sentence_count'], 3)
        self.assertEqual(structure['bullet_lists'], 2)
        self.assertEqual(structure['numbered_lists'], 2)
    
    def test_analyze_seo_factors(self):
        """Test SEO factors analysis"""
        content = "This is test content about SEO optimization."
        metadata = {
            'title': 'SEO Optimization Guide',
            'h1_tags': ['Main Heading'],
            'h2_tags': ['Sub Heading 1', 'Sub Heading 2'],
            'internal_links': [{'text': 'Link', 'url': '/page'}],
            'external_links': [{'text': 'External', 'url': 'https://example.com'}],
            'images': [{'alt': 'Alt text', 'src': 'image.jpg'}]
        }
        target_keywords = ['seo', 'optimization']
        
        seo_analysis = self.service._analyze_seo_factors(content, metadata, target_keywords)
        
        self.assertIn('title_length', seo_analysis)
        self.assertIn('title_has_keyword', seo_analysis)
        self.assertIn('h1_count', seo_analysis)
        self.assertIn('internal_link_count', seo_analysis)
        self.assertIn('alt_text_coverage', seo_analysis)
        
        self.assertEqual(seo_analysis['title_length'], len('SEO Optimization Guide'))
        self.assertTrue(seo_analysis['title_has_keyword'])
        self.assertEqual(seo_analysis['h1_count'], 1)
        self.assertEqual(seo_analysis['internal_link_count'], 1)
        self.assertEqual(seo_analysis['alt_text_coverage'], 100.0)
    
    def test_analyze_readability(self):
        """Test readability analysis"""
        # Simple content
        simple_content = "This is simple. Easy to read. Short sentences."
        simple_readability = self.service._analyze_readability(simple_content)
        
        # Complex content
        complex_content = "This is a very complex sentence with multiple clauses and sophisticated vocabulary that might be difficult for some readers to comprehend easily."
        complex_readability = self.service._analyze_readability(complex_content)
        
        self.assertIn('flesch_score', simple_readability)
        self.assertIn('grade_level', simple_readability)
        self.assertIn('level', simple_readability)
        
        # Simple content should have higher readability score
        self.assertGreater(simple_readability['flesch_score'], complex_readability['flesch_score'])
    
    def test_analyze_keyword_optimization(self):
        """Test keyword optimization analysis"""
        content = "This content is about SEO optimization. SEO is important for websites. Optimization helps improve rankings."
        target_keywords = ['seo', 'optimization']
        
        keyword_analysis = self.service._analyze_keyword_optimization(content, target_keywords)
        
        self.assertIn('keyword_density', keyword_analysis)
        self.assertIn('keyword_positions', keyword_analysis)
        self.assertIn('keyword_in_first_100', keyword_analysis)
        
        # Check keyword densities
        self.assertIn('seo', keyword_analysis['keyword_density'])
        self.assertIn('optimization', keyword_analysis['keyword_density'])
        self.assertGreater(keyword_analysis['keyword_density']['seo'], 0)
        self.assertGreater(keyword_analysis['keyword_density']['optimization'], 0)
        
        # Check keyword in first 100 words
        self.assertTrue(keyword_analysis['keyword_in_first_100'])
    
    def test_calculate_seo_score(self):
        """Test SEO score calculation"""
        good_seo = {
            'title_length': 45,  # Good length
            'h1_count': 1,       # Perfect
            'meets_min_length': True,
            'internal_link_count': 3,
            'alt_text_coverage': 90
        }
        
        poor_seo = {
            'title_length': 10,   # Too short
            'h1_count': 0,        # Missing
            'meets_min_length': False,
            'internal_link_count': 0,
            'alt_text_coverage': 20
        }
        
        good_score = self.service._calculate_seo_score(good_seo)
        poor_score = self.service._calculate_seo_score(poor_seo)
        
        self.assertGreater(good_score, poor_score)
        self.assertGreaterEqual(good_score, 0)
        self.assertLessEqual(good_score, 100)
    
    def test_calculate_structure_score(self):
        """Test structure score calculation"""
        good_structure = {
            'paragraph_count': 5,
            'long_paragraphs': 1,
            'avg_sentence_length': 15,
            'bullet_lists': 2,
            'numbered_lists': 1
        }
        
        poor_structure = {
            'paragraph_count': 1,
            'long_paragraphs': 1,
            'avg_sentence_length': 35,
            'bullet_lists': 0,
            'numbered_lists': 0
        }
        
        good_score = self.service._calculate_structure_score(good_structure)
        poor_score = self.service._calculate_structure_score(poor_structure)
        
        self.assertGreater(good_score, poor_score)
        self.assertGreaterEqual(good_score, 0)
        self.assertLessEqual(good_score, 100)


class TestTrendingTopicsService(unittest.TestCase):
    """Test cases for TrendingTopicsService"""
    
    def setUp(self):
        self.service = TrendingTopicsService()
    
    def test_init(self):
        """Test service initialization"""
        self.assertIsInstance(self.service, TrendingTopicsService)
        self.assertIn('technology', self.service.niche_sources)
        self.assertIn('search_volume_change', self.service.scoring_weights)
    
    @patch('app.services.trending_topics_service.cache')
    def test_identify_trending_topics_cached(self, mock_cache):
        """Test trending topics identification with cached results"""
        cached_result = {
            'niche': 'technology',
            'trending_topics': [],
            'emerging_keywords': [],
            'declining_topics': [],
            'seasonal_patterns': {},
            'competitor_trending_content': [],
            'content_gap_opportunities': [],
            'recommended_actions': []
        }
        mock_cache.get.return_value = cached_result
        
        result = self.service.identify_trending_topics('technology')
        
        self.assertEqual(result.niche, 'technology')
        self.assertIsInstance(result.trending_topics, list)
    
    def test_get_google_trends(self):
        """Test Google Trends data retrieval (mock)"""
        trends = self.service._get_google_trends('technology', TrendTimeframe.LAST_7D)
        
        self.assertIsInstance(trends, list)
        self.assertGreater(len(trends), 0)
        
        # Check first trend
        first_trend = trends[0]
        self.assertIsInstance(first_trend, TrendingTopic)
        self.assertIn('technology', first_trend.topic)
        self.assertEqual(first_trend.source, TrendSource.GOOGLE_TRENDS)
        self.assertGreater(first_trend.trend_score, 0)
    
    def test_get_social_media_trends(self):
        """Test social media trends retrieval (mock)"""
        trends = self.service._get_social_media_trends('technology', TrendTimeframe.LAST_7D)
        
        self.assertIsInstance(trends, list)
        self.assertGreater(len(trends), 0)
        
        # Check trend properties
        first_trend = trends[0]
        self.assertEqual(first_trend.source, TrendSource.SOCIAL_MEDIA)
        self.assertGreater(first_trend.social_mentions, 0)
    
    def test_get_news_trends(self):
        """Test news trends retrieval (mock)"""
        trends = self.service._get_news_trends('technology', TrendTimeframe.LAST_7D)
        
        self.assertIsInstance(trends, list)
        self.assertGreater(len(trends), 0)
        
        # Check trend properties
        first_trend = trends[0]
        self.assertEqual(first_trend.source, TrendSource.NEWS_API)
        self.assertGreater(first_trend.news_mentions, 0)
    
    def test_get_topic_key(self):
        """Test topic key generation for grouping"""
        key1 = self.service._get_topic_key('AI and Machine Learning')
        key2 = self.service._get_topic_key('Machine Learning and AI')
        
        # Should generate same key for similar topics
        self.assertEqual(key1, key2)
        
        # Test with common words removal
        key3 = self.service._get_topic_key('The Future of Technology')
        self.assertNotIn('the', key3)
        self.assertNotIn('of', key3)
    
    def test_calculate_combined_trend_score(self):
        """Test combined trend score calculation"""
        topics = [
            Mock(trend_score=80, source=TrendSource.GOOGLE_TRENDS),
            Mock(trend_score=70, source=TrendSource.SOCIAL_MEDIA),
            Mock(trend_score=60, source=TrendSource.NEWS_API)
        ]
        
        combined_score = self.service._calculate_combined_trend_score(topics)
        
        self.assertIsInstance(combined_score, float)
        self.assertGreater(combined_score, 0)
        self.assertLess(combined_score, 100)
    
    def test_calculate_keyword_trend_score(self):
        """Test keyword trend score calculation"""
        # Mock keyword data
        keyword_data = Mock()
        keyword_data.search_volume = 1000
        keyword_data.keyword_difficulty = 40
        keyword_data.keyword = 'technology trends'
        
        score = self.service._calculate_keyword_trend_score(keyword_data, 'technology')
        
        self.assertIsInstance(score, float)
        self.assertGreaterEqual(score, 0)
        self.assertLessEqual(score, 100)
        
        # Test with niche-relevant keyword
        relevant_score = self.service._calculate_keyword_trend_score(keyword_data, 'technology')
        
        # Test with non-relevant keyword
        keyword_data.keyword = 'cooking recipes'
        non_relevant_score = self.service._calculate_keyword_trend_score(keyword_data, 'technology')
        
        self.assertGreater(relevant_score, non_relevant_score)


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestSuggestionGenerationService))
    test_suite.addTest(unittest.makeSuite(TestContentOptimizationService))
    test_suite.addTest(unittest.makeSuite(TestTrendingTopicsService))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*50}")
    print(f"SPRINT 7 ADDITIONAL TEST RESULTS")
    print(f"{'='*50}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print(f"\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
