#!/usr/bin/env python3
"""
Test runner for LinkUp Plugin Backend
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def run_tests(test_type='all', coverage=True, verbose=True, fail_fast=False):
    """Run tests with specified options"""
    
    # Set environment variables for testing
    os.environ['FLASK_ENV'] = 'testing'
    os.environ['FLASK_CONFIG'] = 'testing'
    
    # Base pytest command
    cmd = ['python', '-m', 'pytest']
    
    # Add coverage options
    if coverage:
        cmd.extend([
            '--cov=app',
            '--cov-report=term-missing',
            '--cov-report=html:htmlcov',
            '--cov-fail-under=80'
        ])
    
    # Add verbosity
    if verbose:
        cmd.append('-v')
    
    # Add fail fast option
    if fail_fast:
        cmd.append('-x')
    
    # Select test type
    if test_type == 'unit':
        cmd.extend(['-m', 'unit'])
    elif test_type == 'integration':
        cmd.extend(['-m', 'integration'])
    elif test_type == 'api':
        cmd.extend(['-m', 'api'])
    elif test_type == 'models':
        cmd.append('tests/test_models.py')
    elif test_type == 'auth':
        cmd.append('tests/test_auth_service.py')
    elif test_type == 'content':
        cmd.append('tests/test_content_analysis_service.py')
    elif test_type == 'routes':
        cmd.append('tests/test_api_routes.py')
    elif test_type == 'quick':
        cmd.extend(['-m', 'not slow'])
    elif test_type == 'all':
        cmd.append('tests/')
    else:
        print(f"Unknown test type: {test_type}")
        return 1
    
    # Run the tests
    print(f"Running tests with command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, cwd=backend_dir)
        return result.returncode
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return 1
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def setup_test_environment():
    """Set up the test environment"""
    print("Setting up test environment...")
    
    # Check if required packages are installed
    required_packages = [
        'pytest',
        'pytest-flask',
        'pytest-cov',
        'factory-boy',
        'responses'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"Missing required packages: {', '.join(missing_packages)}")
        print("Please install them with: pip install -r requirements.txt")
        return False
    
    # Check if spaCy model is available
    try:
        import spacy
        spacy.load('en_core_web_sm')
    except OSError:
        print("spaCy English model not found.")
        print("Please install it with: python -m spacy download en_core_web_sm")
        return False
    
    print("Test environment setup complete!")
    return True


def generate_test_report():
    """Generate a comprehensive test report"""
    print("Generating test report...")
    
    # Run tests with detailed output
    cmd = [
        'python', '-m', 'pytest',
        '--cov=app',
        '--cov-report=html:htmlcov',
        '--cov-report=term-missing',
        '--cov-report=xml:coverage.xml',
        '--junit-xml=test-results.xml',
        '-v',
        'tests/'
    ]
    
    result = subprocess.run(cmd, cwd=backend_dir)
    
    if result.returncode == 0:
        print("\n" + "="*50)
        print("TEST REPORT GENERATED SUCCESSFULLY")
        print("="*50)
        print("HTML Coverage Report: htmlcov/index.html")
        print("XML Coverage Report: coverage.xml")
        print("JUnit Test Results: test-results.xml")
        print("="*50)
    
    return result.returncode


def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description='Run LinkUp Plugin Backend Tests')
    
    parser.add_argument(
        'test_type',
        nargs='?',
        default='all',
        choices=['all', 'unit', 'integration', 'api', 'models', 'auth', 'content', 'routes', 'quick'],
        help='Type of tests to run'
    )
    
    parser.add_argument(
        '--no-coverage',
        action='store_true',
        help='Disable coverage reporting'
    )
    
    parser.add_argument(
        '--quiet',
        action='store_true',
        help='Run tests in quiet mode'
    )
    
    parser.add_argument(
        '--fail-fast',
        action='store_true',
        help='Stop on first failure'
    )
    
    parser.add_argument(
        '--setup',
        action='store_true',
        help='Set up test environment'
    )
    
    parser.add_argument(
        '--report',
        action='store_true',
        help='Generate comprehensive test report'
    )
    
    args = parser.parse_args()
    
    # Setup test environment if requested
    if args.setup:
        if not setup_test_environment():
            return 1
        return 0
    
    # Generate test report if requested
    if args.report:
        return generate_test_report()
    
    # Check if test environment is ready
    if not setup_test_environment():
        return 1
    
    # Run tests
    return run_tests(
        test_type=args.test_type,
        coverage=not args.no_coverage,
        verbose=not args.quiet,
        fail_fast=args.fail_fast
    )


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
