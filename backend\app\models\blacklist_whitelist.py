"""
Blacklist/Whitelist Models for LinkUp Plugin
Manages user-configurable domain and keyword filtering
"""
from datetime import datetime
from typing import Dict, Optional
from app import db
from sqlalchemy import Index


class DomainFilter(db.Model):
    """Model for domain-level blacklist/whitelist entries"""
    
    __tablename__ = 'domain_filters'
    
    # Primary key
    id = db.Column(db.Integer, primary_key=True)
    
    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # NULL for global filters
    website_id = db.Column(db.Integer, db.ForeignKey('websites.id'), nullable=True)  # NULL for user-wide filters
    
    # Filter details
    domain = db.Column(db.String(255), nullable=False, index=True)
    filter_type = db.Column(db.String(20), nullable=False)  # 'blacklist' or 'whitelist'
    scope = db.Column(db.String(20), default='global')  # 'global', 'user', 'website'
    
    # Filter configuration
    is_active = db.Column(db.<PERSON>, default=True)
    is_regex = db.Column(db.Bo<PERSON>, default=False)
    match_subdomains = db.Column(db.Boolean, default=True)
    
    # Metadata
    reason = db.Column(db.String(500))  # Why this domain was filtered
    added_by = db.Column(db.String(50), default='manual')  # 'manual', 'auto_spam', 'quality_filter'
    confidence_score = db.Column(db.Float, default=1.0)  # Confidence in this filter (0-1)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    expires_at = db.Column(db.DateTime)  # Optional expiration for temporary filters
    
    # Relationships
    user = db.relationship('User', backref='domain_filters')
    website = db.relationship('Website', backref='domain_filters')
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_domain_filter_lookup', 'domain', 'filter_type', 'is_active'),
        Index('idx_user_domain_filters', 'user_id', 'filter_type', 'is_active'),
        Index('idx_global_domain_filters', 'scope', 'filter_type', 'is_active'),
    )
    
    def __repr__(self):
        return f'<DomainFilter {self.domain} ({self.filter_type})>'
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'domain': self.domain,
            'filter_type': self.filter_type,
            'scope': self.scope,
            'is_active': self.is_active,
            'is_regex': self.is_regex,
            'match_subdomains': self.match_subdomains,
            'reason': self.reason,
            'added_by': self.added_by,
            'confidence_score': self.confidence_score,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None
        }
    
    @classmethod
    def get_active_filters(cls, user_id=None, website_id=None, filter_type=None):
        """Get active filters for a user/website"""
        query = cls.query.filter_by(is_active=True)
        
        if filter_type:
            query = query.filter_by(filter_type=filter_type)
        
        # Build scope filter
        scope_conditions = [cls.scope == 'global']
        
        if user_id:
            scope_conditions.append(
                db.and_(cls.scope == 'user', cls.user_id == user_id)
            )
        
        if website_id:
            scope_conditions.append(
                db.and_(cls.scope == 'website', cls.website_id == website_id)
            )
        
        query = query.filter(db.or_(*scope_conditions))
        
        return query.all()


class KeywordFilter(db.Model):
    """Model for keyword-level blacklist/whitelist entries"""
    
    __tablename__ = 'keyword_filters'
    
    # Primary key
    id = db.Column(db.Integer, primary_key=True)
    
    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    website_id = db.Column(db.Integer, db.ForeignKey('websites.id'), nullable=True)
    
    # Filter details
    keyword = db.Column(db.String(255), nullable=False, index=True)
    filter_type = db.Column(db.String(20), nullable=False)  # 'blacklist' or 'whitelist'
    scope = db.Column(db.String(20), default='global')
    
    # Filter configuration
    is_active = db.Column(db.Boolean, default=True)
    is_regex = db.Column(db.Boolean, default=False)
    case_sensitive = db.Column(db.Boolean, default=False)
    match_type = db.Column(db.String(20), default='contains')  # 'exact', 'contains', 'starts_with', 'ends_with'
    
    # Context filtering
    apply_to_title = db.Column(db.Boolean, default=True)
    apply_to_content = db.Column(db.Boolean, default=True)
    apply_to_keywords = db.Column(db.Boolean, default=True)
    apply_to_description = db.Column(db.Boolean, default=True)
    
    # Metadata
    reason = db.Column(db.String(500))
    added_by = db.Column(db.String(50), default='manual')
    confidence_score = db.Column(db.Float, default=1.0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    expires_at = db.Column(db.DateTime)
    
    # Relationships
    user = db.relationship('User', backref='keyword_filters')
    website = db.relationship('Website', backref='keyword_filters')
    
    # Indexes
    __table_args__ = (
        Index('idx_keyword_filter_lookup', 'keyword', 'filter_type', 'is_active'),
        Index('idx_user_keyword_filters', 'user_id', 'filter_type', 'is_active'),
    )
    
    def __repr__(self):
        return f'<KeywordFilter {self.keyword} ({self.filter_type})>'
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'keyword': self.keyword,
            'filter_type': self.filter_type,
            'scope': self.scope,
            'is_active': self.is_active,
            'is_regex': self.is_regex,
            'case_sensitive': self.case_sensitive,
            'match_type': self.match_type,
            'apply_to_title': self.apply_to_title,
            'apply_to_content': self.apply_to_content,
            'apply_to_keywords': self.apply_to_keywords,
            'apply_to_description': self.apply_to_description,
            'reason': self.reason,
            'added_by': self.added_by,
            'confidence_score': self.confidence_score,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None
        }
    
    @classmethod
    def get_active_filters(cls, user_id=None, website_id=None, filter_type=None):
        """Get active keyword filters"""
        query = cls.query.filter_by(is_active=True)
        
        if filter_type:
            query = query.filter_by(filter_type=filter_type)
        
        # Build scope filter
        scope_conditions = [cls.scope == 'global']
        
        if user_id:
            scope_conditions.append(
                db.and_(cls.scope == 'user', cls.user_id == user_id)
            )
        
        if website_id:
            scope_conditions.append(
                db.and_(cls.scope == 'website', cls.website_id == website_id)
            )
        
        query = query.filter(db.or_(*scope_conditions))
        
        return query.all()


class FilterRule(db.Model):
    """Model for complex filtering rules combining multiple conditions"""
    
    __tablename__ = 'filter_rules'
    
    # Primary key
    id = db.Column(db.Integer, primary_key=True)
    
    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    website_id = db.Column(db.Integer, db.ForeignKey('websites.id'), nullable=True)
    
    # Rule details
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    rule_type = db.Column(db.String(20), nullable=False)  # 'blacklist' or 'whitelist'
    scope = db.Column(db.String(20), default='user')
    
    # Rule configuration (JSON)
    conditions = db.Column(db.JSON)  # Complex rule conditions
    actions = db.Column(db.JSON)     # Actions to take when rule matches
    
    # Rule metadata
    is_active = db.Column(db.Boolean, default=True)
    priority = db.Column(db.Integer, default=100)  # Higher number = higher priority
    
    # Statistics
    match_count = db.Column(db.Integer, default=0)
    last_matched_at = db.Column(db.DateTime)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='filter_rules')
    website = db.relationship('Website', backref='filter_rules')
    
    def __repr__(self):
        return f'<FilterRule {self.name} ({self.rule_type})>'
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'rule_type': self.rule_type,
            'scope': self.scope,
            'conditions': self.conditions,
            'actions': self.actions,
            'is_active': self.is_active,
            'priority': self.priority,
            'match_count': self.match_count,
            'last_matched_at': self.last_matched_at.isoformat() if self.last_matched_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def increment_match_count(self):
        """Increment match count and update last matched timestamp"""
        self.match_count += 1
        self.last_matched_at = datetime.utcnow()
        db.session.commit()
