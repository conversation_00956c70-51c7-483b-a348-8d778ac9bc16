"""
Production configuration for LinkUp Plugin Backend
"""
import os
from config.base import BaseConfig


class ProductionConfig(BaseConfig):
    """Production configuration"""
    
    DEBUG = False
    TESTING = False
    
    # Database configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://linkup_user:linkup_password@localhost/linkup_prod'
    
    # Production security
    SECRET_KEY = os.environ.get('SECRET_KEY')
    if not SECRET_KEY:
        raise ValueError("SECRET_KEY environment variable must be set in production")
    
    # Strict CORS for production
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '').split(',')
    
    # Production-specific feature flags
    FEATURE_FLAGS = {
        **BaseConfig.FEATURE_FLAGS,
        'DEBUG_MODE': False,
        'MOCK_EXTERNAL_SERVICES': False
    }
    
    @staticmethod
    def init_app(app):
        BaseConfig.init_app(app)
        
        # Production-specific initialization
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            file_handler = RotatingFileHandler(
                'logs/linkup.log', maxBytes=10240, backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('LinkUp Plugin startup')
