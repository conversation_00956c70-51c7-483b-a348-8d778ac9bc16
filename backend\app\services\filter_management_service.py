"""
Filter Management Service for Blacklist/Whitelist Functionality
Handles domain and keyword filtering with automatic spam detection
"""
import re
import logging
from typing import Dict, List, Optional, Tuple, Set
from datetime import datetime, timedelta
from collections import defaultdict

from app import db, cache
from app.models.blacklist_whitelist import DomainFilter, KeywordFilter, FilterRule
from app.models.website import Website
from app.models.analysis import ContentAnalysis

logger = logging.getLogger(__name__)


class FilterManagementService:
    """Comprehensive filter management for blacklist/whitelist functionality"""
    
    def __init__(self):
        """Initialize the filter management service"""
        self.cache_timeout = 3600  # 1 hour cache
        self.auto_spam_threshold = 0.8  # Threshold for automatic spam detection
        
        # Predefined spam patterns for automatic detection
        self.spam_domain_patterns = [
            r'.*\d{4,}.*',  # Domains with many numbers
            r'.*-{3,}.*',   # Multiple consecutive hyphens
            r'.*\.tk$|.*\.ml$|.*\.ga$|.*\.cf$',  # Free TLD domains
            r'.*buy.*cheap.*',
            r'.*free.*download.*',
            r'.*casino.*poker.*',
            r'.*viagra.*cialis.*',
            r'.*weight.*loss.*fast.*'
        ]
        
        self.spam_keyword_patterns = [
            'click here', 'buy now', 'limited time', 'act now',
            'guaranteed', 'make money fast', 'work from home',
            'lose weight fast', 'free trial', 'no obligation',
            'risk free', 'instant approval', 'cash advance'
        ]
        
        # Initialize default global filters
        self._initialize_default_filters()
    
    def _initialize_default_filters(self):
        """Initialize default global spam filters"""
        try:
            # Check if default filters already exist
            existing_count = DomainFilter.query.filter_by(
                scope='global', 
                added_by='system_default'
            ).count()
            
            if existing_count > 0:
                return  # Already initialized
            
            # Add default spam domains
            default_spam_domains = [
                'spam-site.com', 'low-quality-links.net', 'link-farm.org',
                'fake-authority.com', 'content-scraper.info', 'click-farm.net',
                'bot-traffic.com', 'fake-backlinks.org'
            ]
            
            for domain in default_spam_domains:
                self.add_domain_filter(
                    domain=domain,
                    filter_type='blacklist',
                    scope='global',
                    reason='Default spam domain',
                    added_by='system_default'
                )
            
            # Add default spam keywords
            for keyword in self.spam_keyword_patterns[:10]:  # Add first 10
                self.add_keyword_filter(
                    keyword=keyword,
                    filter_type='blacklist',
                    scope='global',
                    reason='Default spam keyword',
                    added_by='system_default'
                )
            
            logger.info("Initialized default spam filters")
            
        except Exception as e:
            logger.error(f"Error initializing default filters: {str(e)}")
    
    def add_domain_filter(self, domain: str, filter_type: str, scope: str = 'user',
                         user_id: Optional[int] = None, website_id: Optional[int] = None,
                         reason: Optional[str] = None, added_by: str = 'manual',
                         **kwargs) -> DomainFilter:
        """Add a domain filter"""
        try:
            # Normalize domain
            domain = domain.lower().strip()
            
            # Check if filter already exists
            existing = DomainFilter.query.filter_by(
                domain=domain,
                filter_type=filter_type,
                scope=scope,
                user_id=user_id,
                website_id=website_id
            ).first()
            
            if existing:
                if not existing.is_active:
                    existing.is_active = True
                    existing.updated_at = datetime.utcnow()
                    db.session.commit()
                return existing
            
            # Create new filter
            domain_filter = DomainFilter(
                domain=domain,
                filter_type=filter_type,
                scope=scope,
                user_id=user_id,
                website_id=website_id,
                reason=reason,
                added_by=added_by,
                **kwargs
            )
            
            db.session.add(domain_filter)
            db.session.commit()
            
            # Clear cache
            self._clear_filter_cache()
            
            logger.info(f"Added domain filter: {domain} ({filter_type})")
            return domain_filter
            
        except Exception as e:
            logger.error(f"Error adding domain filter: {str(e)}")
            db.session.rollback()
            raise
    
    def add_keyword_filter(self, keyword: str, filter_type: str, scope: str = 'user',
                          user_id: Optional[int] = None, website_id: Optional[int] = None,
                          reason: Optional[str] = None, added_by: str = 'manual',
                          **kwargs) -> KeywordFilter:
        """Add a keyword filter"""
        try:
            # Normalize keyword
            keyword = keyword.strip()
            
            # Check if filter already exists
            existing = KeywordFilter.query.filter_by(
                keyword=keyword,
                filter_type=filter_type,
                scope=scope,
                user_id=user_id,
                website_id=website_id
            ).first()
            
            if existing:
                if not existing.is_active:
                    existing.is_active = True
                    existing.updated_at = datetime.utcnow()
                    db.session.commit()
                return existing
            
            # Create new filter
            keyword_filter = KeywordFilter(
                keyword=keyword,
                filter_type=filter_type,
                scope=scope,
                user_id=user_id,
                website_id=website_id,
                reason=reason,
                added_by=added_by,
                **kwargs
            )
            
            db.session.add(keyword_filter)
            db.session.commit()
            
            # Clear cache
            self._clear_filter_cache()
            
            logger.info(f"Added keyword filter: {keyword} ({filter_type})")
            return keyword_filter
            
        except Exception as e:
            logger.error(f"Error adding keyword filter: {str(e)}")
            db.session.rollback()
            raise
    
    def is_domain_filtered(self, domain: str, user_id: Optional[int] = None,
                          website_id: Optional[int] = None) -> Tuple[bool, str, Optional[str]]:
        """
        Check if a domain is filtered
        Returns: (is_filtered, filter_type, reason)
        """
        try:
            domain = domain.lower().strip()
            
            # Get cached filters
            filters = self._get_cached_domain_filters(user_id, website_id)
            
            # Check exact domain matches first
            for filter_obj in filters:
                if self._domain_matches_filter(domain, filter_obj):
                    return True, filter_obj.filter_type, filter_obj.reason
            
            return False, 'none', None
            
        except Exception as e:
            logger.error(f"Error checking domain filter: {str(e)}")
            return False, 'none', None
    
    def is_content_filtered(self, content_data: Dict, user_id: Optional[int] = None,
                           website_id: Optional[int] = None) -> Tuple[bool, str, List[str]]:
        """
        Check if content is filtered by keyword filters
        Returns: (is_filtered, filter_type, matched_keywords)
        """
        try:
            # Get cached keyword filters
            filters = self._get_cached_keyword_filters(user_id, website_id)
            
            matched_keywords = []
            filter_type = 'none'
            
            for filter_obj in filters:
                matches = self._content_matches_keyword_filter(content_data, filter_obj)
                if matches:
                    matched_keywords.extend(matches)
                    filter_type = filter_obj.filter_type
                    
                    # If blacklisted, return immediately
                    if filter_type == 'blacklist':
                        return True, filter_type, matched_keywords
            
            # If we found whitelist matches, that's good
            if filter_type == 'whitelist' and matched_keywords:
                return False, filter_type, matched_keywords
            
            # If we have blacklist matches, filter it
            if matched_keywords:
                return True, 'blacklist', matched_keywords
            
            return False, 'none', []
            
        except Exception as e:
            logger.error(f"Error checking content filter: {str(e)}")
            return False, 'none', []
    
    def auto_detect_spam(self, website: Website, analysis: Optional[ContentAnalysis] = None) -> Dict:
        """Automatically detect spam and add to blacklist if confidence is high"""
        try:
            spam_indicators = []
            confidence_score = 0.0
            
            # Check domain patterns
            if website.domain:
                for pattern in self.spam_domain_patterns:
                    if re.search(pattern, website.domain.lower()):
                        spam_indicators.append(f"Suspicious domain pattern: {pattern}")
                        confidence_score += 0.2
            
            # Check content patterns
            if analysis:
                content_text = self._extract_content_text(analysis)
                for pattern in self.spam_keyword_patterns:
                    if pattern.lower() in content_text.lower():
                        spam_indicators.append(f"Spam keyword detected: {pattern}")
                        confidence_score += 0.1
            
            # Check website characteristics
            if website.created_at:
                age_days = (datetime.utcnow() - website.created_at).days
                if age_days < 30:
                    spam_indicators.append("Very new website (less than 30 days)")
                    confidence_score += 0.3
            
            # Check title for spam patterns
            if website.title:
                spam_words = ['free', 'cheap', 'buy now', 'click here', 'guaranteed']
                spam_count = sum(1 for word in spam_words if word in website.title.lower())
                if spam_count >= 2:
                    spam_indicators.append(f"Multiple spam words in title: {spam_count}")
                    confidence_score += spam_count * 0.1
            
            # Normalize confidence score
            confidence_score = min(1.0, confidence_score)
            
            result = {
                'is_spam': confidence_score >= self.auto_spam_threshold,
                'confidence_score': confidence_score,
                'spam_indicators': spam_indicators,
                'auto_blacklisted': False
            }
            
            # Auto-blacklist if confidence is high
            if result['is_spam'] and website.domain:
                try:
                    self.add_domain_filter(
                        domain=website.domain,
                        filter_type='blacklist',
                        scope='global',
                        reason=f"Auto-detected spam (confidence: {confidence_score:.2f})",
                        added_by='auto_spam',
                        confidence_score=confidence_score
                    )
                    result['auto_blacklisted'] = True
                    logger.warning(f"Auto-blacklisted spam domain: {website.domain}")
                except Exception as e:
                    logger.error(f"Error auto-blacklisting domain: {str(e)}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in auto spam detection: {str(e)}")
            return {'is_spam': False, 'confidence_score': 0.0, 'spam_indicators': []}
    
    def get_user_filters(self, user_id: int, filter_type: Optional[str] = None) -> Dict:
        """Get all filters for a user"""
        try:
            # Get domain filters
            domain_query = DomainFilter.query.filter(
                db.or_(
                    DomainFilter.scope == 'global',
                    db.and_(DomainFilter.scope == 'user', DomainFilter.user_id == user_id)
                ),
                DomainFilter.is_active == True
            )
            
            if filter_type:
                domain_query = domain_query.filter_by(filter_type=filter_type)
            
            domain_filters = domain_query.all()
            
            # Get keyword filters
            keyword_query = KeywordFilter.query.filter(
                db.or_(
                    KeywordFilter.scope == 'global',
                    db.and_(KeywordFilter.scope == 'user', KeywordFilter.user_id == user_id)
                ),
                KeywordFilter.is_active == True
            )
            
            if filter_type:
                keyword_query = keyword_query.filter_by(filter_type=filter_type)
            
            keyword_filters = keyword_query.all()
            
            return {
                'domain_filters': [f.to_dict() for f in domain_filters],
                'keyword_filters': [f.to_dict() for f in keyword_filters],
                'total_count': len(domain_filters) + len(keyword_filters)
            }
            
        except Exception as e:
            logger.error(f"Error getting user filters: {str(e)}")
            return {'domain_filters': [], 'keyword_filters': [], 'total_count': 0}
    
    def remove_filter(self, filter_id: int, filter_model: str, user_id: Optional[int] = None) -> bool:
        """Remove a filter (soft delete by setting is_active=False)"""
        try:
            if filter_model == 'domain':
                filter_obj = DomainFilter.query.get(filter_id)
            elif filter_model == 'keyword':
                filter_obj = KeywordFilter.query.get(filter_id)
            else:
                return False
            
            if not filter_obj:
                return False
            
            # Check permissions (users can only remove their own filters)
            if user_id and filter_obj.user_id != user_id and filter_obj.scope != 'global':
                return False
            
            filter_obj.is_active = False
            filter_obj.updated_at = datetime.utcnow()
            db.session.commit()
            
            # Clear cache
            self._clear_filter_cache()
            
            return True
            
        except Exception as e:
            logger.error(f"Error removing filter: {str(e)}")
            db.session.rollback()
            return False

    def _get_cached_domain_filters(self, user_id: Optional[int] = None,
                                  website_id: Optional[int] = None) -> List[DomainFilter]:
        """Get cached domain filters"""
        cache_key = f"domain_filters_{user_id}_{website_id}"

        filters = cache.get(cache_key)
        if filters is None:
            filters = DomainFilter.get_active_filters(user_id, website_id)
            cache.set(cache_key, filters, timeout=self.cache_timeout)

        return filters

    def _get_cached_keyword_filters(self, user_id: Optional[int] = None,
                                   website_id: Optional[int] = None) -> List[KeywordFilter]:
        """Get cached keyword filters"""
        cache_key = f"keyword_filters_{user_id}_{website_id}"

        filters = cache.get(cache_key)
        if filters is None:
            filters = KeywordFilter.get_active_filters(user_id, website_id)
            cache.set(cache_key, filters, timeout=self.cache_timeout)

        return filters

    def _clear_filter_cache(self):
        """Clear filter cache"""
        try:
            # Clear all filter-related cache keys
            cache.delete_many([
                key for key in cache.cache._cache.keys()
                if 'filters' in str(key)
            ])
        except Exception as e:
            logger.warning(f"Error clearing filter cache: {str(e)}")

    def _domain_matches_filter(self, domain: str, filter_obj: DomainFilter) -> bool:
        """Check if domain matches a filter"""
        try:
            filter_domain = filter_obj.domain.lower()

            if filter_obj.is_regex:
                return bool(re.search(filter_domain, domain))

            if filter_obj.match_subdomains:
                # Check if domain ends with filter domain (for subdomain matching)
                return domain == filter_domain or domain.endswith('.' + filter_domain)
            else:
                # Exact match only
                return domain == filter_domain

        except Exception as e:
            logger.warning(f"Error matching domain filter: {str(e)}")
            return False

    def _content_matches_keyword_filter(self, content_data: Dict,
                                       filter_obj: KeywordFilter) -> List[str]:
        """Check if content matches a keyword filter"""
        try:
            matches = []
            keyword = filter_obj.keyword

            # Prepare search text based on filter configuration
            search_texts = []

            if filter_obj.apply_to_title and content_data.get('title'):
                search_texts.append(content_data['title'])

            if filter_obj.apply_to_content and content_data.get('content'):
                search_texts.append(content_data['content'])

            if filter_obj.apply_to_description and content_data.get('description'):
                search_texts.append(content_data['description'])

            if filter_obj.apply_to_keywords and content_data.get('keywords'):
                if isinstance(content_data['keywords'], list):
                    search_texts.extend(content_data['keywords'])
                else:
                    search_texts.append(str(content_data['keywords']))

            # Search in all applicable text
            for text in search_texts:
                if not text:
                    continue

                search_text = text if filter_obj.case_sensitive else text.lower()
                search_keyword = keyword if filter_obj.case_sensitive else keyword.lower()

                if filter_obj.is_regex:
                    if re.search(search_keyword, search_text):
                        matches.append(keyword)
                        break
                else:
                    if filter_obj.match_type == 'exact':
                        if search_text == search_keyword:
                            matches.append(keyword)
                            break
                    elif filter_obj.match_type == 'contains':
                        if search_keyword in search_text:
                            matches.append(keyword)
                            break
                    elif filter_obj.match_type == 'starts_with':
                        if search_text.startswith(search_keyword):
                            matches.append(keyword)
                            break
                    elif filter_obj.match_type == 'ends_with':
                        if search_text.endswith(search_keyword):
                            matches.append(keyword)
                            break

            return matches

        except Exception as e:
            logger.warning(f"Error matching keyword filter: {str(e)}")
            return []

    def _extract_content_text(self, analysis: ContentAnalysis) -> str:
        """Extract text content from analysis for spam detection"""
        content_parts = []

        try:
            # Add keywords
            if analysis.keywords:
                keywords = analysis.keywords.get('primary_keywords', [])
                keyword_text = ' '.join([kw.get('keyword', '') for kw in keywords])
                content_parts.append(keyword_text)

            # Add topics
            if analysis.topics:
                topics = analysis.topics.get('primary_topics', [])
                topic_text = ' '.join([topic.get('topic', '') for topic in topics])
                content_parts.append(topic_text)

            # Add any content summary if available
            if hasattr(analysis, 'content_summary') and analysis.content_summary:
                content_parts.append(analysis.content_summary)

        except Exception as e:
            logger.warning(f"Error extracting content text: {str(e)}")

        return ' '.join(content_parts)

    def bulk_add_filters(self, filters_data: List[Dict], user_id: Optional[int] = None) -> Dict:
        """Bulk add multiple filters"""
        try:
            results = {
                'added': 0,
                'skipped': 0,
                'errors': []
            }

            for filter_data in filters_data:
                try:
                    filter_type = filter_data.get('type', 'domain')  # 'domain' or 'keyword'

                    if filter_type == 'domain':
                        self.add_domain_filter(
                            domain=filter_data['value'],
                            filter_type=filter_data.get('filter_type', 'blacklist'),
                            scope=filter_data.get('scope', 'user'),
                            user_id=user_id,
                            reason=filter_data.get('reason'),
                            added_by=filter_data.get('added_by', 'bulk_import')
                        )
                    elif filter_type == 'keyword':
                        self.add_keyword_filter(
                            keyword=filter_data['value'],
                            filter_type=filter_data.get('filter_type', 'blacklist'),
                            scope=filter_data.get('scope', 'user'),
                            user_id=user_id,
                            reason=filter_data.get('reason'),
                            added_by=filter_data.get('added_by', 'bulk_import')
                        )

                    results['added'] += 1

                except Exception as e:
                    results['errors'].append(f"Error adding {filter_data.get('value', 'unknown')}: {str(e)}")
                    results['skipped'] += 1

            return results

        except Exception as e:
            logger.error(f"Error in bulk filter addition: {str(e)}")
            return {'added': 0, 'skipped': 0, 'errors': [str(e)]}

    def get_filter_statistics(self, user_id: Optional[int] = None) -> Dict:
        """Get filter statistics"""
        try:
            stats = {
                'domain_filters': {
                    'blacklist': 0,
                    'whitelist': 0,
                    'global': 0,
                    'user': 0
                },
                'keyword_filters': {
                    'blacklist': 0,
                    'whitelist': 0,
                    'global': 0,
                    'user': 0
                },
                'recent_activity': []
            }

            # Count domain filters
            domain_filters = DomainFilter.query.filter_by(is_active=True)
            if user_id:
                domain_filters = domain_filters.filter(
                    db.or_(
                        DomainFilter.scope == 'global',
                        DomainFilter.user_id == user_id
                    )
                )

            for filter_obj in domain_filters:
                stats['domain_filters'][filter_obj.filter_type] += 1
                stats['domain_filters'][filter_obj.scope] += 1

            # Count keyword filters
            keyword_filters = KeywordFilter.query.filter_by(is_active=True)
            if user_id:
                keyword_filters = keyword_filters.filter(
                    db.or_(
                        KeywordFilter.scope == 'global',
                        KeywordFilter.user_id == user_id
                    )
                )

            for filter_obj in keyword_filters:
                stats['keyword_filters'][filter_obj.filter_type] += 1
                stats['keyword_filters'][filter_obj.scope] += 1

            # Get recent activity
            recent_domain = DomainFilter.query.filter_by(is_active=True).order_by(
                DomainFilter.created_at.desc()
            ).limit(5).all()

            recent_keyword = KeywordFilter.query.filter_by(is_active=True).order_by(
                KeywordFilter.created_at.desc()
            ).limit(5).all()

            # Combine and sort recent activity
            recent_activity = []
            for f in recent_domain:
                recent_activity.append({
                    'type': 'domain',
                    'value': f.domain,
                    'filter_type': f.filter_type,
                    'created_at': f.created_at.isoformat() if f.created_at else None
                })

            for f in recent_keyword:
                recent_activity.append({
                    'type': 'keyword',
                    'value': f.keyword,
                    'filter_type': f.filter_type,
                    'created_at': f.created_at.isoformat() if f.created_at else None
                })

            # Sort by creation date
            recent_activity.sort(key=lambda x: x['created_at'] or '', reverse=True)
            stats['recent_activity'] = recent_activity[:10]

            return stats

        except Exception as e:
            logger.error(f"Error getting filter statistics: {str(e)}")
            return {}
