"""
Test Suite for Sprint 4: Backlink Matching System
Tests the enhanced matching algorithms, quality scoring, and niche matching
"""
import pytest
import unittest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

# Import the services we've built
from app.services.matching_service import MatchingService
from app.services.advanced_content_matching import AdvancedContentMatcher
from app.services.niche_matching_service import NicheMatchingService
from app.services.quality_assessment_service import QualityAssessmentService, QualityMetrics


class TestAdvancedContentMatching(unittest.TestCase):
    """Test advanced content matching functionality"""
    
    def setUp(self):
        self.content_matcher = AdvancedContentMatcher()
    
    def test_content_matcher_initialization(self):
        """Test that content matcher initializes properly"""
        self.assertIsNotNone(self.content_matcher)
        self.assertIsNotNone(self.content_matcher.similarity_weights)
        self.assertEqual(len(self.content_matcher.similarity_weights), 5)
    
    def test_preprocess_content(self):
        """Test content preprocessing"""
        test_content = "This is a TEST content with SPECIAL characters!@#"
        processed = self.content_matcher._preprocess_content(test_content)
        
        self.assertIsInstance(processed, str)
        self.assertNotIn("!@#", processed)
        self.assertTrue(processed.islower())
    
    def test_semantic_relationship_detection(self):
        """Test semantic relationship detection"""
        # Test exact match
        self.assertTrue(self.content_matcher._are_semantically_related("technology", "technology"))
        
        # Test semantic group relationships
        self.assertTrue(self.content_matcher._are_semantically_related("technology", "software"))
        self.assertTrue(self.content_matcher._are_semantically_related("business", "corporate"))
        
        # Test unrelated terms
        self.assertFalse(self.content_matcher._are_semantically_related("technology", "cooking"))
    
    def test_topic_vector_creation(self):
        """Test topic vector creation"""
        topics = [
            {"topic": "technology", "weight": 0.8},
            {"topic": "business", "weight": 0.6}
        ]
        
        vector = self.content_matcher._create_topic_vector(topics)
        
        self.assertIsInstance(vector, list)
        self.assertEqual(len(vector), 10)  # Should have 10 common topics
        self.assertGreater(sum(vector), 0)  # Should have some non-zero values


class TestNicheMatchingService(unittest.TestCase):
    """Test niche matching functionality"""
    
    def setUp(self):
        self.niche_matcher = NicheMatchingService()
    
    def test_niche_matcher_initialization(self):
        """Test that niche matcher initializes with categories"""
        self.assertIsNotNone(self.niche_matcher)
        self.assertGreater(len(self.niche_matcher.categories), 0)
        self.assertIn("tech", self.niche_matcher.categories)
        self.assertIn("business", self.niche_matcher.categories)
    
    def test_category_hierarchy(self):
        """Test category hierarchy functionality"""
        # Test root category
        tech_hierarchy = self.niche_matcher.get_category_hierarchy("tech")
        self.assertEqual(tech_hierarchy['depth'], 1)
        self.assertEqual(tech_hierarchy['root'], "tech")
        
        # Test sub-category
        software_hierarchy = self.niche_matcher.get_category_hierarchy("tech_software")
        self.assertEqual(software_hierarchy['depth'], 2)
        self.assertEqual(software_hierarchy['root'], "tech")
    
    def test_niche_compatibility_calculation(self):
        """Test niche compatibility calculation"""
        # Test identical categories
        result = self.niche_matcher.calculate_niche_compatibility("tech", "tech")
        self.assertEqual(result['base_compatibility'], 1.0)
        
        # Test compatible categories
        result = self.niche_matcher.calculate_niche_compatibility("tech", "business")
        self.assertGreater(result['total_score'], 0.5)
        
        # Test explanation generation
        self.assertIsInstance(result['explanation'], list)
        self.assertGreater(len(result['explanation']), 0)
    
    def test_find_best_niche_matches(self):
        """Test finding best niche matches"""
        matches = self.niche_matcher.find_best_niche_matches("tech", limit=5)
        
        self.assertIsInstance(matches, list)
        self.assertLessEqual(len(matches), 5)
        
        if matches:
            # Check match structure
            match = matches[0]
            self.assertIn('category_id', match)
            self.assertIn('compatibility_score', match)
            self.assertIn('category_name', match)
    
    def test_cross_niche_opportunities(self):
        """Test cross-niche opportunity identification"""
        categories = ["tech", "business", "health"]
        opportunities = self.niche_matcher.get_cross_niche_opportunities(categories)
        
        self.assertIn('high_compatibility', opportunities)
        self.assertIn('complementary_pairs', opportunities)
        self.assertIn('expansion_suggestions', opportunities)
        self.assertIsInstance(opportunities['high_compatibility'], list)


class TestQualityAssessmentService(unittest.TestCase):
    """Test quality assessment functionality"""
    
    def setUp(self):
        self.quality_assessor = QualityAssessmentService()
    
    def test_quality_assessor_initialization(self):
        """Test quality assessor initialization"""
        self.assertIsNotNone(self.quality_assessor)
        self.assertIsNotNone(self.quality_assessor.quality_weights)
        self.assertIsNotNone(self.quality_assessor.spam_patterns)
        self.assertIsNotNone(self.quality_assessor.trust_signals)
    
    def test_spam_score_calculation(self):
        """Test spam score calculation"""
        # Create mock website with suspicious characteristics
        mock_website = Mock()
        mock_website.domain = "buy-cheap-123456.tk"
        mock_website.title = "Buy Cheap Products Now! Click Here!"
        mock_website.created_at = datetime.utcnow() - timedelta(days=10)  # Very new
        
        spam_score = self.quality_assessor._calculate_spam_score(mock_website, None)
        
        self.assertGreater(spam_score, 5.0)  # Should be high spam score
    
    def test_quality_level_determination(self):
        """Test quality level determination"""
        # Test excellent quality
        excellent_level = self.quality_assessor._determine_quality_level(9.5)
        self.assertEqual(excellent_level, "EXCELLENT")
        
        # Test poor quality
        poor_level = self.quality_assessor._determine_quality_level(4.5)
        self.assertEqual(poor_level, "POOR")
        
        # Test spam level
        spam_level = self.quality_assessor._determine_quality_level(1.0)
        self.assertEqual(spam_level, "SPAM")
    
    def test_red_flags_identification(self):
        """Test red flag identification"""
        # Create mock website and analysis
        mock_website = Mock()
        mock_website.domain = "spam-site.com"
        mock_website.created_at = datetime.utcnow() - timedelta(days=5)
        mock_website.last_analyzed_at = None
        
        mock_analysis = Mock()
        
        # Create quality metrics with issues
        metrics = QualityMetrics()
        metrics.spam_score = 8.0
        metrics.content_quality = 3.0
        metrics.authority_signals = 2.0
        
        red_flags = self.quality_assessor._identify_red_flags(mock_website, mock_analysis, metrics)
        
        self.assertIsInstance(red_flags, list)
        self.assertGreater(len(red_flags), 0)
        self.assertTrue(any("spam" in flag.lower() for flag in red_flags))
    
    def test_positive_signals_identification(self):
        """Test positive signal identification"""
        # Create mock website with good characteristics
        mock_website = Mock()
        mock_website.domain = "professional-company.com"
        mock_website.created_at = datetime.utcnow() - timedelta(days=800)  # 2+ years old
        mock_website.last_analyzed_at = datetime.utcnow() - timedelta(days=10)
        
        mock_analysis = Mock()
        
        # Create quality metrics with good scores
        metrics = QualityMetrics()
        metrics.overall_score = 8.5
        metrics.content_quality = 8.0
        metrics.authority_signals = 8.5
        metrics.spam_score = 0.5
        
        positive_signals = self.quality_assessor._identify_positive_signals(mock_website, mock_analysis, metrics)
        
        self.assertIsInstance(positive_signals, list)
        self.assertGreater(len(positive_signals), 0)


class TestEnhancedMatchingService(unittest.TestCase):
    """Test the enhanced matching service integration"""
    
    def setUp(self):
        self.matching_service = MatchingService()
    
    def test_matching_service_initialization(self):
        """Test that matching service initializes with all components"""
        self.assertIsNotNone(self.matching_service)
        self.assertIsNotNone(self.matching_service.advanced_matcher)
        self.assertIsNotNone(self.matching_service.niche_matcher)
        self.assertIsNotNone(self.matching_service.quality_assessor)
    
    def test_blacklist_whitelist_functionality(self):
        """Test blacklist and whitelist functionality"""
        # Test domain blacklisting
        self.assertFalse(self.matching_service.is_domain_blacklisted("good-site.com"))
        
        # Add to blacklist
        self.matching_service.add_to_blacklist("spam-site.com")
        self.assertTrue(self.matching_service.is_domain_blacklisted("spam-site.com"))
        
        # Test whitelisting
        self.matching_service.add_to_whitelist("trusted-site.com")
        self.assertTrue(self.matching_service.is_domain_whitelisted("trusted-site.com"))
    
    def test_enhanced_weights_configuration(self):
        """Test that enhanced weights are properly configured"""
        weights = self.matching_service.weights
        
        # Check that all expected components are present
        expected_components = [
            'content_similarity', 'category_match', 'quality_score',
            'domain_authority', 'language_match', 'freshness',
            'mutual_benefit', 'traffic_compatibility', 'niche_authority'
        ]
        
        for component in expected_components:
            self.assertIn(component, weights)
            self.assertGreater(weights[component], 0)
        
        # Check that weights sum to approximately 1.0
        total_weight = sum(weights.values())
        self.assertAlmostEqual(total_weight, 1.0, places=1)
    
    def test_enhanced_thresholds(self):
        """Test enhanced quality thresholds"""
        thresholds = self.matching_service.thresholds
        
        # Check new thresholds are present
        self.assertIn('min_niche_authority', thresholds)
        self.assertIn('max_spam_score', thresholds)
        self.assertIn('min_trust_signals', thresholds)


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
