"""
Match Scoring Transparency Service
Provides detailed explanations and debugging tools for match scoring
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from app.models.website import Website
from app.models.analysis import ContentAnalysis
from app.services.matching_service import MatchingService
from app.services.quality_assessment_service import QualityAssessmentService
from app.services.niche_matching_service import NicheMatchingService

logger = logging.getLogger(__name__)


class ScoreComponent(Enum):
    """Enumeration of scoring components"""
    CONTENT_SIMILARITY = "content_similarity"
    CATEGORY_MATCH = "category_match"
    QUALITY_SCORE = "quality_score"
    DOMAIN_AUTHORITY = "domain_authority"
    LANGUAGE_MATCH = "language_match"
    FRESHNESS = "freshness"
    MUTUAL_BENEFIT = "mutual_benefit"
    TRAFFIC_COMPATIBILITY = "traffic_compatibility"
    NICHE_AUTHORITY = "niche_authority"


@dataclass
class ScoreExplanation:
    """Detailed explanation for a score component"""
    component: str
    score: float
    weight: float
    contribution: float
    explanation: str
    factors: List[Dict]
    improvement_suggestions: List[str]


@dataclass
class MatchExplanation:
    """Complete explanation for a match score"""
    source_website_id: int
    target_website_id: int
    overall_score: float
    component_explanations: List[ScoreExplanation]
    decision_factors: List[str]
    improvement_suggestions: List[str]
    confidence_level: str
    explanation_timestamp: datetime


class MatchTransparencyService:
    """Service for providing transparent match scoring explanations"""
    
    def __init__(self):
        """Initialize the transparency service"""
        self.matching_service = MatchingService()
        self.quality_service = QualityAssessmentService()
        self.niche_service = NicheMatchingService()
        
        # Confidence level thresholds
        self.confidence_thresholds = {
            'very_high': 0.9,
            'high': 0.8,
            'medium': 0.6,
            'low': 0.4,
            'very_low': 0.0
        }
        
        # Component importance for explanations
        self.component_importance = {
            ScoreComponent.CONTENT_SIMILARITY: 'critical',
            ScoreComponent.QUALITY_SCORE: 'critical',
            ScoreComponent.CATEGORY_MATCH: 'important',
            ScoreComponent.DOMAIN_AUTHORITY: 'important',
            ScoreComponent.LANGUAGE_MATCH: 'moderate',
            ScoreComponent.FRESHNESS: 'moderate',
            ScoreComponent.MUTUAL_BENEFIT: 'moderate',
            ScoreComponent.TRAFFIC_COMPATIBILITY: 'moderate',
            ScoreComponent.NICHE_AUTHORITY: 'moderate'
        }
    
    def explain_match_score(self, source_website_id: int, target_website_id: int) -> MatchExplanation:
        """Generate comprehensive explanation for a match score"""
        try:
            # Get websites and analyses
            source_website = Website.query.get(source_website_id)
            target_website = Website.query.get(target_website_id)
            
            if not source_website or not target_website:
                raise ValueError("One or both websites not found")
            
            source_analysis = self.matching_service._get_latest_analysis(source_website_id)
            target_analysis = self.matching_service._get_latest_analysis(target_website_id)
            
            if not source_analysis or not target_analysis:
                raise ValueError("Content analysis not available for one or both websites")
            
            # Calculate match score with detailed breakdown
            match_scores = self.matching_service._calculate_match_score(
                source_website, source_analysis,
                target_website, target_analysis
            )
            
            # Generate explanations for each component
            component_explanations = []
            
            for component in ScoreComponent:
                explanation = self._explain_score_component(
                    component, match_scores, source_website, target_website,
                    source_analysis, target_analysis
                )
                if explanation:
                    component_explanations.append(explanation)
            
            # Generate overall decision factors
            decision_factors = self._generate_decision_factors(match_scores, component_explanations)
            
            # Generate improvement suggestions
            improvement_suggestions = self._generate_improvement_suggestions(
                match_scores, component_explanations, source_website, target_website
            )
            
            # Determine confidence level
            confidence_level = self._determine_confidence_level(match_scores['total_score'])
            
            return MatchExplanation(
                source_website_id=source_website_id,
                target_website_id=target_website_id,
                overall_score=match_scores['total_score'],
                component_explanations=component_explanations,
                decision_factors=decision_factors,
                improvement_suggestions=improvement_suggestions,
                confidence_level=confidence_level,
                explanation_timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Error explaining match score: {str(e)}")
            raise
    
    def _explain_score_component(self, component: ScoreComponent, match_scores: Dict,
                                source_website: Website, target_website: Website,
                                source_analysis: ContentAnalysis, target_analysis: ContentAnalysis) -> Optional[ScoreExplanation]:
        """Generate explanation for a specific score component"""
        try:
            component_key = component.value
            if component_key not in match_scores:
                return None
            
            score = match_scores[component_key]
            weight = self.matching_service.weights.get(component_key, 0)
            contribution = score * weight
            
            # Generate component-specific explanation
            explanation, factors, suggestions = self._get_component_details(
                component, score, source_website, target_website,
                source_analysis, target_analysis
            )
            
            return ScoreExplanation(
                component=component_key,
                score=score,
                weight=weight,
                contribution=contribution,
                explanation=explanation,
                factors=factors,
                improvement_suggestions=suggestions
            )
            
        except Exception as e:
            logger.warning(f"Error explaining component {component}: {str(e)}")
            return None
    
    def _get_component_details(self, component: ScoreComponent, score: float,
                              source_website: Website, target_website: Website,
                              source_analysis: ContentAnalysis, target_analysis: ContentAnalysis) -> Tuple[str, List[Dict], List[str]]:
        """Get detailed explanation for a specific component"""
        
        if component == ScoreComponent.CONTENT_SIMILARITY:
            return self._explain_content_similarity(score, source_analysis, target_analysis)
        
        elif component == ScoreComponent.CATEGORY_MATCH:
            return self._explain_category_match(score, source_website, target_website)
        
        elif component == ScoreComponent.QUALITY_SCORE:
            return self._explain_quality_score(score, source_analysis, target_analysis)
        
        elif component == ScoreComponent.DOMAIN_AUTHORITY:
            return self._explain_domain_authority(score, source_website, target_website)
        
        elif component == ScoreComponent.LANGUAGE_MATCH:
            return self._explain_language_match(score, source_analysis, target_analysis)
        
        elif component == ScoreComponent.FRESHNESS:
            return self._explain_freshness(score, source_analysis, target_analysis)
        
        elif component == ScoreComponent.MUTUAL_BENEFIT:
            return self._explain_mutual_benefit(score, source_website, target_website)
        
        elif component == ScoreComponent.TRAFFIC_COMPATIBILITY:
            return self._explain_traffic_compatibility(score, source_website, target_website)
        
        elif component == ScoreComponent.NICHE_AUTHORITY:
            return self._explain_niche_authority(score, target_website, target_analysis)
        
        else:
            return f"Score: {score:.2f}", [], []
    
    def _explain_content_similarity(self, score: float, source_analysis: ContentAnalysis, 
                                   target_analysis: ContentAnalysis) -> Tuple[str, List[Dict], List[str]]:
        """Explain content similarity score"""
        factors = []
        suggestions = []
        
        # Analyze keyword overlap
        source_keywords = source_analysis.keywords.get('primary_keywords', []) if source_analysis.keywords else []
        target_keywords = target_analysis.keywords.get('primary_keywords', []) if target_analysis.keywords else []
        
        source_terms = {kw.get('keyword', '').lower() for kw in source_keywords}
        target_terms = {kw.get('keyword', '').lower() for kw in target_keywords}
        
        overlap = source_terms & target_terms
        overlap_ratio = len(overlap) / max(len(source_terms), len(target_terms), 1)
        
        factors.append({
            'factor': 'Keyword Overlap',
            'value': f"{len(overlap)} common keywords ({overlap_ratio:.1%})",
            'impact': 'high' if overlap_ratio > 0.3 else 'medium' if overlap_ratio > 0.1 else 'low'
        })
        
        if score < 0.5:
            suggestions.extend([
                "Consider targeting more similar keywords",
                "Focus on content in the same topic area",
                "Use related semantic keywords"
            ])
        
        if score >= 0.8:
            explanation = f"Excellent content similarity ({score:.1%}). Your content topics align very well."
        elif score >= 0.6:
            explanation = f"Good content similarity ({score:.1%}). Content topics are related."
        elif score >= 0.4:
            explanation = f"Moderate content similarity ({score:.1%}). Some topic overlap exists."
        else:
            explanation = f"Low content similarity ({score:.1%}). Content topics are quite different."
        
        return explanation, factors, suggestions
    
    def _explain_category_match(self, score: float, source_website: Website, 
                               target_website: Website) -> Tuple[str, List[Dict], List[str]]:
        """Explain category match score"""
        factors = []
        suggestions = []
        
        source_category = source_website.category or 'uncategorized'
        target_category = target_website.category or 'uncategorized'
        
        factors.append({
            'factor': 'Source Category',
            'value': source_category,
            'impact': 'reference'
        })
        
        factors.append({
            'factor': 'Target Category',
            'value': target_category,
            'impact': 'reference'
        })
        
        if score == 1.0:
            explanation = f"Perfect category match. Both websites are in '{source_category}' category."
        elif score >= 0.7:
            explanation = f"Good category compatibility. '{source_category}' and '{target_category}' are related."
        elif score >= 0.5:
            explanation = f"Moderate category compatibility between '{source_category}' and '{target_category}'."
        else:
            explanation = f"Low category compatibility. '{source_category}' and '{target_category}' are different niches."
            suggestions.append("Consider targeting websites in more related categories")
        
        return explanation, factors, suggestions
    
    def _explain_quality_score(self, score: float, source_analysis: ContentAnalysis,
                              target_analysis: ContentAnalysis) -> Tuple[str, List[Dict], List[str]]:
        """Explain quality compatibility score"""
        factors = []
        suggestions = []
        
        source_quality = source_analysis.quality_score or 0
        target_quality = target_analysis.quality_score or 0
        
        factors.extend([
            {
                'factor': 'Source Quality',
                'value': f"{source_quality:.1f}/10",
                'impact': 'high' if source_quality >= 7 else 'medium' if source_quality >= 5 else 'low'
            },
            {
                'factor': 'Target Quality',
                'value': f"{target_quality:.1f}/10",
                'impact': 'high' if target_quality >= 7 else 'medium' if target_quality >= 5 else 'low'
            }
        ])
        
        quality_diff = abs(source_quality - target_quality)
        
        if score >= 0.8:
            explanation = f"Excellent quality compatibility. Both sites have similar high quality levels."
        elif score >= 0.6:
            explanation = f"Good quality compatibility. Quality levels are reasonably matched."
        else:
            explanation = f"Quality mismatch detected. Quality difference: {quality_diff:.1f} points."
            if target_quality < 6:
                suggestions.append("Target higher quality websites for better partnerships")
            if source_quality < target_quality:
                suggestions.append("Improve your content quality to match higher-quality partners")
        
        return explanation, factors, suggestions
    
    def _explain_domain_authority(self, score: float, source_website: Website,
                                 target_website: Website) -> Tuple[str, List[Dict], List[str]]:
        """Explain domain authority score"""
        factors = []
        suggestions = []
        
        source_da = getattr(source_website, 'domain_authority', 0) or 0
        target_da = getattr(target_website, 'domain_authority', 0) or 0
        
        factors.extend([
            {
                'factor': 'Source Domain Authority',
                'value': f"{source_da}/100",
                'impact': 'high' if source_da >= 50 else 'medium' if source_da >= 30 else 'low'
            },
            {
                'factor': 'Target Domain Authority',
                'value': f"{target_da}/100",
                'impact': 'high' if target_da >= 50 else 'medium' if target_da >= 30 else 'low'
            }
        ])
        
        if score >= 0.8:
            explanation = f"Strong domain authority match. Both sites have good authority levels."
        elif score >= 0.6:
            explanation = f"Moderate domain authority compatibility."
        else:
            explanation = f"Domain authority mismatch. Consider the authority gap in partnership value."
            if source_da < target_da:
                suggestions.append("Focus on building your domain authority to attract higher-authority partners")
            else:
                suggestions.append("Consider the mutual benefit when partnering with lower-authority sites")
        
        return explanation, factors, suggestions
    
    def _explain_language_match(self, score: float, source_analysis: ContentAnalysis,
                               target_analysis: ContentAnalysis) -> Tuple[str, List[Dict], List[str]]:
        """Explain language match score"""
        factors = []
        suggestions = []
        
        source_lang = getattr(source_analysis, 'language', 'en') or 'en'
        target_lang = getattr(target_analysis, 'language', 'en') or 'en'
        
        factors.extend([
            {
                'factor': 'Source Language',
                'value': source_lang,
                'impact': 'reference'
            },
            {
                'factor': 'Target Language',
                'value': target_lang,
                'impact': 'reference'
            }
        ])
        
        if score == 1.0:
            explanation = f"Perfect language match. Both sites use {source_lang}."
        else:
            explanation = f"Language mismatch. Source: {source_lang}, Target: {target_lang}."
            suggestions.append("Consider targeting websites in the same language for better audience alignment")
        
        return explanation, factors, suggestions

    def _explain_freshness(self, score: float, source_analysis: ContentAnalysis,
                          target_analysis: ContentAnalysis) -> Tuple[str, List[Dict], List[str]]:
        """Explain content freshness score"""
        factors = []
        suggestions = []

        source_age = (datetime.utcnow() - source_analysis.analyzed_at).days if source_analysis.analyzed_at else 999
        target_age = (datetime.utcnow() - target_analysis.analyzed_at).days if target_analysis.analyzed_at else 999

        factors.extend([
            {
                'factor': 'Source Content Age',
                'value': f"{source_age} days",
                'impact': 'high' if source_age <= 30 else 'medium' if source_age <= 90 else 'low'
            },
            {
                'factor': 'Target Content Age',
                'value': f"{target_age} days",
                'impact': 'high' if target_age <= 30 else 'medium' if target_age <= 90 else 'low'
            }
        ])

        if score >= 0.8:
            explanation = "Both sites have fresh, recently updated content."
        elif score >= 0.6:
            explanation = "Content is reasonably fresh on both sites."
        else:
            explanation = "One or both sites have outdated content."
            suggestions.append("Update content regularly to improve freshness scores")

        return explanation, factors, suggestions

    def _explain_mutual_benefit(self, score: float, source_website: Website,
                               target_website: Website) -> Tuple[str, List[Dict], List[str]]:
        """Explain mutual benefit score"""
        factors = []
        suggestions = []

        source_da = getattr(source_website, 'domain_authority', 0) or 0
        target_da = getattr(target_website, 'domain_authority', 0) or 0

        factors.append({
            'factor': 'Authority Balance',
            'value': f"Source: {source_da}, Target: {target_da}",
            'impact': 'high' if abs(source_da - target_da) <= 20 else 'medium'
        })

        if score >= 0.8:
            explanation = "Excellent mutual benefit potential. Both parties gain value."
        elif score >= 0.6:
            explanation = "Good mutual benefit. Partnership offers value to both sides."
        else:
            explanation = "Limited mutual benefit. Consider what value you can offer."
            suggestions.append("Focus on partnerships where you can provide clear value")

        return explanation, factors, suggestions

    def _explain_traffic_compatibility(self, score: float, source_website: Website,
                                     target_website: Website) -> Tuple[str, List[Dict], List[str]]:
        """Explain traffic compatibility score"""
        factors = []
        suggestions = []

        source_da = getattr(source_website, 'domain_authority', 0) or 0
        target_da = getattr(target_website, 'domain_authority', 0) or 0

        factors.append({
            'factor': 'Traffic Level Compatibility',
            'value': f"Based on DA similarity ({source_da} vs {target_da})",
            'impact': 'medium'
        })

        if score >= 0.8:
            explanation = "Excellent traffic compatibility. Similar audience sizes."
        elif score >= 0.6:
            explanation = "Good traffic compatibility."
        else:
            explanation = "Traffic level mismatch may affect partnership effectiveness."
            suggestions.append("Consider targeting sites with similar traffic levels")

        return explanation, factors, suggestions

    def _explain_niche_authority(self, score: float, target_website: Website,
                                target_analysis: ContentAnalysis) -> Tuple[str, List[Dict], List[str]]:
        """Explain niche authority score"""
        factors = []
        suggestions = []

        target_da = getattr(target_website, 'domain_authority', 0) or 0
        target_quality = target_analysis.quality_score or 0

        factors.extend([
            {
                'factor': 'Domain Authority',
                'value': f"{target_da}/100",
                'impact': 'high' if target_da >= 50 else 'medium'
            },
            {
                'factor': 'Content Quality',
                'value': f"{target_quality:.1f}/10",
                'impact': 'high' if target_quality >= 7 else 'medium'
            }
        ])

        if score >= 0.8:
            explanation = "Target site has strong niche authority."
        elif score >= 0.6:
            explanation = "Target site has moderate niche authority."
        else:
            explanation = "Target site has limited niche authority."
            suggestions.append("Target sites with stronger niche authority for better link value")

        return explanation, factors, suggestions

    def _generate_decision_factors(self, match_scores: Dict,
                                  component_explanations: List[ScoreExplanation]) -> List[str]:
        """Generate key decision factors for the match"""
        factors = []

        # Identify strongest components
        strong_components = [exp for exp in component_explanations if exp.score >= 0.7]
        weak_components = [exp for exp in component_explanations if exp.score < 0.4]

        if strong_components:
            strongest = max(strong_components, key=lambda x: x.contribution)
            factors.append(f"Strongest factor: {strongest.component.replace('_', ' ').title()} ({strongest.score:.1%})")

        if weak_components:
            weakest = min(weak_components, key=lambda x: x.score)
            factors.append(f"Weakest factor: {weakest.component.replace('_', ' ').title()} ({weakest.score:.1%})")

        # Overall assessment
        overall_score = match_scores.get('total_score', 0)
        if overall_score >= 0.8:
            factors.append("This is a high-quality match with strong compatibility")
        elif overall_score >= 0.6:
            factors.append("This is a good match with solid potential")
        elif overall_score >= 0.4:
            factors.append("This is a moderate match that may require careful consideration")
        else:
            factors.append("This is a low-quality match with limited compatibility")

        return factors

    def _generate_improvement_suggestions(self, match_scores: Dict,
                                        component_explanations: List[ScoreExplanation],
                                        source_website: Website, target_website: Website) -> List[str]:
        """Generate suggestions for improving match scores"""
        suggestions = []

        # Collect suggestions from components
        for explanation in component_explanations:
            suggestions.extend(explanation.improvement_suggestions)

        # Add general suggestions based on overall score
        overall_score = match_scores.get('total_score', 0)

        if overall_score < 0.6:
            suggestions.extend([
                "Consider improving your content quality and relevance",
                "Focus on building domain authority through quality backlinks",
                "Ensure your content targets similar keywords and topics"
            ])

        # Remove duplicates while preserving order
        unique_suggestions = []
        seen = set()
        for suggestion in suggestions:
            if suggestion not in seen:
                unique_suggestions.append(suggestion)
                seen.add(suggestion)

        return unique_suggestions[:10]  # Limit to top 10 suggestions

    def _determine_confidence_level(self, score: float) -> str:
        """Determine confidence level based on score"""
        for level, threshold in sorted(self.confidence_thresholds.items(),
                                     key=lambda x: x[1], reverse=True):
            if score >= threshold:
                return level
        return 'very_low'

    def get_score_debugging_info(self, source_website_id: int, target_website_id: int) -> Dict:
        """Get detailed debugging information for score calculation"""
        try:
            explanation = self.explain_match_score(source_website_id, target_website_id)

            # Create debugging breakdown
            debug_info = {
                'overall_score': explanation.overall_score,
                'confidence_level': explanation.confidence_level,
                'component_breakdown': {},
                'weight_distribution': {},
                'score_calculation_steps': [],
                'potential_issues': [],
                'optimization_opportunities': []
            }

            total_weighted_score = 0
            total_weight = 0

            for comp_exp in explanation.component_explanations:
                debug_info['component_breakdown'][comp_exp.component] = {
                    'raw_score': comp_exp.score,
                    'weight': comp_exp.weight,
                    'weighted_contribution': comp_exp.contribution,
                    'explanation': comp_exp.explanation,
                    'factors': comp_exp.factors
                }

                debug_info['weight_distribution'][comp_exp.component] = comp_exp.weight
                total_weighted_score += comp_exp.contribution
                total_weight += comp_exp.weight

                # Add calculation step
                debug_info['score_calculation_steps'].append(
                    f"{comp_exp.component}: {comp_exp.score:.3f} × {comp_exp.weight:.3f} = {comp_exp.contribution:.3f}"
                )

                # Identify potential issues
                if comp_exp.score < 0.3:
                    debug_info['potential_issues'].append(
                        f"Very low {comp_exp.component.replace('_', ' ')} score ({comp_exp.score:.1%})"
                    )

                # Identify optimization opportunities
                if comp_exp.weight > 0.1 and comp_exp.score < 0.6:
                    debug_info['optimization_opportunities'].append(
                        f"Improve {comp_exp.component.replace('_', ' ')} (high weight, low score)"
                    )

            # Add final calculation
            debug_info['score_calculation_steps'].append(
                f"Total: {total_weighted_score:.3f} (normalized: {explanation.overall_score:.3f})"
            )

            # Add weight validation
            debug_info['weight_validation'] = {
                'total_weight': total_weight,
                'is_normalized': abs(total_weight - 1.0) < 0.01,
                'weight_distribution_balanced': max(debug_info['weight_distribution'].values()) < 0.4
            }

            return debug_info

        except Exception as e:
            logger.error(f"Error getting debugging info: {str(e)}")
            return {'error': str(e)}
