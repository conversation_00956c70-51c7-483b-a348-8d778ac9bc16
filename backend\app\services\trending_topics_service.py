"""
Trending Topics Identification Service
Identifies trending topics and keywords in user's niche
"""
import logging
import requests
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import re
from collections import defaultdict, Counter

from app import db, cache
from app.models.website import Website
from app.services.keyword_research_api_service import KeywordResearchAPIService, APIProvider

logger = logging.getLogger(__name__)


class TrendSource(Enum):
    """Sources for trending data"""
    GOOGLE_TRENDS = "google_trends"
    TWITTER_API = "twitter_api"
    REDDIT_API = "reddit_api"
    NEWS_API = "news_api"
    SOCIAL_MEDIA = "social_media"
    SEARCH_ENGINES = "search_engines"
    INDUSTRY_BLOGS = "industry_blogs"
    MOCK_DATA = "mock_data"


class TrendTimeframe(Enum):
    """Timeframes for trend analysis"""
    LAST_24H = "24h"
    LAST_7D = "7d"
    LAST_30D = "30d"
    LAST_90D = "90d"
    LAST_YEAR = "1y"


@dataclass
class TrendingTopic:
    """Represents a trending topic"""
    topic: str
    trend_score: float
    search_volume_change: float
    social_mentions: int
    news_mentions: int
    related_keywords: List[str]
    sentiment_score: float
    trend_direction: str  # 'rising', 'falling', 'stable'
    peak_date: datetime
    source: TrendSource
    niche_relevance: float
    content_opportunities: List[str]
    estimated_traffic_potential: int
    competition_level: str
    urgency_score: float
    created_at: datetime


@dataclass
class NicheTrendAnalysis:
    """Analysis of trends in a specific niche"""
    niche: str
    analysis_date: datetime
    trending_topics: List[TrendingTopic]
    emerging_keywords: List[str]
    declining_topics: List[str]
    seasonal_patterns: Dict[str, float]
    competitor_trending_content: List[Dict]
    content_gap_opportunities: List[str]
    recommended_actions: List[str]


class TrendingTopicsService:
    """Service for identifying trending topics and keywords"""
    
    def __init__(self):
        """Initialize the trending topics service"""
        self.keyword_research_service = KeywordResearchAPIService()
        self.cache_timeout = 3600 * 6  # 6 hours for trending data
        
        # API configurations for trend sources
        self.trend_apis = {
            TrendSource.GOOGLE_TRENDS: {
                'base_url': 'https://trends.google.com/trends/api',
                'requires_auth': False
            },
            TrendSource.TWITTER_API: {
                'base_url': 'https://api.twitter.com/2',
                'requires_auth': True,
                'api_key': None  # Set from environment
            },
            TrendSource.REDDIT_API: {
                'base_url': 'https://www.reddit.com/api/v1',
                'requires_auth': False
            },
            TrendSource.NEWS_API: {
                'base_url': 'https://newsapi.org/v2',
                'requires_auth': True,
                'api_key': None  # Set from environment
            }
        }
        
        # Niche-specific trend sources
        self.niche_sources = {
            'technology': ['techcrunch.com', 'wired.com', 'theverge.com', 'ars-technica.com'],
            'business': ['forbes.com', 'bloomberg.com', 'businessinsider.com'],
            'health': ['webmd.com', 'healthline.com', 'mayoclinic.org'],
            'finance': ['investopedia.com', 'fool.com', 'marketwatch.com'],
            'lifestyle': ['buzzfeed.com', 'huffpost.com', 'refinery29.com']
        }
        
        # Trend scoring weights
        self.scoring_weights = {
            'search_volume_change': 0.3,
            'social_mentions': 0.25,
            'news_mentions': 0.2,
            'niche_relevance': 0.15,
            'recency': 0.1
        }
    
    def identify_trending_topics(self, niche: str, timeframe: TrendTimeframe = TrendTimeframe.LAST_7D,
                                limit: int = 20) -> NicheTrendAnalysis:
        """Identify trending topics in a specific niche"""
        try:
            # Check cache first
            cache_key = f"trending_topics_{niche}_{timeframe.value}_{limit}"
            cached_result = cache.get(cache_key)
            if cached_result:
                return NicheTrendAnalysis(**cached_result)
            
            # Gather trending data from multiple sources
            trending_topics = []
            
            # Get trends from Google Trends
            google_trends = self._get_google_trends(niche, timeframe)
            trending_topics.extend(google_trends)
            
            # Get trends from social media
            social_trends = self._get_social_media_trends(niche, timeframe)
            trending_topics.extend(social_trends)
            
            # Get trends from news sources
            news_trends = self._get_news_trends(niche, timeframe)
            trending_topics.extend(news_trends)
            
            # Get trends from industry-specific sources
            industry_trends = self._get_industry_trends(niche, timeframe)
            trending_topics.extend(industry_trends)
            
            # Deduplicate and score topics
            unique_topics = self._deduplicate_and_score_topics(trending_topics)
            
            # Sort by trend score and limit results
            unique_topics.sort(key=lambda x: x.trend_score, reverse=True)
            top_topics = unique_topics[:limit]
            
            # Analyze patterns and generate insights
            emerging_keywords = self._identify_emerging_keywords(top_topics)
            declining_topics = self._identify_declining_topics(niche, timeframe)
            seasonal_patterns = self._analyze_seasonal_patterns(niche, top_topics)
            competitor_content = self._analyze_competitor_trending_content(niche, top_topics)
            content_opportunities = self._identify_content_opportunities(top_topics)
            recommendations = self._generate_trend_recommendations(top_topics, niche)
            
            analysis = NicheTrendAnalysis(
                niche=niche,
                analysis_date=datetime.now(),
                trending_topics=top_topics,
                emerging_keywords=emerging_keywords,
                declining_topics=declining_topics,
                seasonal_patterns=seasonal_patterns,
                competitor_trending_content=competitor_content,
                content_gap_opportunities=content_opportunities,
                recommended_actions=recommendations
            )
            
            # Cache the result
            cache.set(cache_key, analysis.__dict__, timeout=self.cache_timeout)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error identifying trending topics for '{niche}': {str(e)}")
            return NicheTrendAnalysis(
                niche=niche,
                analysis_date=datetime.now(),
                trending_topics=[],
                emerging_keywords=[],
                declining_topics=[],
                seasonal_patterns={},
                competitor_trending_content=[],
                content_gap_opportunities=[],
                recommended_actions=[]
            )
    
    def get_trending_keywords_for_topic(self, topic: str, niche: str) -> List[str]:
        """Get trending keywords related to a specific topic"""
        try:
            # Check cache first
            cache_key = f"trending_keywords_{topic}_{niche}"
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result
            
            # Get keyword suggestions from research API
            keyword_suggestions = self.keyword_research_service.get_keyword_suggestions(
                topic, APIProvider.MOCK_API, limit=50
            )
            
            # Filter and score keywords by trend potential
            trending_keywords = []
            
            for keyword_data in keyword_suggestions:
                # Calculate trend score based on search volume and recency
                trend_score = self._calculate_keyword_trend_score(keyword_data, niche)
                
                if trend_score > 50:  # Threshold for trending
                    trending_keywords.append(keyword_data.keyword)
            
            # Sort by trend potential
            trending_keywords = trending_keywords[:20]  # Top 20
            
            # Cache the result
            cache.set(cache_key, trending_keywords, timeout=self.cache_timeout)
            
            return trending_keywords
            
        except Exception as e:
            logger.error(f"Error getting trending keywords for '{topic}': {str(e)}")
            return []
    
    def _get_google_trends(self, niche: str, timeframe: TrendTimeframe) -> List[TrendingTopic]:
        """Get trending topics from Google Trends (mock implementation)"""
        try:
            # This would integrate with Google Trends API
            # For now, return mock data
            mock_trends = [
                f"{niche} 2024",
                f"new {niche} trends",
                f"{niche} innovations",
                f"future of {niche}",
                f"{niche} technology",
                f"best {niche} practices",
                f"{niche} automation",
                f"ai in {niche}",
                f"{niche} predictions",
                f"emerging {niche} tools"
            ]
            
            trending_topics = []
            
            for i, topic in enumerate(mock_trends):
                trending_topic = TrendingTopic(
                    topic=topic,
                    trend_score=90 - (i * 5),  # Decreasing scores
                    search_volume_change=50 + (i * 10),  # Varying growth
                    social_mentions=1000 - (i * 100),
                    news_mentions=50 - (i * 5),
                    related_keywords=[f"{topic} guide", f"{topic} tips", f"{topic} tutorial"],
                    sentiment_score=0.7 + (i * 0.02),
                    trend_direction='rising',
                    peak_date=datetime.now() - timedelta(days=i),
                    source=TrendSource.GOOGLE_TRENDS,
                    niche_relevance=0.9 - (i * 0.05),
                    content_opportunities=[
                        f"Create guide about {topic}",
                        f"Write tutorial on {topic}",
                        f"Develop case study for {topic}"
                    ],
                    estimated_traffic_potential=5000 - (i * 500),
                    competition_level='medium' if i < 5 else 'high',
                    urgency_score=80 - (i * 8),
                    created_at=datetime.now()
                )
                trending_topics.append(trending_topic)
            
            return trending_topics
            
        except Exception as e:
            logger.error(f"Error getting Google Trends data: {str(e)}")
            return []
    
    def _get_social_media_trends(self, niche: str, timeframe: TrendTimeframe) -> List[TrendingTopic]:
        """Get trending topics from social media (mock implementation)"""
        try:
            # This would integrate with Twitter API, Reddit API, etc.
            # For now, return mock data
            social_trends = [
                f"viral {niche} content",
                f"{niche} influencers",
                f"{niche} community",
                f"trending {niche} hashtags",
                f"{niche} discussions"
            ]
            
            trending_topics = []
            
            for i, topic in enumerate(social_trends):
                trending_topic = TrendingTopic(
                    topic=topic,
                    trend_score=75 - (i * 10),
                    search_volume_change=30 + (i * 5),
                    social_mentions=2000 - (i * 200),
                    news_mentions=20 - (i * 3),
                    related_keywords=[f"{topic} viral", f"{topic} trending"],
                    sentiment_score=0.6 + (i * 0.05),
                    trend_direction='rising',
                    peak_date=datetime.now() - timedelta(hours=i * 6),
                    source=TrendSource.SOCIAL_MEDIA,
                    niche_relevance=0.8 - (i * 0.1),
                    content_opportunities=[
                        f"Create social content about {topic}",
                        f"Engage with {topic} community"
                    ],
                    estimated_traffic_potential=3000 - (i * 300),
                    competition_level='low' if i < 3 else 'medium',
                    urgency_score=90 - (i * 15),
                    created_at=datetime.now()
                )
                trending_topics.append(trending_topic)
            
            return trending_topics
            
        except Exception as e:
            logger.error(f"Error getting social media trends: {str(e)}")
            return []
    
    def _get_news_trends(self, niche: str, timeframe: TrendTimeframe) -> List[TrendingTopic]:
        """Get trending topics from news sources (mock implementation)"""
        try:
            # This would integrate with News API
            # For now, return mock data
            news_trends = [
                f"{niche} industry news",
                f"{niche} market updates",
                f"{niche} regulations",
                f"{niche} breakthrough",
                f"{niche} investment"
            ]
            
            trending_topics = []
            
            for i, topic in enumerate(news_trends):
                trending_topic = TrendingTopic(
                    topic=topic,
                    trend_score=70 - (i * 8),
                    search_volume_change=40 + (i * 8),
                    social_mentions=500 - (i * 50),
                    news_mentions=100 - (i * 10),
                    related_keywords=[f"{topic} analysis", f"{topic} impact"],
                    sentiment_score=0.5 + (i * 0.08),
                    trend_direction='rising',
                    peak_date=datetime.now() - timedelta(days=i * 2),
                    source=TrendSource.NEWS_API,
                    niche_relevance=0.85 - (i * 0.08),
                    content_opportunities=[
                        f"Write news analysis on {topic}",
                        f"Create opinion piece about {topic}"
                    ],
                    estimated_traffic_potential=2500 - (i * 250),
                    competition_level='medium',
                    urgency_score=85 - (i * 10),
                    created_at=datetime.now()
                )
                trending_topics.append(trending_topic)
            
            return trending_topics
            
        except Exception as e:
            logger.error(f"Error getting news trends: {str(e)}")
            return []

    def _get_industry_trends(self, niche: str, timeframe: TrendTimeframe) -> List[TrendingTopic]:
        """Get trends from industry-specific sources"""
        try:
            # Get industry sources for the niche
            sources = self.niche_sources.get(niche, [])

            # Mock industry trends
            industry_trends = [
                f"{niche} best practices 2024",
                f"{niche} tools comparison",
                f"{niche} case studies",
                f"{niche} expert insights"
            ]

            trending_topics = []

            for i, topic in enumerate(industry_trends):
                trending_topic = TrendingTopic(
                    topic=topic,
                    trend_score=65 - (i * 5),
                    search_volume_change=25 + (i * 5),
                    social_mentions=300 - (i * 30),
                    news_mentions=30 - (i * 5),
                    related_keywords=[f"{topic} guide", f"{topic} examples"],
                    sentiment_score=0.7,
                    trend_direction='stable',
                    peak_date=datetime.now() - timedelta(days=i * 3),
                    source=TrendSource.INDUSTRY_BLOGS,
                    niche_relevance=0.95,
                    content_opportunities=[
                        f"Create comprehensive guide on {topic}",
                        f"Develop expert roundup for {topic}"
                    ],
                    estimated_traffic_potential=2000 - (i * 200),
                    competition_level='high',
                    urgency_score=70 - (i * 5),
                    created_at=datetime.now()
                )
                trending_topics.append(trending_topic)

            return trending_topics

        except Exception as e:
            logger.error(f"Error getting industry trends: {str(e)}")
            return []

    def _deduplicate_and_score_topics(self, trending_topics: List[TrendingTopic]) -> List[TrendingTopic]:
        """Deduplicate similar topics and recalculate scores"""
        try:
            # Group similar topics
            topic_groups = defaultdict(list)

            for topic in trending_topics:
                # Simple similarity check based on common words
                topic_key = self._get_topic_key(topic.topic)
                topic_groups[topic_key].append(topic)

            # Merge similar topics and calculate combined scores
            unique_topics = []

            for topic_key, topics in topic_groups.items():
                if len(topics) == 1:
                    unique_topics.append(topics[0])
                else:
                    # Merge topics
                    merged_topic = self._merge_topics(topics)
                    unique_topics.append(merged_topic)

            return unique_topics

        except Exception as e:
            logger.error(f"Error deduplicating topics: {str(e)}")
            return trending_topics

    def _get_topic_key(self, topic: str) -> str:
        """Generate a key for topic grouping"""
        # Remove common words and normalize
        common_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        words = [word.lower() for word in topic.split() if word.lower() not in common_words]
        return ' '.join(sorted(words))

    def _merge_topics(self, topics: List[TrendingTopic]) -> TrendingTopic:
        """Merge similar topics into one"""
        # Use the topic with highest score as base
        base_topic = max(topics, key=lambda x: x.trend_score)

        # Combine metrics
        total_search_volume_change = sum(t.search_volume_change for t in topics) / len(topics)
        total_social_mentions = sum(t.social_mentions for t in topics)
        total_news_mentions = sum(t.news_mentions for t in topics)

        # Combine related keywords
        all_keywords = []
        for topic in topics:
            all_keywords.extend(topic.related_keywords)
        unique_keywords = list(set(all_keywords))

        # Combine content opportunities
        all_opportunities = []
        for topic in topics:
            all_opportunities.extend(topic.content_opportunities)
        unique_opportunities = list(set(all_opportunities))

        # Calculate new trend score
        new_trend_score = self._calculate_combined_trend_score(topics)

        merged_topic = TrendingTopic(
            topic=base_topic.topic,
            trend_score=new_trend_score,
            search_volume_change=total_search_volume_change,
            social_mentions=total_social_mentions,
            news_mentions=total_news_mentions,
            related_keywords=unique_keywords[:10],  # Limit to top 10
            sentiment_score=sum(t.sentiment_score for t in topics) / len(topics),
            trend_direction=base_topic.trend_direction,
            peak_date=max(t.peak_date for t in topics),
            source=base_topic.source,
            niche_relevance=max(t.niche_relevance for t in topics),
            content_opportunities=unique_opportunities[:5],  # Limit to top 5
            estimated_traffic_potential=sum(t.estimated_traffic_potential for t in topics),
            competition_level=base_topic.competition_level,
            urgency_score=max(t.urgency_score for t in topics),
            created_at=datetime.now()
        )

        return merged_topic

    def _calculate_combined_trend_score(self, topics: List[TrendingTopic]) -> float:
        """Calculate combined trend score for merged topics"""
        try:
            # Weight by source reliability
            source_weights = {
                TrendSource.GOOGLE_TRENDS: 1.0,
                TrendSource.NEWS_API: 0.9,
                TrendSource.SOCIAL_MEDIA: 0.8,
                TrendSource.INDUSTRY_BLOGS: 0.85,
                TrendSource.MOCK_DATA: 0.7
            }

            weighted_scores = []
            for topic in topics:
                weight = source_weights.get(topic.source, 0.5)
                weighted_scores.append(topic.trend_score * weight)

            return sum(weighted_scores) / len(weighted_scores)

        except Exception as e:
            logger.error(f"Error calculating combined trend score: {str(e)}")
            return max(t.trend_score for t in topics)

    def _calculate_keyword_trend_score(self, keyword_data, niche: str) -> float:
        """Calculate trend score for a keyword"""
        try:
            # Base score from search volume
            volume_score = min(keyword_data.search_volume / 100, 50)

            # Difficulty factor (easier keywords score higher for trending)
            difficulty_factor = max(0, (100 - keyword_data.keyword_difficulty) / 2)

            # Niche relevance (check if keyword relates to niche)
            relevance_score = 20 if niche.lower() in keyword_data.keyword.lower() else 10

            # Recency factor (newer keywords score higher)
            recency_score = 15  # Default for mock data

            total_score = volume_score + difficulty_factor + relevance_score + recency_score
            return min(total_score, 100)

        except Exception as e:
            logger.error(f"Error calculating keyword trend score: {str(e)}")
            return 50.0
