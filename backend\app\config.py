"""
Configuration settings for LinkUp Plugin Backend
"""
import os
from datetime import timedelta


class BaseConfig:
    """Base configuration class"""
    
    # Flask Settings
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'jwt-secret-key-change-in-production')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=1)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    
    # Database Settings
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', 'sqlite:///linkup.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_timeout': 20,
        'max_overflow': 0
    }
    
    # Redis Settings
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    
    # Cache Settings
    CACHE_TYPE = 'redis'
    CACHE_REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/1')
    CACHE_DEFAULT_TIMEOUT = 300
    
    # Rate Limiting
    RATELIMIT_STORAGE_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/2')
    RATELIMIT_DEFAULT = "1000 per hour"
    
    # CORS Settings
    CORS_ORIGINS = ['http://localhost:8080', 'http://localhost:3000']
    
    # API Settings
    API_TITLE = 'LinkUp Plugin API'
    API_VERSION = 'v1'
    OPENAPI_VERSION = '3.0.2'
    
    # File Upload Settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    
    # AI/ML Settings
    SPACY_MODEL = 'en_core_web_sm'
    MAX_CONTENT_LENGTH_FOR_ANALYSIS = 50000  # 50KB max content for analysis
    SIMILARITY_THRESHOLD = 0.7
    QUALITY_SCORE_THRESHOLD = 6.0
    
    # External API Settings
    GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')
    SERPAPI_KEY = os.getenv('SERPAPI_KEY')
    AHREFS_API_KEY = os.getenv('AHREFS_API_KEY')
    
    # Monitoring & Logging
    SENTRY_DSN = os.getenv('SENTRY_DSN')
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    
    # Business Logic Settings
    FREE_TIER_BACKLINKS_PER_MONTH = 10
    PRO_TIER_BACKLINKS_PER_MONTH = 100
    AGENCY_TIER_BACKLINKS_PER_MONTH = 1000
    
    # Link Velocity Settings
    MIN_LINK_VELOCITY_DAYS = 7  # Minimum days between backlinks
    MAX_LINK_VELOCITY_DAYS = 30  # Maximum days between backlinks
    VELOCITY_RANDOMIZATION_FACTOR = 0.3  # 30% randomization
    
    # Data Retention Settings
    ANALYTICS_DATA_RETENTION_DAYS = 365
    LOG_DATA_RETENTION_DAYS = 90
    QUEUE_DATA_RETENTION_DAYS = 7


class DevelopmentConfig(BaseConfig):
    """Development configuration"""
    DEBUG = True
    TESTING = False
    
    # More verbose logging in development
    LOG_LEVEL = 'DEBUG'
    
    # Relaxed rate limiting for development
    RATELIMIT_DEFAULT = "10000 per hour"
    
    # Allow all origins in development
    CORS_ORIGINS = ['*']


class TestingConfig(BaseConfig):
    """Testing configuration"""
    TESTING = True
    DEBUG = True

    # Use in-memory database for testing
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

    # SQLite doesn't support pooling - override engine options
    SQLALCHEMY_ENGINE_OPTIONS = {
        'connect_args': {'check_same_thread': False}
    }

    # Disable rate limiting for tests
    RATELIMIT_ENABLED = False

    # Use simple cache for testing
    CACHE_TYPE = 'simple'

    # Shorter token expiration for testing
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(minutes=5)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(hours=1)

    # Disable external API calls in tests
    GOOGLE_API_KEY = 'test-key'
    SERPAPI_KEY = 'test-key'
    AHREFS_API_KEY = 'test-key'


class ProductionConfig(BaseConfig):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Strict CORS in production
    CORS_ORIGINS = [
        'https://linkup-plugin.com',
        'https://api.linkup-plugin.com',
        'https://admin.linkup-plugin.com'
    ]
    
    # Production database with connection pooling
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 3600,
        'pool_timeout': 30,
        'pool_size': 20,
        'max_overflow': 30
    }
    
    # Stricter rate limiting in production
    RATELIMIT_DEFAULT = "1000 per hour"
    
    # Production logging
    LOG_LEVEL = 'WARNING'
    
    # Security headers
    SECURITY_HEADERS = {
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Content-Security-Policy': "default-src 'self'"
    }


class StagingConfig(ProductionConfig):
    """Staging configuration - similar to production but with debug enabled"""
    DEBUG = True
    LOG_LEVEL = 'INFO'
    
    # Staging-specific CORS
    CORS_ORIGINS = [
        'https://staging.linkup-plugin.com',
        'https://staging-api.linkup-plugin.com'
    ]


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'staging': StagingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
