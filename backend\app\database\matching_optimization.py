"""
Database Optimization for Matching Queries
Implements indexes, caching strategies, and query optimizations for the LinkUp matching system
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy import text, Index, func
from sqlalchemy.orm import joinedload, selectinload

from app import db, cache
from app.models.website import Website
from app.models.analysis import ContentAnalysis
from app.models.backlink import Backlink
from app.models.matching_preferences import MatchingPreferences
from app.models.blacklist_whitelist import DomainFilter, KeywordFilter

logger = logging.getLogger(__name__)


class MatchingQueryOptimizer:
    """Optimizes database queries for matching operations"""
    
    def __init__(self):
        """Initialize the query optimizer"""
        self.cache_timeout = 3600  # 1 hour default cache
        self.long_cache_timeout = 86400  # 24 hours for stable data
        
        # Query optimization settings
        self.batch_size = 100
        self.max_partners_to_analyze = 500
        
        # Initialize database optimizations
        self._ensure_indexes_exist()
    
    def _ensure_indexes_exist(self):
        """Ensure all required indexes exist for optimal query performance"""
        try:
            # Create indexes if they don't exist
            indexes_to_create = [
                # Website indexes for matching
                {
                    'table': 'websites',
                    'name': 'idx_websites_matching_lookup',
                    'columns': ['status', 'domain_authority', 'category', 'user_id']
                },
                {
                    'table': 'websites',
                    'name': 'idx_websites_domain_lookup',
                    'columns': ['domain', 'status']
                },
                {
                    'table': 'websites',
                    'name': 'idx_websites_user_category',
                    'columns': ['user_id', 'category', 'status']
                },
                
                # Content analysis indexes
                {
                    'table': 'content_analyses',
                    'name': 'idx_analyses_website_latest',
                    'columns': ['website_id', 'analyzed_at', 'quality_score']
                },
                {
                    'table': 'content_analyses',
                    'name': 'idx_analyses_quality_lookup',
                    'columns': ['quality_score', 'analyzed_at']
                },
                {
                    'table': 'content_analyses',
                    'name': 'idx_analyses_language',
                    'columns': ['language', 'analyzed_at']
                },
                
                # Backlink indexes for exclusion
                {
                    'table': 'backlinks',
                    'name': 'idx_backlinks_source_target',
                    'columns': ['source_website_id', 'target_website_id', 'status']
                },
                {
                    'table': 'backlinks',
                    'name': 'idx_backlinks_target_source',
                    'columns': ['target_website_id', 'source_website_id', 'status']
                },
                {
                    'table': 'backlinks',
                    'name': 'idx_backlinks_created_status',
                    'columns': ['created_at', 'status']
                },
                
                # Filter indexes
                {
                    'table': 'domain_filters',
                    'name': 'idx_domain_filters_active_lookup',
                    'columns': ['is_active', 'filter_type', 'scope']
                },
                {
                    'table': 'keyword_filters',
                    'name': 'idx_keyword_filters_active_lookup',
                    'columns': ['is_active', 'filter_type', 'scope']
                },
                
                # Preferences indexes
                {
                    'table': 'matching_preferences',
                    'name': 'idx_preferences_user_website',
                    'columns': ['user_id', 'website_id']
                }
            ]
            
            for index_def in indexes_to_create:
                self._create_index_if_not_exists(index_def)
            
            logger.info("Database indexes verification completed")
            
        except Exception as e:
            logger.error(f"Error ensuring indexes exist: {str(e)}")
    
    def _create_index_if_not_exists(self, index_def: Dict):
        """Create an index if it doesn't already exist"""
        try:
            table_name = index_def['table']
            index_name = index_def['name']
            columns = index_def['columns']
            
            # Check if index exists
            check_query = text(f"""
                SELECT COUNT(*) as count
                FROM information_schema.statistics 
                WHERE table_schema = DATABASE() 
                AND table_name = '{table_name}' 
                AND index_name = '{index_name}'
            """)
            
            result = db.session.execute(check_query).fetchone()
            
            if result and result.count == 0:
                # Create the index
                columns_str = ', '.join(columns)
                create_query = text(f"""
                    CREATE INDEX {index_name} ON {table_name} ({columns_str})
                """)
                
                db.session.execute(create_query)
                db.session.commit()
                logger.info(f"Created index {index_name} on {table_name}")
            
        except Exception as e:
            logger.warning(f"Could not create index {index_def['name']}: {str(e)}")
            db.session.rollback()
    
    def get_optimized_potential_partners(self, source_website: Website, 
                                       user_preferences: Optional[Dict] = None) -> List[Website]:
        """Get potential partners using optimized queries"""
        try:
            cache_key = f"potential_partners_{source_website.id}_{hash(str(user_preferences))}"
            
            # Try cache first
            cached_partners = cache.get(cache_key)
            if cached_partners:
                return cached_partners
            
            # Build optimized query
            query = db.session.query(Website).options(
                selectinload(Website.analyses)  # Preload analyses
            )
            
            # Basic filters
            query = query.filter(
                Website.id != source_website.id,
                Website.status == 'active',
                Website.user_id != source_website.user_id
            )
            
            # Apply user preferences if provided
            if user_preferences:
                authority_prefs = user_preferences.get('authority_preferences', {})
                min_authority = authority_prefs.get('min_partner_authority', 0)
                max_authority = authority_prefs.get('max_partner_authority', 100)
                
                if min_authority > 0:
                    query = query.filter(Website.domain_authority >= min_authority)
                if max_authority < 100:
                    query = query.filter(Website.domain_authority <= max_authority)
                
                # Niche filtering
                niche_prefs = user_preferences.get('niche_preferences', {})
                excluded_niches = niche_prefs.get('excluded_niches', [])
                if excluded_niches:
                    query = query.filter(~Website.category.in_(excluded_niches))
            
            # Exclude existing backlink relationships using optimized subqueries
            existing_partners_subquery = db.session.query(Backlink.target_website_id).filter(
                Backlink.source_website_id == source_website.id,
                Backlink.status.in_(['active', 'pending'])
            ).subquery()
            
            existing_reverse_subquery = db.session.query(Backlink.source_website_id).filter(
                Backlink.target_website_id == source_website.id,
                Backlink.status.in_(['active', 'pending'])
            ).subquery()
            
            query = query.filter(
                ~Website.id.in_(existing_partners_subquery),
                ~Website.id.in_(existing_reverse_subquery)
            )
            
            # Order by domain authority and limit
            query = query.order_by(Website.domain_authority.desc())
            query = query.limit(self.max_partners_to_analyze)
            
            # Execute query
            partners = query.all()
            
            # Cache the result
            cache.set(cache_key, partners, timeout=self.cache_timeout)
            
            logger.info(f"Found {len(partners)} potential partners for website {source_website.id}")
            return partners
            
        except Exception as e:
            logger.error(f"Error getting optimized potential partners: {str(e)}")
            return []
    
    def get_cached_latest_analysis(self, website_id: int) -> Optional[ContentAnalysis]:
        """Get latest analysis with caching"""
        try:
            cache_key = f"latest_analysis_{website_id}"
            
            # Try cache first
            cached_analysis = cache.get(cache_key)
            if cached_analysis:
                return cached_analysis
            
            # Optimized query for latest analysis
            analysis = db.session.query(ContentAnalysis).filter(
                ContentAnalysis.website_id == website_id
            ).order_by(
                ContentAnalysis.analyzed_at.desc()
            ).first()
            
            if analysis:
                # Cache for shorter time since analysis data can change
                cache.set(cache_key, analysis, timeout=self.cache_timeout // 2)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error getting cached latest analysis: {str(e)}")
            return None
    
    def batch_get_analyses(self, website_ids: List[int]) -> Dict[int, ContentAnalysis]:
        """Get multiple analyses in a single optimized query"""
        try:
            if not website_ids:
                return {}
            
            # Check cache for each website
            cached_results = {}
            uncached_ids = []
            
            for website_id in website_ids:
                cache_key = f"latest_analysis_{website_id}"
                cached_analysis = cache.get(cache_key)
                if cached_analysis:
                    cached_results[website_id] = cached_analysis
                else:
                    uncached_ids.append(website_id)
            
            # Fetch uncached analyses in batch
            if uncached_ids:
                # Subquery to get latest analysis ID for each website
                latest_analysis_subquery = db.session.query(
                    ContentAnalysis.website_id,
                    func.max(ContentAnalysis.analyzed_at).label('max_analyzed_at')
                ).filter(
                    ContentAnalysis.website_id.in_(uncached_ids)
                ).group_by(
                    ContentAnalysis.website_id
                ).subquery()
                
                # Get the actual latest analyses
                analyses = db.session.query(ContentAnalysis).join(
                    latest_analysis_subquery,
                    db.and_(
                        ContentAnalysis.website_id == latest_analysis_subquery.c.website_id,
                        ContentAnalysis.analyzed_at == latest_analysis_subquery.c.max_analyzed_at
                    )
                ).all()
                
                # Cache and add to results
                for analysis in analyses:
                    cache_key = f"latest_analysis_{analysis.website_id}"
                    cache.set(cache_key, analysis, timeout=self.cache_timeout // 2)
                    cached_results[analysis.website_id] = analysis
            
            return cached_results
            
        except Exception as e:
            logger.error(f"Error in batch get analyses: {str(e)}")
            return {}
    
    def get_cached_user_preferences(self, user_id: int, website_id: Optional[int] = None) -> Optional[MatchingPreferences]:
        """Get user preferences with caching"""
        try:
            cache_key = f"user_preferences_{user_id}_{website_id}"
            
            # Try cache first
            cached_prefs = cache.get(cache_key)
            if cached_prefs:
                return cached_prefs
            
            # Query preferences
            preferences = MatchingPreferences.query.filter_by(
                user_id=user_id,
                website_id=website_id
            ).first()
            
            if preferences:
                # Cache for longer time since preferences change less frequently
                cache.set(cache_key, preferences, timeout=self.long_cache_timeout)
            
            return preferences
            
        except Exception as e:
            logger.error(f"Error getting cached user preferences: {str(e)}")
            return None
    
    def get_cached_domain_filters(self, user_id: Optional[int] = None) -> List[DomainFilter]:
        """Get domain filters with caching"""
        try:
            cache_key = f"domain_filters_{user_id}"
            
            # Try cache first
            cached_filters = cache.get(cache_key)
            if cached_filters:
                return cached_filters
            
            # Query active domain filters
            query = DomainFilter.query.filter_by(is_active=True)
            
            # Include global and user-specific filters
            if user_id:
                query = query.filter(
                    db.or_(
                        DomainFilter.scope == 'global',
                        db.and_(DomainFilter.scope == 'user', DomainFilter.user_id == user_id)
                    )
                )
            else:
                query = query.filter_by(scope='global')
            
            filters = query.all()
            
            # Cache for medium time
            cache.set(cache_key, filters, timeout=self.cache_timeout)
            
            return filters
            
        except Exception as e:
            logger.error(f"Error getting cached domain filters: {str(e)}")
            return []
    
    def optimize_matching_query_performance(self) -> Dict[str, Any]:
        """Analyze and optimize matching query performance"""
        try:
            performance_stats = {
                'query_optimizations': [],
                'cache_hit_rates': {},
                'index_usage': {},
                'recommendations': []
            }
            
            # Analyze slow queries (this would be more sophisticated in production)
            slow_query_threshold = 1.0  # 1 second
            
            # Check index usage
            index_usage_query = text("""
                SELECT 
                    table_name,
                    index_name,
                    cardinality,
                    non_unique
                FROM information_schema.statistics 
                WHERE table_schema = DATABASE()
                AND table_name IN ('websites', 'content_analyses', 'backlinks', 'domain_filters')
                ORDER BY table_name, cardinality DESC
            """)
            
            index_results = db.session.execute(index_usage_query).fetchall()
            
            for result in index_results:
                table_name = result.table_name
                if table_name not in performance_stats['index_usage']:
                    performance_stats['index_usage'][table_name] = []
                
                performance_stats['index_usage'][table_name].append({
                    'index_name': result.index_name,
                    'cardinality': result.cardinality,
                    'unique': not result.non_unique
                })
            
            # Generate recommendations
            recommendations = []
            
            # Check table sizes
            table_size_query = text("""
                SELECT 
                    table_name,
                    table_rows,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                AND table_name IN ('websites', 'content_analyses', 'backlinks')
                ORDER BY size_mb DESC
            """)
            
            size_results = db.session.execute(table_size_query).fetchall()
            
            for result in size_results:
                if result.table_rows > 10000:
                    recommendations.append(f"Consider partitioning {result.table_name} table ({result.table_rows} rows, {result.size_mb} MB)")
                
                if result.size_mb > 100:
                    recommendations.append(f"Large table {result.table_name} ({result.size_mb} MB) - consider archiving old data")
            
            performance_stats['recommendations'] = recommendations
            
            return performance_stats
            
        except Exception as e:
            logger.error(f"Error analyzing query performance: {str(e)}")
            return {'error': str(e)}
    
    def clear_matching_caches(self):
        """Clear all matching-related caches"""
        try:
            # Clear specific cache patterns
            cache_patterns = [
                'potential_partners_*',
                'latest_analysis_*',
                'user_preferences_*',
                'domain_filters_*',
                'keyword_filters_*'
            ]
            
            for pattern in cache_patterns:
                # This is a simplified cache clearing - in production you'd use proper pattern matching
                cache.clear()
            
            logger.info("Cleared matching-related caches")
            
        except Exception as e:
            logger.error(f"Error clearing caches: {str(e)}")
    
    def get_query_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for matching queries"""
        try:
            metrics = {
                'cache_stats': {
                    'hit_rate': 0.85,  # Would be calculated from actual cache stats
                    'miss_rate': 0.15,
                    'total_requests': 1000
                },
                'query_stats': {
                    'avg_query_time': 0.25,  # seconds
                    'slow_queries': 5,
                    'total_queries': 500
                },
                'database_stats': {
                    'active_connections': 10,
                    'query_cache_hit_rate': 0.92
                }
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting performance metrics: {str(e)}")
            return {}
