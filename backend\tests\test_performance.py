"""
Performance Tests for LinkUp Backend
Tests performance characteristics and identifies bottlenecks
"""
import unittest
import time
import threading
import concurrent.futures
import sys
import os
import psutil
import gc
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.competitor_analysis_service import CompetitorAnalysisService
from app.services.keyword_gap_service import KeywordGapService
from app.services.content_opportunity_service import ContentOpportunityService
from app.services.suggestion_generation_service import SuggestionGenerationService
from app.services.keyword_research_api_service import KeywordResearchAPIService, APIProvider
from app.services.content_optimization_service import ContentOptimizationService
from app.services.trending_topics_service import TrendingTopicsService, TrendTimeframe


class PerformanceTestCase(unittest.TestCase):
    """Base class for performance tests"""
    
    def setUp(self):
        """Set up performance test fixtures"""
        self.start_time = None
        self.start_memory = None
        self.process = psutil.Process()
        
        # Performance thresholds
        self.max_response_time = 5.0  # 5 seconds
        self.max_memory_increase = 50 * 1024 * 1024  # 50MB
        self.max_cpu_usage = 80.0  # 80%
    
    def start_performance_monitoring(self):
        """Start monitoring performance metrics"""
        self.start_time = time.time()
        self.start_memory = self.process.memory_info().rss
        gc.collect()  # Force garbage collection
    
    def stop_performance_monitoring(self):
        """Stop monitoring and return metrics"""
        end_time = time.time()
        end_memory = self.process.memory_info().rss
        
        return {
            'execution_time': end_time - self.start_time,
            'memory_increase': end_memory - self.start_memory,
            'cpu_percent': self.process.cpu_percent()
        }
    
    def assert_performance_acceptable(self, metrics, operation_name):
        """Assert that performance metrics are within acceptable limits"""
        self.assertLess(
            metrics['execution_time'], 
            self.max_response_time,
            f"{operation_name} took {metrics['execution_time']:.2f}s (max: {self.max_response_time}s)"
        )
        
        self.assertLess(
            metrics['memory_increase'],
            self.max_memory_increase,
            f"{operation_name} used {metrics['memory_increase'] / 1024 / 1024:.2f}MB (max: {self.max_memory_increase / 1024 / 1024:.2f}MB)"
        )


class TestServicePerformance(PerformanceTestCase):
    """Test individual service performance"""
    
    def setUp(self):
        super().setUp()
        self.mock_website = Mock()
        self.mock_website.id = 1
        self.mock_website.domain = 'example.com'
        self.mock_website.category = 'technology'
    
    def test_competitor_analysis_performance(self):
        """Test competitor analysis service performance"""
        service = CompetitorAnalysisService()
        
        self.start_performance_monitoring()
        
        with patch('app.models.website.Website.query') as mock_query:
            mock_query.get.return_value = self.mock_website
            
            with patch.object(service, '_discover_competitors', return_value=['competitor1.com']):
                with patch.object(service, '_analyze_competitor_content', return_value={}):
                    result = service.analyze_competitors(1)
        
        metrics = self.stop_performance_monitoring()
        self.assert_performance_acceptable(metrics, "Competitor Analysis")
        
        # Verify result structure
        self.assertIn('analysis_summary', result)
    
    def test_keyword_gap_analysis_performance(self):
        """Test keyword gap analysis performance"""
        service = KeywordGapService()
        
        self.start_performance_monitoring()
        
        with patch.object(service, '_get_competitor_analysis', return_value={'competitor_details': {}}):
            with patch.object(service, '_get_source_keywords', return_value=set()):
                result = service.analyze_keyword_gaps(1, ['competitor1.com'])
        
        metrics = self.stop_performance_monitoring()
        self.assert_performance_acceptable(metrics, "Keyword Gap Analysis")
        
        # Verify result structure
        self.assertIn('keyword_gaps', result)
    
    def test_content_opportunity_scoring_performance(self):
        """Test content opportunity scoring performance"""
        service = ContentOpportunityService()
        
        self.start_performance_monitoring()
        
        with patch.object(service.keyword_gap_service, 'analyze_keyword_gaps') as mock_gaps:
            mock_gaps.return_value = {
                'keyword_gaps': [
                    {
                        'keyword': f'test keyword {i}',
                        'opportunity_score': 50 + i,
                        'difficulty_score': 30 + i,
                        'search_volume_estimate': 1000 + i * 100
                    }
                    for i in range(100)  # Test with 100 keywords
                ],
                'content_gaps': [],
                'long_tail_opportunities': []
            }
            
            result = service.score_content_opportunities(1)
        
        metrics = self.stop_performance_monitoring()
        self.assert_performance_acceptable(metrics, "Content Opportunity Scoring")
        
        # Verify result structure
        self.assertIn('opportunities', result)
    
    def test_suggestion_generation_performance(self):
        """Test suggestion generation performance"""
        service = SuggestionGenerationService()
        
        self.start_performance_monitoring()
        
        with patch.object(service.opportunity_service, 'score_content_opportunities') as mock_opps:
            mock_opps.return_value = {
                'opportunities': [
                    {
                        'opportunity_id': f'opp_{i}',
                        'opportunity_type': 'keyword_targeting',
                        'title': f'Test opportunity {i}',
                        'target_keywords': [f'keyword{i}'],
                        'opportunity_score': 70 + i,
                        'difficulty_score': 40,
                        'estimated_traffic': 1000
                    }
                    for i in range(50)  # Test with 50 opportunities
                ]
            }
            
            result = service.generate_content_suggestions(1)
        
        metrics = self.stop_performance_monitoring()
        self.assert_performance_acceptable(metrics, "Suggestion Generation")
        
        # Verify result structure
        self.assertIn('suggestions', result)
    
    def test_keyword_research_api_performance(self):
        """Test keyword research API performance"""
        service = KeywordResearchAPIService()
        
        self.start_performance_monitoring()
        
        # Test multiple API calls
        keywords = [f'test keyword {i}' for i in range(10)]
        
        for keyword in keywords:
            result = service.get_keyword_data(keyword, APIProvider.MOCK_API)
            self.assertIsNotNone(result)
        
        metrics = self.stop_performance_monitoring()
        self.assert_performance_acceptable(metrics, "Keyword Research API (10 calls)")
    
    def test_content_optimization_performance(self):
        """Test content optimization performance"""
        service = ContentOptimizationService()
        
        # Generate large content for testing
        large_content = """
# Test Content

This is a large piece of content for performance testing. """ + "This is repeated content. " * 1000 + """

## Section 1
More content here with keywords and analysis.

## Section 2  
Additional content for comprehensive testing.
"""
        
        self.start_performance_monitoring()
        
        result = service.analyze_content(
            'https://example.com/test',
            large_content,
            ['test', 'content', 'performance']
        )
        
        metrics = self.stop_performance_monitoring()
        self.assert_performance_acceptable(metrics, "Content Optimization (Large Content)")
        
        # Verify result structure
        self.assertGreater(result.overall_score, 0)
    
    def test_trending_topics_performance(self):
        """Test trending topics identification performance"""
        service = TrendingTopicsService()
        
        self.start_performance_monitoring()
        
        result = service.identify_trending_topics('technology', TrendTimeframe.LAST_7D)
        
        metrics = self.stop_performance_monitoring()
        self.assert_performance_acceptable(metrics, "Trending Topics Identification")
        
        # Verify result structure
        self.assertEqual(result.niche, 'technology')
        self.assertIsInstance(result.trending_topics, list)


class TestConcurrentPerformance(PerformanceTestCase):
    """Test performance under concurrent load"""
    
    def setUp(self):
        super().setUp()
        self.mock_website = Mock()
        self.mock_website.id = 1
        self.mock_website.domain = 'example.com'
    
    def test_concurrent_competitor_analysis(self):
        """Test concurrent competitor analysis requests"""
        service = CompetitorAnalysisService()
        
        def analyze_competitor():
            with patch('app.models.website.Website.query') as mock_query:
                mock_query.get.return_value = self.mock_website
                with patch.object(service, '_discover_competitors', return_value=['competitor1.com']):
                    with patch.object(service, '_analyze_competitor_content', return_value={}):
                        return service.analyze_competitors(1)
        
        self.start_performance_monitoring()
        
        # Run 5 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(analyze_competitor) for _ in range(5)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        metrics = self.stop_performance_monitoring()
        
        # Should handle concurrent requests within reasonable time
        self.assertLess(metrics['execution_time'], 10.0, "Concurrent requests should complete within 10 seconds")
        
        # All requests should succeed
        self.assertEqual(len(results), 5)
        for result in results:
            self.assertIn('analysis_summary', result)
    
    def test_concurrent_keyword_gap_analysis(self):
        """Test concurrent keyword gap analysis"""
        service = KeywordGapService()
        
        def analyze_gaps():
            with patch.object(service, '_get_competitor_analysis', return_value={'competitor_details': {}}):
                with patch.object(service, '_get_source_keywords', return_value=set()):
                    return service.analyze_keyword_gaps(1, ['competitor1.com'])
        
        self.start_performance_monitoring()
        
        # Run 3 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(analyze_gaps) for _ in range(3)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        metrics = self.stop_performance_monitoring()
        
        # Should handle concurrent requests efficiently
        self.assertLess(metrics['execution_time'], 8.0, "Concurrent gap analysis should complete within 8 seconds")
        
        # All requests should succeed
        self.assertEqual(len(results), 3)
        for result in results:
            self.assertIn('keyword_gaps', result)


class TestMemoryPerformance(PerformanceTestCase):
    """Test memory usage and potential memory leaks"""
    
    def test_memory_usage_competitor_analysis(self):
        """Test memory usage during competitor analysis"""
        service = CompetitorAnalysisService()
        
        initial_memory = self.process.memory_info().rss
        
        # Run multiple analyses to check for memory leaks
        for i in range(10):
            with patch('app.models.website.Website.query') as mock_query:
                mock_query.get.return_value = Mock(id=1, domain='example.com')
                with patch.object(service, '_discover_competitors', return_value=['competitor1.com']):
                    with patch.object(service, '_analyze_competitor_content', return_value={}):
                        result = service.analyze_competitors(1)
            
            # Force garbage collection
            gc.collect()
        
        final_memory = self.process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 20MB for 10 runs)
        self.assertLess(
            memory_increase, 
            20 * 1024 * 1024,
            f"Memory increased by {memory_increase / 1024 / 1024:.2f}MB after 10 runs"
        )
    
    def test_memory_usage_large_dataset(self):
        """Test memory usage with large datasets"""
        service = ContentOpportunityService()
        
        # Create large mock dataset
        large_keyword_gaps = [
            {
                'keyword': f'keyword {i}',
                'opportunity_score': 50 + (i % 50),
                'difficulty_score': 30 + (i % 30),
                'search_volume_estimate': 1000 + i
            }
            for i in range(1000)  # 1000 keywords
        ]
        
        self.start_performance_monitoring()
        
        with patch.object(service.keyword_gap_service, 'analyze_keyword_gaps') as mock_gaps:
            mock_gaps.return_value = {
                'keyword_gaps': large_keyword_gaps,
                'content_gaps': [],
                'long_tail_opportunities': []
            }
            
            result = service.score_content_opportunities(1)
        
        metrics = self.stop_performance_monitoring()
        
        # Should handle large datasets efficiently
        self.assertLess(metrics['execution_time'], 10.0, "Large dataset processing should complete within 10 seconds")
        self.assertLess(metrics['memory_increase'], 100 * 1024 * 1024, "Memory usage should be under 100MB")


class TestCachePerformance(PerformanceTestCase):
    """Test caching performance and effectiveness"""
    
    def test_cache_hit_performance(self):
        """Test performance with cache hits"""
        service = CompetitorAnalysisService()
        
        # Mock cache hit
        cached_result = {'analysis_summary': {'cached': True}}
        
        with patch('app.services.competitor_analysis_service.cache') as mock_cache:
            mock_cache.get.return_value = cached_result
            
            self.start_performance_monitoring()
            
            # Multiple calls should be fast due to caching
            for _ in range(10):
                result = service.analyze_competitors(1)
                self.assertEqual(result, cached_result)
            
            metrics = self.stop_performance_monitoring()
        
        # Cache hits should be very fast
        self.assertLess(metrics['execution_time'], 0.1, "Cache hits should be under 100ms for 10 calls")
    
    def test_cache_miss_vs_hit_performance(self):
        """Compare cache miss vs hit performance"""
        service = KeywordGapService()
        
        # Test cache miss
        with patch('app.services.keyword_gap_service.cache') as mock_cache:
            mock_cache.get.return_value = None  # Cache miss
            
            with patch.object(service, '_get_competitor_analysis', return_value={'competitor_details': {}}):
                with patch.object(service, '_get_source_keywords', return_value=set()):
                    
                    start_time = time.time()
                    service.analyze_keyword_gaps(1)
                    cache_miss_time = time.time() - start_time
        
        # Test cache hit
        cached_result = {'keyword_gaps': [], 'analysis_summary': {}}
        
        with patch('app.services.keyword_gap_service.cache') as mock_cache:
            mock_cache.get.return_value = cached_result
            
            start_time = time.time()
            service.analyze_keyword_gaps(1)
            cache_hit_time = time.time() - start_time
        
        # Cache hit should be significantly faster
        self.assertLess(cache_hit_time, cache_miss_time / 10, "Cache hit should be at least 10x faster than cache miss")


class TestDatabasePerformance(PerformanceTestCase):
    """Test database operation performance"""
    
    def test_bulk_database_operations(self):
        """Test performance of bulk database operations"""
        # This would test actual database operations in a real environment
        # For now, we'll simulate with mock operations
        
        self.start_performance_monitoring()
        
        # Simulate bulk insert operations
        for i in range(100):
            # Mock database operation
            time.sleep(0.001)  # Simulate 1ms database operation
        
        metrics = self.stop_performance_monitoring()
        
        # Bulk operations should complete efficiently
        self.assertLess(metrics['execution_time'], 1.0, "Bulk operations should complete within 1 second")


class PerformanceTestRunner:
    """Performance test runner with detailed reporting"""
    
    def __init__(self):
        self.results = {}
    
    def run_performance_tests(self):
        """Run all performance tests and generate report"""
        print("🚀 LinkUp Performance Test Suite")
        print("=" * 50)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Test suites to run
        test_suites = [
            ('Service Performance', TestServicePerformance),
            ('Concurrent Performance', TestConcurrentPerformance),
            ('Memory Performance', TestMemoryPerformance),
            ('Cache Performance', TestCachePerformance),
            ('Database Performance', TestDatabasePerformance)
        ]
        
        overall_start_time = time.time()
        
        for suite_name, test_class in test_suites:
            print(f"⚡ Running {suite_name} Tests...")
            print("-" * 40)
            
            suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
            runner = unittest.TextTestRunner(verbosity=1, stream=open(os.devnull, 'w'))
            
            start_time = time.time()
            result = runner.run(suite)
            end_time = time.time()
            
            self.results[suite_name] = {
                'tests_run': result.testsRun,
                'failures': len(result.failures),
                'errors': len(result.errors),
                'execution_time': end_time - start_time,
                'success_rate': ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0
            }
            
            # Print suite results
            status = "✅" if result.failures == 0 and result.errors == 0 else "❌"
            print(f"{status} {suite_name}: {self.results[suite_name]['success_rate']:.1f}% success rate")
            print(f"   Tests: {result.testsRun}, Time: {end_time - start_time:.2f}s")
            print()
        
        overall_end_time = time.time()
        
        # Generate performance report
        self.generate_performance_report(overall_end_time - overall_start_time)
        
        return self.results
    
    def generate_performance_report(self, total_time):
        """Generate comprehensive performance report"""
        print("📊 PERFORMANCE TEST REPORT")
        print("=" * 50)
        
        total_tests = sum(r['tests_run'] for r in self.results.values())
        total_failures = sum(r['failures'] for r in self.results.values())
        total_errors = sum(r['errors'] for r in self.results.values())
        overall_success_rate = ((total_tests - total_failures - total_errors) / total_tests * 100) if total_tests > 0 else 0
        
        print(f"Total Tests: {total_tests}")
        print(f"Total Failures: {total_failures}")
        print(f"Total Errors: {total_errors}")
        print(f"Overall Success Rate: {overall_success_rate:.1f}%")
        print(f"Total Execution Time: {total_time:.2f} seconds")
        print()
        
        # Performance summary
        print("Performance Summary:")
        print("-" * 20)
        
        for suite_name, results in self.results.items():
            status = "🟢" if results['success_rate'] >= 90 else "🟡" if results['success_rate'] >= 70 else "🔴"
            print(f"{status} {suite_name}: {results['success_rate']:.1f}% ({results['execution_time']:.2f}s)")
        
        print()
        
        # Performance recommendations
        print("Performance Recommendations:")
        print("-" * 30)
        
        if overall_success_rate >= 95:
            print("🎉 Excellent performance! System is optimized and ready for production.")
        elif overall_success_rate >= 85:
            print("✅ Good performance with minor optimization opportunities.")
        elif overall_success_rate >= 70:
            print("⚠️ Fair performance. Consider optimization for better user experience.")
        else:
            print("❌ Poor performance. Significant optimization required before production.")
        
        print()
        print("=" * 50)
        print(f"Performance test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == '__main__':
    # Run performance tests
    runner = PerformanceTestRunner()
    results = runner.run_performance_tests()
    
    # Exit with appropriate code
    overall_success = all(r['success_rate'] >= 80 for r in results.values())
    exit(0 if overall_success else 1)
