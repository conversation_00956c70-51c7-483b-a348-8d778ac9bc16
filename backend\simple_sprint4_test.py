#!/usr/bin/env python3
"""
Simple Sprint 4-6 Test Runner
Tests the core functionality without complex dependencies
"""
import sys
import os
import unittest
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all core modules can be imported"""
    print("🔍 Testing Core Module Imports...")
    
    try:
        # Test basic imports
        import numpy as np
        print("✅ NumPy imported successfully")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    try:
        from sklearn.feature_extraction.text import TfidfVectorizer
        from sklearn.metrics.pairwise import cosine_similarity
        print("✅ Scikit-learn imported successfully")
    except ImportError as e:
        print(f"❌ Scikit-learn import failed: {e}")
        return False
    
    try:
        from sqlalchemy import and_, or_, func
        print("✅ SQLAlchemy imported successfully")
    except ImportError as e:
        print(f"❌ SQLAlchemy import failed: {e}")
        return False
    
    return True

def test_service_imports():
    """Test that all service modules can be imported"""
    print("\n🔍 Testing Service Module Imports...")
    
    services = [
        'app.services.matching_service',
        'app.services.advanced_content_matching',
        'app.services.niche_matching_service',
        'app.services.quality_assessment_service',
        'app.services.filter_management_service',
        'app.services.preferences_service',
        'app.services.match_transparency_service'
    ]
    
    success_count = 0
    for service in services:
        try:
            __import__(service)
            print(f"✅ {service}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {service} - {e}")
    
    return success_count == len(services)

def test_model_imports():
    """Test that all model modules can be imported"""
    print("\n🔍 Testing Model Imports...")
    
    models = [
        'app.models.website',
        'app.models.user',
        'app.models.analysis',
        'app.models.backlink',
        'app.models.matching_preferences',
        'app.models.blacklist_whitelist'
    ]
    
    success_count = 0
    for model in models:
        try:
            __import__(model)
            print(f"✅ {model}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {model} - {e}")
    
    return success_count == len(models)

def test_basic_functionality():
    """Test basic functionality of core services"""
    print("\n🔍 Testing Basic Service Functionality...")
    
    try:
        # Test AdvancedContentMatcher
        from app.services.advanced_content_matching import AdvancedContentMatcher
        matcher = AdvancedContentMatcher()
        print("✅ AdvancedContentMatcher instantiated")
        
        # Test NicheMatchingService
        from app.services.niche_matching_service import NicheMatchingService
        niche_service = NicheMatchingService()
        print("✅ NicheMatchingService instantiated")
        
        # Test basic niche compatibility
        compatibility = niche_service.calculate_niche_compatibility('technology', 'business')
        if isinstance(compatibility, dict) and 'total_score' in compatibility:
            print(f"✅ Niche compatibility calculation works: {compatibility['total_score']:.2f}")
        else:
            print("❌ Niche compatibility calculation failed")
            return False
        
        # Test QualityAssessmentService
        from app.services.quality_assessment_service import QualityAssessmentService
        quality_service = QualityAssessmentService()
        print("✅ QualityAssessmentService instantiated")
        
        # Test FilterManagementService
        from app.services.filter_management_service import FilterManagementService
        filter_service = FilterManagementService()
        print("✅ FilterManagementService instantiated")
        
        # Test PreferencesService
        from app.services.preferences_service import PreferencesService
        prefs_service = PreferencesService()
        print("✅ PreferencesService instantiated")
        
        # Test MatchTransparencyService
        from app.services.match_transparency_service import MatchTransparencyService
        transparency_service = MatchTransparencyService()
        print("✅ MatchTransparencyService instantiated")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def test_algorithm_components():
    """Test individual algorithm components"""
    print("\n🔍 Testing Algorithm Components...")
    
    try:
        from app.services.niche_matching_service import NicheMatchingService
        niche_service = NicheMatchingService()
        
        # Test category hierarchy
        hierarchy = niche_service.get_category_hierarchy('technology')
        if isinstance(hierarchy, dict):
            print(f"✅ Category hierarchy works: {hierarchy}")
        else:
            print("❌ Category hierarchy failed")
            return False
        
        # Test cross-niche opportunities
        opportunities = niche_service.get_cross_niche_opportunities(['technology', 'business'])
        if isinstance(opportunities, dict):
            print("✅ Cross-niche opportunities calculation works")
        else:
            print("❌ Cross-niche opportunities failed")
            return False
        
        # Test preference templates
        from app.services.preferences_service import PreferencesService
        prefs_service = PreferencesService()
        
        templates = prefs_service.get_preference_templates()
        if isinstance(templates, dict) and len(templates) > 0:
            print(f"✅ Preference templates work: {list(templates.keys())}")
        else:
            print("❌ Preference templates failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Algorithm components test failed: {e}")
        return False

def test_data_structures():
    """Test data structure creation and manipulation"""
    print("\n🔍 Testing Data Structures...")
    
    try:
        # Test ScoreExplanation dataclass
        from app.services.match_transparency_service import ScoreExplanation
        
        explanation = ScoreExplanation(
            component="test_component",
            score=0.8,
            weight=0.2,
            contribution=0.16,
            explanation="Test explanation",
            factors=[{"factor": "test", "value": "test_value", "impact": "high"}],
            improvement_suggestions=["Test suggestion"]
        )
        
        if explanation.component == "test_component" and explanation.score == 0.8:
            print("✅ ScoreExplanation dataclass works")
        else:
            print("❌ ScoreExplanation dataclass failed")
            return False
        
        # Test MatchExplanation dataclass
        from app.services.match_transparency_service import MatchExplanation
        
        match_explanation = MatchExplanation(
            source_website_id=1,
            target_website_id=2,
            overall_score=0.75,
            component_explanations=[explanation],
            decision_factors=["Test factor"],
            improvement_suggestions=["Test improvement"],
            confidence_level="high",
            explanation_timestamp=datetime.utcnow()
        )
        
        if match_explanation.overall_score == 0.75:
            print("✅ MatchExplanation dataclass works")
        else:
            print("❌ MatchExplanation dataclass failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Data structures test failed: {e}")
        return False

def run_sprint4_tests():
    """Run all Sprint 4-6 tests"""
    print("=" * 80)
    print("🚀 SPRINT 4-6 FUNCTIONALITY TESTS")
    print("=" * 80)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Core Module Imports", test_imports),
        ("Service Module Imports", test_service_imports),
        ("Model Imports", test_model_imports),
        ("Basic Functionality", test_basic_functionality),
        ("Algorithm Components", test_algorithm_components),
        ("Data Structures", test_data_structures)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 50)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 80)
    print("📊 SPRINT 4-6 TEST RESULTS")
    print("=" * 80)
    print(f"Total Tests: {total}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {total - passed}")
    print(f"🎯 Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL SPRINT 4-6 TESTS PASSED!")
        print("🚀 LinkUp Matching System is ready for production!")
        return True
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please review and fix issues.")
        return False

if __name__ == '__main__':
    success = run_sprint4_tests()
    sys.exit(0 if success else 1)
