"""
Backlink model for LinkUp Plugin
"""
from app import db
from datetime import datetime
import uuid


class Backlink(db.Model):
    """Backlink model for storing backlink exchange information"""
    
    __tablename__ = 'backlinks'
    
    # Primary key
    id = db.Column(db.Integer, primary_key=True)
    
    # Foreign keys
    source_website_id = db.Column(db.In<PERSON>ger, db.ForeignKey('websites.id'), nullable=False)
    target_website_id = db.Column(db.Integer, db.ForeignKey('websites.id'), nullable=False)
    
    # Backlink details
    source_url = db.Column(db.String(500), nullable=False)  # URL where the link is placed
    target_url = db.Column(db.String(500), nullable=False)  # URL being linked to
    anchor_text = db.Column(db.String(200), nullable=False)
    
    # Context information
    context_before = db.Column(db.Text)  # Text before the link
    context_after = db.Column(db.Text)   # Text after the link
    
    # Status and workflow
    status = db.Column(db.String(50), default='pending')  # pending, approved, active, removed, rejected
    
    # Quality metrics
    relevance_score = db.Column(db.Float, default=0.0)  # 0-10 relevance score
    quality_score = db.Column(db.Float, default=0.0)    # 0-10 quality score
    
    # SEO metrics
    link_juice_value = db.Column(db.Float, default=0.0)  # Estimated SEO value
    position_on_page = db.Column(db.Integer)             # Position of link on page
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    approved_at = db.Column(db.DateTime)
    activated_at = db.Column(db.DateTime)
    removed_at = db.Column(db.DateTime)
    
    # Relationships
    source_website = db.relationship('Website', foreign_keys=[source_website_id], backref='outbound_backlinks')
    target_website = db.relationship('Website', foreign_keys=[target_website_id], backref='inbound_backlinks')
    
    def __repr__(self):
        return f'<Backlink {self.id}: {self.source_website_id} -> {self.target_website_id}>'
    
    def to_dict(self):
        """Convert backlink to dictionary"""
        return {
            'id': self.id,
            'source_website_id': self.source_website_id,
            'target_website_id': self.target_website_id,
            'source_url': self.source_url,
            'target_url': self.target_url,
            'anchor_text': self.anchor_text,
            'context_before': self.context_before,
            'context_after': self.context_after,
            'status': self.status,
            'relevance_score': self.relevance_score,
            'quality_score': self.quality_score,
            'link_juice_value': self.link_juice_value,
            'position_on_page': self.position_on_page,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'activated_at': self.activated_at.isoformat() if self.activated_at else None,
            'removed_at': self.removed_at.isoformat() if self.removed_at else None
        }
    
    def approve(self):
        """Approve the backlink"""
        self.status = 'approved'
        self.approved_at = datetime.utcnow()
        db.session.commit()
    
    def activate(self):
        """Activate the backlink (mark as live)"""
        self.status = 'active'
        self.activated_at = datetime.utcnow()
        db.session.commit()
    
    def remove(self):
        """Remove the backlink"""
        self.status = 'removed'
        self.removed_at = datetime.utcnow()
        db.session.commit()
    
    def reject(self):
        """Reject the backlink"""
        self.status = 'rejected'
        db.session.commit()
    
    @classmethod
    def get_active_backlinks(cls):
        """Get all active backlinks"""
        return cls.query.filter_by(status='active').all()
    
    @classmethod
    def get_pending_backlinks(cls):
        """Get all pending backlinks"""
        return cls.query.filter_by(status='pending').all()
    
    @classmethod
    def get_website_backlinks(cls, website_id, direction='inbound'):
        """Get backlinks for a specific website"""
        if direction == 'inbound':
            return cls.query.filter_by(target_website_id=website_id).all()
        else:
            return cls.query.filter_by(source_website_id=website_id).all()
    
    @classmethod
    def get_mutual_backlinks(cls, website1_id, website2_id):
        """Get mutual backlinks between two websites"""
        return cls.query.filter(
            db.or_(
                db.and_(cls.source_website_id == website1_id, cls.target_website_id == website2_id),
                db.and_(cls.source_website_id == website2_id, cls.target_website_id == website1_id)
            )
        ).all()
    
    def calculate_relevance_score(self):
        """Calculate relevance score based on content similarity"""
        # This would use the content analysis data to calculate relevance
        # For now, return a placeholder
        return 7.5
    
    def calculate_quality_score(self):
        """Calculate quality score based on various factors"""
        # This would consider factors like:
        # - Source website authority
        # - Content quality
        # - Link placement
        # - Anchor text relevance
        return 8.0
    
    def is_reciprocal(self):
        """Check if this is part of a reciprocal link exchange"""
        reciprocal = Backlink.query.filter_by(
            source_website_id=self.target_website_id,
            target_website_id=self.source_website_id
        ).first()
        
        return reciprocal is not None
