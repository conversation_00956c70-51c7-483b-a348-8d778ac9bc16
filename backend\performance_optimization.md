# LinkUp Performance Optimization Guide

## Overview

This guide provides comprehensive performance optimization strategies for the LinkUp backend system, covering database optimization, caching strategies, API performance, and system monitoring.

## Performance Benchmarks

### Target Performance Metrics

| Metric | Target | Critical Threshold |
|--------|--------|--------------------|
| API Response Time | < 2s | < 5s |
| Database Query Time | < 500ms | < 1s |
| Memory Usage | < 512MB | < 1GB |
| CPU Usage | < 50% | < 80% |
| Cache Hit Rate | > 80% | > 60% |
| Throughput | > 100 req/s | > 50 req/s |

### Current Performance Status

Run the performance test suite to get current metrics:

```bash
# Run performance tests
python backend/tests/test_performance.py

# Run load tests
python backend/load_test.py --medium

# Monitor system resources
python backend/monitor_performance.py
```

## Database Optimization

### 1. Index Optimization

**Current Indexes:**
```sql
-- Websites table
CREATE INDEX idx_websites_domain ON linkup_websites(domain);
CREATE INDEX idx_websites_category ON linkup_websites(category);
CREATE INDEX idx_websites_status ON linkup_websites(status);

-- Analyses table
CREATE INDEX idx_analyses_website_id ON linkup_analyses(website_id);
CREATE INDEX idx_analyses_created_at ON linkup_analyses(created_at);
CREATE INDEX idx_analyses_status ON linkup_analyses(status);

-- Matches table
CREATE INDEX idx_matches_source_website ON linkup_matches(source_website_id);
CREATE INDEX idx_matches_target_website ON linkup_matches(target_website_id);
CREATE INDEX idx_matches_score ON linkup_matches(compatibility_score);
CREATE INDEX idx_matches_created_at ON linkup_matches(created_at);
```

**Optimization Recommendations:**
- Add composite indexes for common query patterns
- Monitor slow query log for missing indexes
- Use EXPLAIN ANALYZE to optimize query plans

### 2. Query Optimization

**Slow Query Patterns to Avoid:**
```python
# BAD: N+1 queries
for website in websites:
    analyses = Analysis.query.filter_by(website_id=website.id).all()

# GOOD: Eager loading
websites = Website.query.options(joinedload(Website.analyses)).all()
```

**Optimized Query Examples:**
```python
# Use pagination for large datasets
def get_paginated_matches(page=1, per_page=50):
    return Match.query.paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )

# Use database-level filtering
def get_high_quality_matches(min_score=70):
    return Match.query.filter(
        Match.compatibility_score >= min_score
    ).order_by(Match.compatibility_score.desc())
```

### 3. Connection Pooling

**SQLAlchemy Configuration:**
```python
# app/config.py
class Config:
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 20,
        'pool_recycle': 3600,
        'pool_pre_ping': True,
        'max_overflow': 30
    }
```

## Caching Strategies

### 1. Redis Configuration

**Optimal Redis Settings:**
```python
# app/config.py
CACHE_CONFIG = {
    'CACHE_TYPE': 'redis',
    'CACHE_REDIS_URL': 'redis://localhost:6379/0',
    'CACHE_DEFAULT_TIMEOUT': 3600,
    'CACHE_KEY_PREFIX': 'linkup:',
    'CACHE_OPTIONS': {
        'connection_pool_kwargs': {
            'max_connections': 50,
            'retry_on_timeout': True
        }
    }
}
```

### 2. Caching Patterns

**Service-Level Caching:**
```python
from app import cache

class CompetitorAnalysisService:
    @cache.memoize(timeout=3600)  # 1 hour
    def analyze_competitors(self, website_id):
        # Expensive computation
        return analysis_result
    
    def invalidate_cache(self, website_id):
        cache.delete_memoized(self.analyze_competitors, website_id)
```

**API Response Caching:**
```python
from flask import request
from app import cache

@app.route('/api/content-suggestions')
@cache.cached(timeout=1800, query_string=True)  # 30 minutes
def get_content_suggestions():
    # API logic
    return jsonify(suggestions)
```

### 3. Cache Warming

**Background Cache Warming:**
```python
from celery import Celery

@celery.task
def warm_cache_for_website(website_id):
    """Pre-populate cache for frequently accessed data"""
    service = CompetitorAnalysisService()
    service.analyze_competitors(website_id)
    
    opportunity_service = ContentOpportunityService()
    opportunity_service.score_content_opportunities(website_id)
```

## API Performance Optimization

### 1. Response Compression

**Enable Gzip Compression:**
```python
from flask_compress import Compress

app = Flask(__name__)
Compress(app)

# Configure compression
app.config['COMPRESS_MIMETYPES'] = [
    'text/html', 'text/css', 'text/xml',
    'application/json', 'application/javascript'
]
```

### 2. Async Processing

**Background Task Processing:**
```python
from celery import Celery

# Long-running tasks
@celery.task
def analyze_competitor_content(website_id, competitor_domains):
    service = CompetitorAnalysisService()
    return service.analyze_competitors(website_id, competitor_domains)

# API endpoint
@app.route('/api/competitor-analysis', methods=['POST'])
def start_competitor_analysis():
    task = analyze_competitor_content.delay(website_id, competitors)
    return jsonify({'task_id': task.id, 'status': 'started'})
```

### 3. Request Optimization

**Batch API Endpoints:**
```python
@app.route('/api/batch/content-suggestions', methods=['POST'])
def get_batch_content_suggestions():
    website_ids = request.json.get('website_ids', [])
    
    # Process in parallel
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = {
            executor.submit(get_suggestions, wid): wid 
            for wid in website_ids
        }
        
        results = {}
        for future in as_completed(futures):
            website_id = futures[future]
            results[website_id] = future.result()
    
    return jsonify(results)
```

## Memory Optimization

### 1. Object Lifecycle Management

**Proper Resource Cleanup:**
```python
class CompetitorAnalysisService:
    def analyze_competitors(self, website_id):
        try:
            # Large data processing
            large_dataset = self._fetch_competitor_data()
            result = self._process_data(large_dataset)
            return result
        finally:
            # Explicit cleanup
            del large_dataset
            gc.collect()
```

### 2. Streaming for Large Datasets

**Stream Large Responses:**
```python
from flask import Response
import json

@app.route('/api/large-dataset')
def stream_large_dataset():
    def generate():
        yield '{"data": ['
        
        first = True
        for item in get_large_dataset():
            if not first:
                yield ','
            yield json.dumps(item)
            first = False
        
        yield ']}'
    
    return Response(generate(), mimetype='application/json')
```

### 3. Memory Profiling

**Monitor Memory Usage:**
```python
import psutil
import tracemalloc

def profile_memory(func):
    def wrapper(*args, **kwargs):
        tracemalloc.start()
        
        # Get initial memory
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            # Get final memory and trace
            final_memory = process.memory_info().rss
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            print(f"Memory increase: {(final_memory - initial_memory) / 1024 / 1024:.2f} MB")
            print(f"Peak traced memory: {peak / 1024 / 1024:.2f} MB")
    
    return wrapper
```

## Monitoring and Alerting

### 1. Performance Metrics Collection

**Custom Metrics:**
```python
from prometheus_client import Counter, Histogram, Gauge
import time

# Define metrics
REQUEST_COUNT = Counter('linkup_requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('linkup_request_duration_seconds', 'Request duration')
ACTIVE_USERS = Gauge('linkup_active_users', 'Number of active users')

# Middleware for metrics collection
@app.before_request
def before_request():
    request.start_time = time.time()

@app.after_request
def after_request(response):
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.endpoint or 'unknown'
    ).inc()
    
    REQUEST_DURATION.observe(time.time() - request.start_time)
    return response
```

### 2. Health Check Endpoints

**Comprehensive Health Checks:**
```python
@app.route('/health')
def health_check():
    checks = {
        'database': check_database_connection(),
        'redis': check_redis_connection(),
        'external_apis': check_external_apis(),
        'disk_space': check_disk_space(),
        'memory_usage': check_memory_usage()
    }
    
    all_healthy = all(checks.values())
    status_code = 200 if all_healthy else 503
    
    return jsonify({
        'status': 'healthy' if all_healthy else 'unhealthy',
        'checks': checks,
        'timestamp': datetime.utcnow().isoformat()
    }), status_code
```

### 3. Performance Alerts

**Alert Thresholds:**
```python
PERFORMANCE_THRESHOLDS = {
    'response_time_p95': 2.0,  # 95th percentile < 2s
    'error_rate': 0.05,        # < 5% error rate
    'memory_usage': 0.8,       # < 80% memory usage
    'cpu_usage': 0.7,          # < 70% CPU usage
    'cache_hit_rate': 0.8      # > 80% cache hit rate
}

def check_performance_thresholds():
    metrics = get_current_metrics()
    alerts = []
    
    for metric, threshold in PERFORMANCE_THRESHOLDS.items():
        if metric in ['response_time_p95', 'error_rate', 'memory_usage', 'cpu_usage']:
            if metrics[metric] > threshold:
                alerts.append(f"{metric} exceeded threshold: {metrics[metric]} > {threshold}")
        elif metric == 'cache_hit_rate':
            if metrics[metric] < threshold:
                alerts.append(f"{metric} below threshold: {metrics[metric]} < {threshold}")
    
    return alerts
```

## Performance Testing

### 1. Automated Performance Tests

**CI/CD Integration:**
```yaml
# .github/workflows/performance.yml
name: Performance Tests
on: [push, pull_request]

jobs:
  performance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      
      - name: Install dependencies
        run: pip install -r requirements.txt
      
      - name: Run performance tests
        run: python backend/tests/test_performance.py
      
      - name: Run load tests
        run: python backend/load_test.py --light
```

### 2. Performance Regression Detection

**Benchmark Comparison:**
```python
def compare_performance_benchmarks():
    current_metrics = run_performance_tests()
    baseline_metrics = load_baseline_metrics()
    
    regressions = []
    
    for metric, current_value in current_metrics.items():
        baseline_value = baseline_metrics.get(metric)
        if baseline_value:
            change_percent = ((current_value - baseline_value) / baseline_value) * 100
            
            if change_percent > 10:  # 10% regression threshold
                regressions.append({
                    'metric': metric,
                    'current': current_value,
                    'baseline': baseline_value,
                    'change_percent': change_percent
                })
    
    return regressions
```

## Optimization Checklist

### Database
- [ ] Proper indexes on frequently queried columns
- [ ] Query optimization with EXPLAIN ANALYZE
- [ ] Connection pooling configured
- [ ] Slow query monitoring enabled

### Caching
- [ ] Redis properly configured
- [ ] Cache hit rate > 80%
- [ ] Cache invalidation strategy implemented
- [ ] Cache warming for critical data

### API
- [ ] Response compression enabled
- [ ] Async processing for long operations
- [ ] Proper pagination implemented
- [ ] Rate limiting configured

### Memory
- [ ] Memory profiling implemented
- [ ] Resource cleanup in place
- [ ] Streaming for large datasets
- [ ] Memory leak detection

### Monitoring
- [ ] Performance metrics collection
- [ ] Health check endpoints
- [ ] Alert thresholds configured
- [ ] Performance regression detection

## Troubleshooting Common Issues

### High Response Times
1. Check database query performance
2. Verify cache hit rates
3. Monitor CPU and memory usage
4. Review API endpoint complexity

### Memory Leaks
1. Use memory profiling tools
2. Check for unclosed connections
3. Review object lifecycle management
4. Monitor garbage collection

### Database Performance
1. Analyze slow query log
2. Check index usage
3. Monitor connection pool
4. Review query patterns

### Cache Issues
1. Monitor cache hit rates
2. Check cache expiration policies
3. Verify cache invalidation
4. Review cache key patterns
