"""
Testing configuration for LinkUp Plugin Backend
"""
import os
import tempfile
from config.base import BaseConfig


class TestingConfig(BaseConfig):
    """Testing configuration"""
    
    # Testing flags
    TESTING = True
    DEBUG = True
    WTF_CSRF_ENABLED = False
    
    # Database configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or 'sqlite:///:memory:'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Redis configuration (use different database for testing)
    REDIS_URL = os.environ.get('TEST_REDIS_URL') or 'redis://localhost:6379/15'
    
    # JWT configuration
    JWT_SECRET_KEY = 'test-jwt-secret-key-do-not-use-in-production'
    JWT_ACCESS_TOKEN_EXPIRES = False  # Don't expire tokens in tests
    JWT_REFRESH_TOKEN_EXPIRES = False
    
    # Celery configuration
    CELERY_TASK_ALWAYS_EAGER = True  # Execute tasks synchronously in tests
    CELERY_TASK_EAGER_PROPAGATES = True  # Propagate exceptions in tests
    
    # Rate limiting (disabled for testing)
    RATELIMIT_ENABLED = False
    
    # Email configuration (use console backend for testing)
    MAIL_BACKEND = 'console'
    MAIL_SUPPRESS_SEND = True
    
    # File upload configuration
    UPLOAD_FOLDER = tempfile.mkdtemp()
    MAX_CONTENT_LENGTH = 1 * 1024 * 1024  # 1MB for testing
    
    # External API configuration (use mock endpoints)
    WORDPRESS_API_TIMEOUT = 5
    EXTERNAL_API_TIMEOUT = 5
    
    # Logging configuration
    LOG_LEVEL = 'DEBUG'
    LOG_TO_STDOUT = True
    
    # Security configuration (relaxed for testing)
    SECRET_KEY = 'test-secret-key-do-not-use-in-production'
    SECURITY_PASSWORD_SALT = 'test-salt'
    
    # Content analysis configuration
    CONTENT_ANALYSIS_TIMEOUT = 30
    MAX_CONTENT_LENGTH = 10000  # Smaller limit for testing
    
    # Matching service configuration
    MATCHING_BATCH_SIZE = 10
    MATCHING_CACHE_TTL = 300  # 5 minutes
    
    # Feature flags for testing
    FEATURE_FLAGS = {
        'ADVANCED_ANALYTICS': True,
        'PREMIUM_FEATURES': True,
        'BETA_FEATURES': True,
        'AI_CONTENT_GENERATION': False,  # Disabled to avoid API costs
        'REAL_TIME_NOTIFICATIONS': False
    }
    
    # Mock external services
    MOCK_EXTERNAL_SERVICES = True
    
    # Test data configuration
    TEST_USER_EMAIL = '<EMAIL>'
    TEST_USER_PASSWORD = 'testpassword123'
    TEST_ADMIN_EMAIL = '<EMAIL>'
    TEST_ADMIN_PASSWORD = 'adminpassword123'
    
    @staticmethod
    def init_app(app):
        """Initialize testing configuration"""
        BaseConfig.init_app(app)
        
        # Disable logging during tests to reduce noise
        import logging
        logging.disable(logging.CRITICAL)
        
        # Set up test database
        with app.app_context():
            from app import db
            db.create_all()
    
    @classmethod
    def cleanup(cls):
        """Cleanup testing resources"""
        import shutil
        import os
        
        # Clean up temporary upload folder
        if os.path.exists(cls.UPLOAD_FOLDER):
            shutil.rmtree(cls.UPLOAD_FOLDER)


# Test data factories configuration
FACTORY_SETTINGS = {
    'FACTORY_FOR_DJANGO_MODELS': False,
    'FACTORY_RANDOM_SEED': 12345,  # For reproducible tests
}

# Mock service configurations
MOCK_SERVICES = {
    'wordpress_api': {
        'base_url': 'http://mock-wordpress.test',
        'timeout': 5
    },
    'seo_api': {
        'base_url': 'http://mock-seo.test',
        'api_key': 'mock-api-key'
    },
    'analytics_api': {
        'base_url': 'http://mock-analytics.test',
        'timeout': 10
    }
}

# Test database configuration (SQLite doesn't support pooling)
TEST_DATABASE_CONFIG = {
    # SQLite-specific settings
    'connect_args': {'check_same_thread': False}
}
