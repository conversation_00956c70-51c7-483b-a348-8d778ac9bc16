#!/usr/bin/env python3
"""
Bug Tracking and Management System for LinkUp Plugin
Identifies, tracks, and manages bug fixes and code improvements
"""
import os
import sys
import json
import re
import ast
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
import subprocess


class BugTracker:
    """Main bug tracking and management system"""
    
    def __init__(self, project_root=None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.bugs = []
        self.improvements = []
        self.fixed_issues = []
        
        # Bug patterns to detect
        self.bug_patterns = {
            'todo_comments': r'#\s*TODO|//\s*TODO|/\*\s*TODO',
            'fixme_comments': r'#\s*FIXME|//\s*FIXME|/\*\s*FIXME',
            'hack_comments': r'#\s*HACK|//\s*HACK|/\*\s*HACK',
            'deprecated_usage': r'@deprecated|deprecated',
            'empty_catch_blocks': r'except.*:\s*pass',
            'hardcoded_values': r'localhost|127\.0\.0\.1|password.*=.*["\'][^"\']+["\']',
            'debug_statements': r'console\.log|print\s*\(.*debug|var_dump',
            'unused_imports': r'^import\s+\w+$',
            'long_functions': None,  # Detected programmatically
            'duplicate_code': None,  # Detected programmatically
        }
    
    def scan_for_bugs(self):
        """Scan codebase for potential bugs and issues"""
        print("🐛 Scanning for Bugs and Issues...")
        print("=" * 35)
        
        # Scan Python files
        python_files = list(self.project_root.rglob("*.py"))
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
            self._scan_python_file(file_path)
        
        # Scan JavaScript files
        js_files = list(self.project_root.rglob("*.js"))
        for file_path in js_files:
            if self._should_skip_file(file_path):
                continue
            self._scan_js_file(file_path)
        
        # Scan PHP files
        php_files = list(self.project_root.rglob("*.php"))
        for file_path in php_files:
            if self._should_skip_file(file_path):
                continue
            self._scan_php_file(file_path)
        
        # Detect structural issues
        self._detect_structural_issues()
        
        print(f"✅ Scanned {len(python_files + js_files + php_files)} files")
        print(f"📊 Found {len(self.bugs)} bugs and {len(self.improvements)} improvements")
        print()
    
    def _should_skip_file(self, file_path):
        """Check if file should be skipped"""
        skip_patterns = [
            'venv', '__pycache__', '.git', 'node_modules',
            'vendor', '.pytest_cache', 'htmlcov'
        ]
        return any(pattern in str(file_path) for pattern in skip_patterns)
    
    def _scan_python_file(self, file_path):
        """Scan Python file for issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Pattern-based detection
            self._detect_patterns(file_path, content, 'python')
            
            # AST-based analysis
            try:
                tree = ast.parse(content)
                self._analyze_python_ast(file_path, tree, content)
            except SyntaxError as e:
                self._add_bug(
                    'syntax_error',
                    'HIGH',
                    str(file_path),
                    e.lineno,
                    f"Syntax error: {e.msg}"
                )
        
        except Exception as e:
            self._add_bug(
                'file_read_error',
                'LOW',
                str(file_path),
                0,
                f"Could not read file: {e}"
            )
    
    def _scan_js_file(self, file_path):
        """Scan JavaScript file for issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self._detect_patterns(file_path, content, 'javascript')
            
            # JavaScript-specific checks
            self._check_js_specific_issues(file_path, content)
        
        except Exception as e:
            self._add_bug(
                'file_read_error',
                'LOW',
                str(file_path),
                0,
                f"Could not read file: {e}"
            )
    
    def _scan_php_file(self, file_path):
        """Scan PHP file for issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self._detect_patterns(file_path, content, 'php')
            
            # PHP-specific checks
            self._check_php_specific_issues(file_path, content)
        
        except Exception as e:
            self._add_bug(
                'file_read_error',
                'LOW',
                str(file_path),
                0,
                f"Could not read file: {e}"
            )
    
    def _detect_patterns(self, file_path, content, language):
        """Detect pattern-based issues"""
        lines = content.split('\n')
        
        for pattern_name, pattern in self.bug_patterns.items():
            if pattern is None:
                continue
            
            matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                
                severity = self._get_pattern_severity(pattern_name)
                description = self._get_pattern_description(pattern_name, match.group())
                
                if pattern_name in ['todo_comments', 'fixme_comments', 'hack_comments']:
                    self._add_improvement(pattern_name, severity, str(file_path), line_num, description)
                else:
                    self._add_bug(pattern_name, severity, str(file_path), line_num, description)
    
    def _analyze_python_ast(self, file_path, tree, content):
        """Analyze Python AST for issues"""
        lines = content.split('\n')
        
        for node in ast.walk(tree):
            # Check for long functions
            if isinstance(node, ast.FunctionDef):
                if hasattr(node, 'end_lineno'):
                    func_length = node.end_lineno - node.lineno
                    if func_length > 50:  # Functions longer than 50 lines
                        self._add_improvement(
                            'long_function',
                            'MEDIUM',
                            str(file_path),
                            node.lineno,
                            f"Function '{node.name}' is {func_length} lines long (consider refactoring)"
                        )
            
            # Check for too many arguments
            if isinstance(node, ast.FunctionDef):
                arg_count = len(node.args.args)
                if arg_count > 7:  # More than 7 arguments
                    self._add_improvement(
                        'too_many_arguments',
                        'MEDIUM',
                        str(file_path),
                        node.lineno,
                        f"Function '{node.name}' has {arg_count} arguments (consider refactoring)"
                    )
            
            # Check for deeply nested code
            if isinstance(node, (ast.If, ast.For, ast.While)):
                depth = self._calculate_nesting_depth(node)
                if depth > 4:  # More than 4 levels of nesting
                    self._add_improvement(
                        'deep_nesting',
                        'MEDIUM',
                        str(file_path),
                        node.lineno,
                        f"Code is nested {depth} levels deep (consider refactoring)"
                    )
    
    def _check_js_specific_issues(self, file_path, content):
        """Check JavaScript-specific issues"""
        lines = content.split('\n')
        
        # Check for var usage (should use let/const)
        var_matches = re.finditer(r'\bvar\s+\w+', content)
        for match in var_matches:
            line_num = content[:match.start()].count('\n') + 1
            self._add_improvement(
                'var_usage',
                'LOW',
                str(file_path),
                line_num,
                "Use 'let' or 'const' instead of 'var'"
            )
        
        # Check for == usage (should use ===)
        equality_matches = re.finditer(r'[^=!]==[^=]', content)
        for match in equality_matches:
            line_num = content[:match.start()].count('\n') + 1
            self._add_improvement(
                'loose_equality',
                'MEDIUM',
                str(file_path),
                line_num,
                "Use strict equality (===) instead of loose equality (==)"
            )
    
    def _check_php_specific_issues(self, file_path, content):
        """Check PHP-specific issues"""
        # Check for deprecated PHP functions
        deprecated_functions = [
            'mysql_connect', 'mysql_query', 'ereg', 'split',
            'each', 'create_function'
        ]
        
        for func in deprecated_functions:
            if func in content:
                matches = re.finditer(rf'\b{func}\s*\(', content)
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    self._add_bug(
                        'deprecated_function',
                        'HIGH',
                        str(file_path),
                        line_num,
                        f"Deprecated function '{func}' used"
                    )
        
        # Check for SQL injection vulnerabilities
        sql_patterns = [
            r'\$_GET\[.*\].*query',
            r'\$_POST\[.*\].*query',
            r'query.*\$_',
        ]
        
        for pattern in sql_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                self._add_bug(
                    'potential_sql_injection',
                    'HIGH',
                    str(file_path),
                    line_num,
                    "Potential SQL injection vulnerability"
                )
    
    def _detect_structural_issues(self):
        """Detect structural code issues"""
        # Check for duplicate code
        self._detect_duplicate_code()
        
        # Check for missing documentation
        self._check_documentation_coverage()
        
        # Check for test coverage
        self._check_test_coverage()
    
    def _detect_duplicate_code(self):
        """Detect duplicate code blocks"""
        # Simple duplicate detection based on similar lines
        python_files = list(self.project_root.rglob("*.py"))
        
        code_blocks = {}
        
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # Check for blocks of 5+ similar lines
                for i in range(len(lines) - 4):
                    block = ''.join(lines[i:i+5]).strip()
                    if len(block) > 100:  # Only check substantial blocks
                        block_hash = hash(block)
                        
                        if block_hash in code_blocks:
                            self._add_improvement(
                                'duplicate_code',
                                'MEDIUM',
                                str(file_path),
                                i + 1,
                                f"Duplicate code block found (also in {code_blocks[block_hash]})"
                            )
                        else:
                            code_blocks[block_hash] = str(file_path)
            
            except Exception:
                continue
    
    def _check_documentation_coverage(self):
        """Check documentation coverage"""
        python_files = list(self.project_root.rglob("*.py"))
        
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                        if not ast.get_docstring(node):
                            self._add_improvement(
                                'missing_docstring',
                                'LOW',
                                str(file_path),
                                node.lineno,
                                f"{type(node).__name__} '{node.name}' missing docstring"
                            )
            
            except Exception:
                continue
    
    def _check_test_coverage(self):
        """Check test coverage"""
        # Check if test files exist for main modules
        src_files = list(self.project_root.rglob("app/**/*.py"))
        test_files = list(self.project_root.rglob("tests/**/*.py"))
        
        test_file_names = {f.stem for f in test_files}
        
        for src_file in src_files:
            if self._should_skip_file(src_file):
                continue
            
            expected_test_name = f"test_{src_file.stem}"
            if expected_test_name not in test_file_names:
                self._add_improvement(
                    'missing_tests',
                    'MEDIUM',
                    str(src_file),
                    1,
                    f"No test file found for {src_file.name}"
                )
    
    def _calculate_nesting_depth(self, node, depth=0):
        """Calculate nesting depth of AST node"""
        max_depth = depth
        
        for child in ast.iter_child_nodes(node):
            if isinstance(child, (ast.If, ast.For, ast.While, ast.With, ast.Try)):
                child_depth = self._calculate_nesting_depth(child, depth + 1)
                max_depth = max(max_depth, child_depth)
        
        return max_depth
    
    def _get_pattern_severity(self, pattern_name):
        """Get severity for pattern"""
        severity_map = {
            'todo_comments': 'LOW',
            'fixme_comments': 'MEDIUM',
            'hack_comments': 'HIGH',
            'deprecated_usage': 'HIGH',
            'empty_catch_blocks': 'MEDIUM',
            'hardcoded_values': 'MEDIUM',
            'debug_statements': 'LOW',
            'unused_imports': 'LOW'
        }
        return severity_map.get(pattern_name, 'MEDIUM')
    
    def _get_pattern_description(self, pattern_name, match_text):
        """Get description for pattern match"""
        descriptions = {
            'todo_comments': f"TODO comment found: {match_text}",
            'fixme_comments': f"FIXME comment found: {match_text}",
            'hack_comments': f"HACK comment found: {match_text}",
            'deprecated_usage': f"Deprecated usage: {match_text}",
            'empty_catch_blocks': "Empty exception handler",
            'hardcoded_values': f"Hardcoded value: {match_text}",
            'debug_statements': f"Debug statement: {match_text}",
            'unused_imports': f"Potentially unused import: {match_text}"
        }
        return descriptions.get(pattern_name, f"Issue found: {match_text}")
    
    def _add_bug(self, bug_type, severity, file_path, line_number, description):
        """Add bug to tracking list"""
        bug = {
            'id': f"BUG-{len(self.bugs) + 1:04d}",
            'type': bug_type,
            'severity': severity,
            'file': file_path,
            'line': line_number,
            'description': description,
            'status': 'OPEN',
            'created_at': datetime.now().isoformat()
        }
        self.bugs.append(bug)
    
    def _add_improvement(self, improvement_type, priority, file_path, line_number, description):
        """Add improvement to tracking list"""
        improvement = {
            'id': f"IMP-{len(self.improvements) + 1:04d}",
            'type': improvement_type,
            'priority': priority,
            'file': file_path,
            'line': line_number,
            'description': description,
            'status': 'OPEN',
            'created_at': datetime.now().isoformat()
        }
        self.improvements.append(improvement)
    
    def apply_automated_fixes(self):
        """Apply automated fixes for common issues"""
        print("🔧 Applying Automated Fixes...")
        print("-" * 30)
        
        fixes_applied = 0
        
        # Fix simple issues that can be automated
        for bug in self.bugs:
            if bug['status'] == 'OPEN':
                if self._apply_automated_fix(bug):
                    bug['status'] = 'FIXED'
                    fixes_applied += 1
        
        for improvement in self.improvements:
            if improvement['status'] == 'OPEN':
                if self._apply_automated_improvement(improvement):
                    improvement['status'] = 'FIXED'
                    fixes_applied += 1
        
        print(f"✅ Applied {fixes_applied} automated fixes")
        print()
    
    def _apply_automated_fix(self, bug):
        """Apply automated fix for a specific bug"""
        try:
            file_path = Path(bug['file'])
            
            if not file_path.exists():
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Apply fixes based on bug type
            if bug['type'] == 'debug_statements':
                # Remove debug print statements
                content = re.sub(r'print\s*\([^)]*debug[^)]*\)', '', content, flags=re.IGNORECASE)
            
            elif bug['type'] == 'empty_catch_blocks':
                # Add logging to empty catch blocks
                content = re.sub(
                    r'except.*:\s*pass',
                    'except Exception as e:\n        logger.warning(f"Exception caught: {e}")',
                    content
                )
            
            # Write back if changes were made
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
        
        except Exception as e:
            print(f"Failed to apply fix for {bug['id']}: {e}")
        
        return False
    
    def _apply_automated_improvement(self, improvement):
        """Apply automated improvement"""
        try:
            file_path = Path(improvement['file'])
            
            if not file_path.exists():
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Apply improvements based on type
            if improvement['type'] == 'var_usage':
                # Replace var with let (simple cases)
                content = re.sub(r'\bvar\s+(\w+)\s*=', r'let \1 =', content)
            
            elif improvement['type'] == 'loose_equality':
                # Replace == with === (simple cases)
                content = re.sub(r'([^=!])==([^=])', r'\1===\2', content)
            
            # Write back if changes were made
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
        
        except Exception as e:
            print(f"Failed to apply improvement for {improvement['id']}: {e}")
        
        return False
    
    def generate_bug_report(self):
        """Generate comprehensive bug report"""
        print("\n📋 BUG TRACKING REPORT")
        print("=" * 50)
        
        total_bugs = len(self.bugs)
        total_improvements = len(self.improvements)
        
        # Count by severity
        bug_severity_counts = {}
        for bug in self.bugs:
            severity = bug['severity']
            bug_severity_counts[severity] = bug_severity_counts.get(severity, 0) + 1
        
        # Count by status
        open_bugs = sum(1 for bug in self.bugs if bug['status'] == 'OPEN')
        fixed_bugs = sum(1 for bug in self.bugs if bug['status'] == 'FIXED')
        
        print(f"Total Bugs: {total_bugs}")
        print(f"Total Improvements: {total_improvements}")
        print(f"Open Issues: {open_bugs}")
        print(f"Fixed Issues: {fixed_bugs}")
        print()
        
        # Severity breakdown
        if bug_severity_counts:
            print("Bug Severity Breakdown:")
            print("-" * 25)
            for severity, count in sorted(bug_severity_counts.items()):
                print(f"{severity}: {count}")
            print()
        
        # Top issues by type
        bug_types = {}
        for bug in self.bugs:
            bug_type = bug['type']
            bug_types[bug_type] = bug_types.get(bug_type, 0) + 1
        
        if bug_types:
            print("Top Bug Types:")
            print("-" * 15)
            for bug_type, count in sorted(bug_types.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"{bug_type}: {count}")
            print()
        
        # Critical issues
        critical_bugs = [bug for bug in self.bugs if bug['severity'] == 'HIGH' and bug['status'] == 'OPEN']
        
        if critical_bugs:
            print("🚨 CRITICAL ISSUES (HIGH SEVERITY):")
            print("-" * 35)
            for bug in critical_bugs[:5]:  # Show top 5
                print(f"• {bug['id']}: {bug['description']}")
                print(f"  File: {bug['file']}:{bug['line']}")
                print()
        
        # Recommendations
        print("💡 RECOMMENDATIONS:")
        print("-" * 20)
        
        if total_bugs == 0:
            print("🎉 No bugs found! Code quality is excellent.")
        elif open_bugs <= 5:
            print("✅ Low bug count. Address remaining issues before release.")
        elif open_bugs <= 20:
            print("⚠️ Moderate bug count. Prioritize high-severity issues.")
        else:
            print("❌ High bug count. Significant cleanup needed before release.")
        
        print()
        print("=" * 50)
        print(f"Report generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Save detailed report
        report_data = {
            'summary': {
                'total_bugs': total_bugs,
                'total_improvements': total_improvements,
                'open_bugs': open_bugs,
                'fixed_bugs': fixed_bugs,
                'severity_breakdown': bug_severity_counts
            },
            'bugs': self.bugs,
            'improvements': self.improvements,
            'generated_at': datetime.now().isoformat()
        }
        
        with open('bug_report.json', 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print("📄 Detailed report saved to bug_report.json")


    def create_fix_suggestions(self):
        """Create fix suggestions for identified issues"""
        suggestions = []

        for bug in self.bugs:
            if bug['status'] == 'OPEN':
                suggestion = self._generate_fix_suggestion(bug)
                if suggestion:
                    suggestions.append(suggestion)

        return suggestions

    def _generate_fix_suggestion(self, bug):
        """Generate fix suggestion for a specific bug"""
        fix_templates = {
            'deprecated_function': {
                'description': 'Replace deprecated function with modern alternative',
                'example': 'Replace mysql_connect() with PDO or mysqli',
                'priority': 'HIGH'
            },
            'potential_sql_injection': {
                'description': 'Use prepared statements to prevent SQL injection',
                'example': 'Use $stmt = $pdo->prepare("SELECT * FROM table WHERE id = ?"); $stmt->execute([$id]);',
                'priority': 'CRITICAL'
            },
            'hardcoded_values': {
                'description': 'Move hardcoded values to configuration',
                'example': 'Use environment variables or config files',
                'priority': 'MEDIUM'
            },
            'empty_catch_blocks': {
                'description': 'Add proper error handling',
                'example': 'Log the exception or handle it appropriately',
                'priority': 'MEDIUM'
            }
        }

        template = fix_templates.get(bug['type'])
        if template:
            return {
                'bug_id': bug['id'],
                'fix_description': template['description'],
                'example': template['example'],
                'priority': template['priority'],
                'file': bug['file'],
                'line': bug['line']
            }

        return None


def main():
    """Main function to run bug tracking"""
    tracker = BugTracker()

    # Scan for bugs
    tracker.scan_for_bugs()

    # Apply automated fixes
    tracker.apply_automated_fixes()

    # Create fix suggestions
    suggestions = tracker.create_fix_suggestions()

    # Generate report
    tracker.generate_bug_report()

    # Print fix suggestions
    if suggestions:
        print("\n🔧 FIX SUGGESTIONS:")
        print("-" * 20)
        for suggestion in suggestions[:10]:  # Show top 10
            print(f"• {suggestion['bug_id']}: {suggestion['fix_description']}")
            print(f"  File: {suggestion['file']}:{suggestion['line']}")
            print(f"  Example: {suggestion['example']}")
            print()

    # Exit with appropriate code
    critical_bugs = sum(1 for bug in tracker.bugs if bug['severity'] == 'HIGH' and bug['status'] == 'OPEN')
    exit(0 if critical_bugs == 0 else 1)


if __name__ == '__main__':
    main()
