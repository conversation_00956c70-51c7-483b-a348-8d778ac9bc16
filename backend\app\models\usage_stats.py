"""
Usage Statistics model for LinkUp Plugin Backend
"""
from datetime import datetime, date
from app import db
from sqlalchemy import func, and_


class UsageStats(db.Model):
    """Usage statistics model for tracking user activity and plugin performance"""
    
    __tablename__ = 'usage_stats'
    
    # Primary fields
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    website_id = db.Column(db.Integer, db.ForeignKey('websites.id'), nullable=True, index=True)
    date = db.Column(db.Date, nullable=False, index=True)
    
    # API Usage
    api_requests = db.Column(db.Integer, default=0)
    api_errors = db.Column(db.Integer, default=0)
    api_response_time_avg = db.Column(db.Float, default=0.0)  # Average response time in ms
    
    # Backlink Activity
    backlinks_created = db.Column(db.Integer, default=0)
    backlinks_approved = db.Column(db.Integer, default=0)
    backlinks_rejected = db.Column(db.Integer, default=0)
    backlinks_removed = db.Column(db.Integer, default=0)
    
    # Content Analysis
    content_analyzed = db.Column(db.Integer, default=0)  # Number of posts/pages analyzed
    keywords_extracted = db.Column(db.Integer, default=0)
    content_quality_avg = db.Column(db.Float, default=0.0)
    
    # Traffic & SEO Metrics
    organic_traffic = db.Column(db.Integer, default=0)
    backlink_traffic = db.Column(db.Integer, default=0)
    ranking_improvements = db.Column(db.Integer, default=0)
    ranking_declines = db.Column(db.Integer, default=0)
    
    # User Engagement
    dashboard_views = db.Column(db.Integer, default=0)
    settings_changes = db.Column(db.Integer, default=0)
    feature_usage = db.Column(db.JSON, default=dict)  # Track which features are used
    
    # Performance Metrics
    page_load_time_avg = db.Column(db.Float, default=0.0)  # WordPress page load impact
    plugin_memory_usage = db.Column(db.Float, default=0.0)  # Memory usage in MB
    database_queries = db.Column(db.Integer, default=0)
    
    # Error Tracking
    php_errors = db.Column(db.Integer, default=0)
    javascript_errors = db.Column(db.Integer, default=0)
    plugin_conflicts = db.Column(db.Integer, default=0)
    
    # Business Metrics
    revenue_attributed = db.Column(db.Float, default=0.0)  # Revenue attributed to backlinks
    conversion_rate = db.Column(db.Float, default=0.0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', back_populates='usage_stats')
    website = db.relationship('Website', back_populates='usage_stats')
    
    # Unique constraint to prevent duplicate entries per user/website/date
    __table_args__ = (
        db.UniqueConstraint('user_id', 'website_id', 'date', name='unique_usage_per_day'),
    )
    
    @classmethod
    def get_or_create_today(cls, user_id, website_id=None):
        """Get or create usage stats record for today"""
        today = date.today()
        
        stats = cls.query.filter_by(
            user_id=user_id,
            website_id=website_id,
            date=today
        ).first()
        
        if not stats:
            stats = cls(
                user_id=user_id,
                website_id=website_id,
                date=today
            )
            db.session.add(stats)
            db.session.commit()
        
        return stats
    
    @classmethod
    def record_api_request(cls, user_id, website_id=None, response_time=None, error=False):
        """Record an API request"""
        stats = cls.get_or_create_today(user_id, website_id)
        
        stats.api_requests += 1
        if error:
            stats.api_errors += 1
        
        if response_time:
            # Calculate running average
            total_requests = stats.api_requests
            current_avg = stats.api_response_time_avg
            stats.api_response_time_avg = ((current_avg * (total_requests - 1)) + response_time) / total_requests
        
        db.session.commit()
    
    @classmethod
    def record_backlink_activity(cls, user_id, website_id, activity_type):
        """Record backlink activity"""
        stats = cls.get_or_create_today(user_id, website_id)
        
        if activity_type == 'created':
            stats.backlinks_created += 1
        elif activity_type == 'approved':
            stats.backlinks_approved += 1
        elif activity_type == 'rejected':
            stats.backlinks_rejected += 1
        elif activity_type == 'removed':
            stats.backlinks_removed += 1
        
        db.session.commit()
    
    @classmethod
    def record_content_analysis(cls, user_id, website_id, keywords_count=0, quality_score=0.0):
        """Record content analysis activity"""
        stats = cls.get_or_create_today(user_id, website_id)
        
        stats.content_analyzed += 1
        stats.keywords_extracted += keywords_count
        
        # Calculate running average for quality score
        total_analyzed = stats.content_analyzed
        current_avg = stats.content_quality_avg
        stats.content_quality_avg = ((current_avg * (total_analyzed - 1)) + quality_score) / total_analyzed
        
        db.session.commit()
    
    @classmethod
    def record_traffic_data(cls, user_id, website_id, organic_traffic=0, backlink_traffic=0):
        """Record traffic data"""
        stats = cls.get_or_create_today(user_id, website_id)
        
        stats.organic_traffic = organic_traffic
        stats.backlink_traffic = backlink_traffic
        
        db.session.commit()
    
    @classmethod
    def record_user_engagement(cls, user_id, website_id=None, activity_type=None, feature_name=None):
        """Record user engagement activity"""
        stats = cls.get_or_create_today(user_id, website_id)
        
        if activity_type == 'dashboard_view':
            stats.dashboard_views += 1
        elif activity_type == 'settings_change':
            stats.settings_changes += 1
        elif activity_type == 'feature_usage' and feature_name:
            if not stats.feature_usage:
                stats.feature_usage = {}
            stats.feature_usage[feature_name] = stats.feature_usage.get(feature_name, 0) + 1
        
        db.session.commit()
    
    @classmethod
    def get_user_summary(cls, user_id, days=30):
        """Get usage summary for a user over specified days"""
        start_date = date.today() - timedelta(days=days)
        
        summary = db.session.query(
            func.sum(cls.api_requests).label('total_api_requests'),
            func.sum(cls.backlinks_created).label('total_backlinks_created'),
            func.sum(cls.content_analyzed).label('total_content_analyzed'),
            func.sum(cls.organic_traffic).label('total_organic_traffic'),
            func.sum(cls.backlink_traffic).label('total_backlink_traffic'),
            func.avg(cls.content_quality_avg).label('avg_content_quality'),
            func.sum(cls.dashboard_views).label('total_dashboard_views')
        ).filter(
            cls.user_id == user_id,
            cls.date >= start_date
        ).first()
        
        return {
            'api_requests': summary.total_api_requests or 0,
            'backlinks_created': summary.total_backlinks_created or 0,
            'content_analyzed': summary.total_content_analyzed or 0,
            'organic_traffic': summary.total_organic_traffic or 0,
            'backlink_traffic': summary.total_backlink_traffic or 0,
            'avg_content_quality': round(summary.avg_content_quality or 0, 2),
            'dashboard_views': summary.total_dashboard_views or 0
        }
    
    @classmethod
    def get_system_metrics(cls, days=7):
        """Get system-wide metrics"""
        start_date = date.today() - timedelta(days=days)
        
        metrics = db.session.query(
            func.sum(cls.api_requests).label('total_api_requests'),
            func.sum(cls.api_errors).label('total_api_errors'),
            func.avg(cls.api_response_time_avg).label('avg_response_time'),
            func.sum(cls.backlinks_created).label('total_backlinks'),
            func.count(func.distinct(cls.user_id)).label('active_users'),
            func.avg(cls.content_quality_avg).label('avg_content_quality')
        ).filter(
            cls.date >= start_date
        ).first()
        
        error_rate = 0
        if metrics.total_api_requests and metrics.total_api_requests > 0:
            error_rate = (metrics.total_api_errors or 0) / metrics.total_api_requests * 100
        
        return {
            'api_requests': metrics.total_api_requests or 0,
            'api_error_rate': round(error_rate, 2),
            'avg_response_time': round(metrics.avg_response_time or 0, 2),
            'backlinks_created': metrics.total_backlinks or 0,
            'active_users': metrics.active_users or 0,
            'avg_content_quality': round(metrics.avg_content_quality or 0, 2)
        }
    
    def to_dict(self):
        """Convert usage stats to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'website_id': self.website_id,
            'date': self.date.isoformat(),
            'api_requests': self.api_requests,
            'api_errors': self.api_errors,
            'api_response_time_avg': self.api_response_time_avg,
            'backlinks_created': self.backlinks_created,
            'backlinks_approved': self.backlinks_approved,
            'content_analyzed': self.content_analyzed,
            'organic_traffic': self.organic_traffic,
            'backlink_traffic': self.backlink_traffic,
            'dashboard_views': self.dashboard_views,
            'feature_usage': self.feature_usage,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<UsageStats User:{self.user_id} Date:{self.date}>'
