"""
User model for LinkUp Plugin Backend
"""
from datetime import datetime, timedelta
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from app import db
import uuid
import hashlib
import secrets


class User(db.Model):
    """User model with enhanced authentication and role management"""
    
    __tablename__ = 'users'
    
    # Primary fields
    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    email = db.Column(db.String(255), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # Profile information
    first_name = db.Column(db.String(100))
    last_name = db.Column(db.String(100))
    company = db.Column(db.String(200))
    
    # Account status
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    is_verified = db.Column(db.Boolean, default=False, nullable=False)
    email_verified_at = db.Column(db.DateTime)
    
    # Subscription and plan
    plan = db.Column(db.String(20), default='free', nullable=False)  # free, pro, agency
    plan_expires_at = db.Column(db.DateTime)
    stripe_customer_id = db.Column(db.String(100))
    
    # Role and permissions
    role = db.Column(db.String(20), default='user', nullable=False)  # user, admin, super_admin
    permissions = db.Column(db.JSON, default=list)
    
    # Authentication settings
    two_factor_enabled = db.Column(db.Boolean, default=False, nullable=False)
    two_factor_secret = db.Column(db.String(32))
    
    # Privacy and data collection preferences
    data_collection_consent = db.Column(db.Boolean, default=False, nullable=False)
    analytics_consent = db.Column(db.Boolean, default=False, nullable=False)
    marketing_consent = db.Column(db.Boolean, default=False, nullable=False)
    privacy_settings = db.Column(db.JSON, default=dict)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login_at = db.Column(db.DateTime)
    last_activity_at = db.Column(db.DateTime)
    
    # Relationships
    websites = db.relationship('Website', back_populates='user', lazy='dynamic', cascade='all, delete-orphan')
    api_keys = db.relationship('ApiKey', back_populates='user', lazy='dynamic', cascade='all, delete-orphan')
    usage_stats = db.relationship('UsageStats', back_populates='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(User, self).__init__(**kwargs)
        if not self.uuid:
            self.uuid = str(uuid.uuid4())
    
    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)
    
    def generate_api_key(self, website_id=None, description=None):
        """Generate a new API key for this user"""
        from app.models.api_key import ApiKey
        
        # Generate secure random key
        raw_key = secrets.token_urlsafe(32)
        key_hash = hashlib.sha256(raw_key.encode()).hexdigest()
        
        # Create API key record
        api_key = ApiKey(
            user_id=self.id,
            website_id=website_id,
            key_hash=key_hash,
            description=description or f"API Key generated on {datetime.utcnow().strftime('%Y-%m-%d')}"
        )
        
        db.session.add(api_key)
        db.session.commit()
        
        return raw_key, api_key
    
    def get_active_api_keys(self):
        """Get all active API keys for this user"""
        return self.api_keys.filter_by(is_active=True).all()
    
    def revoke_api_key(self, api_key_id):
        """Revoke a specific API key"""
        api_key = self.api_keys.filter_by(id=api_key_id).first()
        if api_key:
            api_key.is_active = False
            api_key.revoked_at = datetime.utcnow()
            db.session.commit()
            return True
        return False
    
    def get_plan_limits(self):
        """Get limits based on current plan"""
        limits = {
            'free': {
                'backlinks_per_month': 10,
                'websites': 1,
                'api_requests_per_hour': 100,
                'analytics_retention_days': 30,
                'priority_support': False
            },
            'pro': {
                'backlinks_per_month': 100,
                'websites': 5,
                'api_requests_per_hour': 1000,
                'analytics_retention_days': 365,
                'priority_support': True
            },
            'agency': {
                'backlinks_per_month': 1000,
                'websites': 50,
                'api_requests_per_hour': 10000,
                'analytics_retention_days': 365,
                'priority_support': True
            }
        }
        return limits.get(self.plan, limits['free'])
    
    def is_plan_active(self):
        """Check if user's plan is active"""
        if self.plan == 'free':
            return True
        return self.plan_expires_at and self.plan_expires_at > datetime.utcnow()
    
    def get_usage_this_month(self):
        """Get usage statistics for current month"""
        from app.models.usage_stats import UsageStats
        from sqlalchemy import func, extract
        
        current_month = datetime.utcnow().month
        current_year = datetime.utcnow().year
        
        usage = db.session.query(
            func.sum(UsageStats.backlinks_created).label('backlinks'),
            func.sum(UsageStats.api_requests).label('api_requests'),
            func.sum(UsageStats.content_analyzed).label('content_analyzed')
        ).filter(
            UsageStats.user_id == self.id,
            extract('month', UsageStats.date) == current_month,
            extract('year', UsageStats.date) == current_year
        ).first()
        
        return {
            'backlinks_created': usage.backlinks or 0,
            'api_requests': usage.api_requests or 0,
            'content_analyzed': usage.content_analyzed or 0
        }
    
    def can_create_backlink(self):
        """Check if user can create more backlinks this month"""
        if not self.is_plan_active():
            return False
        
        limits = self.get_plan_limits()
        usage = self.get_usage_this_month()
        
        return usage['backlinks_created'] < limits['backlinks_per_month']
    
    def update_last_activity(self):
        """Update last activity timestamp"""
        self.last_activity_at = datetime.utcnow()
        db.session.commit()
    
    def to_dict(self, include_sensitive=False):
        """Convert user to dictionary"""
        data = {
            'id': self.id,
            'uuid': self.uuid,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'company': self.company,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'plan': self.plan,
            'role': self.role,
            'two_factor_enabled': self.two_factor_enabled,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login_at': self.last_login_at.isoformat() if self.last_login_at else None,
            'plan_limits': self.get_plan_limits(),
            'usage_this_month': self.get_usage_this_month()
        }
        
        if include_sensitive:
            data.update({
                'privacy_settings': self.privacy_settings,
                'data_collection_consent': self.data_collection_consent,
                'analytics_consent': self.analytics_consent,
                'marketing_consent': self.marketing_consent
            })
        
        return data
    
    def __repr__(self):
        return f'<User {self.email}>'
