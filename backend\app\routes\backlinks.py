"""
Backlink management routes for LinkUp Plugin
"""
from flask import Blueprint, request, jsonify, g
from app.models.backlink import Backlink
from app.models.website import Website
from app.utils.decorators import require_auth, validate_json, handle_errors, log_api_usage
from app.utils.validators import validate_url, validate_backlink_status
from app import db
import logging

logger = logging.getLogger(__name__)

bp = Blueprint('backlinks', __name__)


@bp.route('/', methods=['GET'])
@require_auth
@handle_errors
@log_api_usage
def get_backlinks():
    """Get user's backlinks"""
    # Get query parameters
    status = request.args.get('status')
    direction = request.args.get('direction', 'both')  # inbound, outbound, both
    website_id = request.args.get('website_id')
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 20)), 100)
    
    # Build query
    query = Backlink.query
    
    if direction == 'inbound':
        # Get backlinks pointing to user's websites
        user_website_ids = [w.id for w in g.current_user.websites]
        query = query.filter(Backlink.target_website_id.in_(user_website_ids))
    elif direction == 'outbound':
        # Get backlinks from user's websites
        user_website_ids = [w.id for w in g.current_user.websites]
        query = query.filter(Backlink.source_website_id.in_(user_website_ids))
    else:  # both
        user_website_ids = [w.id for w in g.current_user.websites]
        query = query.filter(
            db.or_(
                Backlink.source_website_id.in_(user_website_ids),
                Backlink.target_website_id.in_(user_website_ids)
            )
        )
    
    if status:
        query = query.filter(Backlink.status == status)
    
    if website_id:
        query = query.filter(
            db.or_(
                Backlink.source_website_id == website_id,
                Backlink.target_website_id == website_id
            )
        )
    
    # Paginate results
    backlinks = query.order_by(Backlink.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'success': True,
        'data': {
            'backlinks': [backlink.to_dict() for backlink in backlinks.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': backlinks.total,
                'pages': backlinks.pages,
                'has_next': backlinks.has_next,
                'has_prev': backlinks.has_prev
            }
        }
    })


@bp.route('/', methods=['POST'])
@require_auth
@validate_json('source_website_id', 'target_website_id', 'source_url', 'target_url', 'anchor_text')
@handle_errors
@log_api_usage
def create_backlink():
    """Create a new backlink request"""
    data = request.get_json()
    
    # Validate URLs
    if not validate_url(data['source_url']):
        return jsonify({
            'success': False,
            'error': 'Invalid source URL'
        }), 400
    
    if not validate_url(data['target_url']):
        return jsonify({
            'success': False,
            'error': 'Invalid target URL'
        }), 400
    
    # Verify user owns the source website
    source_website = Website.query.filter_by(
        id=data['source_website_id'],
        user_id=g.current_user.id
    ).first()
    
    if not source_website:
        return jsonify({
            'success': False,
            'error': 'Source website not found or not owned by user'
        }), 404
    
    # Verify target website exists
    target_website = Website.query.get(data['target_website_id'])
    if not target_website:
        return jsonify({
            'success': False,
            'error': 'Target website not found'
        }), 404
    
    # Check if user can create more backlinks
    if not g.current_user.can_create_backlink():
        return jsonify({
            'success': False,
            'error': 'Monthly backlink limit exceeded'
        }), 403
    
    # Create backlink
    backlink = Backlink(
        source_website_id=data['source_website_id'],
        target_website_id=data['target_website_id'],
        source_url=data['source_url'],
        target_url=data['target_url'],
        anchor_text=data['anchor_text'],
        context_before=data.get('context_before', ''),
        context_after=data.get('context_after', ''),
        status='pending'
    )
    
    # Calculate initial scores
    backlink.relevance_score = backlink.calculate_relevance_score()
    backlink.quality_score = backlink.calculate_quality_score()
    
    db.session.add(backlink)
    db.session.commit()
    
    # Record backlink creation in usage stats
    from app.models.usage_stats import UsageStats
    UsageStats.record_backlink_activity(
        g.current_user.id,
        source_website.id,
        'created'
    )
    
    return jsonify({
        'success': True,
        'data': {
            'backlink': backlink.to_dict()
        }
    }), 201


@bp.route('/<int:backlink_id>', methods=['GET'])
@require_auth
@handle_errors
@log_api_usage
def get_backlink(backlink_id):
    """Get specific backlink details"""
    backlink = Backlink.query.get(backlink_id)
    
    if not backlink:
        return jsonify({
            'success': False,
            'error': 'Backlink not found'
        }), 404
    
    # Check if user has access to this backlink
    user_website_ids = [w.id for w in g.current_user.websites]
    if (backlink.source_website_id not in user_website_ids and 
        backlink.target_website_id not in user_website_ids):
        return jsonify({
            'success': False,
            'error': 'Access denied'
        }), 403
    
    return jsonify({
        'success': True,
        'data': {
            'backlink': backlink.to_dict()
        }
    })


@bp.route('/<int:backlink_id>/approve', methods=['POST'])
@require_auth
@handle_errors
@log_api_usage
def approve_backlink(backlink_id):
    """Approve a backlink request"""
    backlink = Backlink.query.get(backlink_id)
    
    if not backlink:
        return jsonify({
            'success': False,
            'error': 'Backlink not found'
        }), 404
    
    # Check if user owns the target website (can approve incoming backlinks)
    target_website = Website.query.filter_by(
        id=backlink.target_website_id,
        user_id=g.current_user.id
    ).first()
    
    if not target_website:
        return jsonify({
            'success': False,
            'error': 'You can only approve backlinks to your own websites'
        }), 403
    
    if backlink.status != 'pending':
        return jsonify({
            'success': False,
            'error': f'Cannot approve backlink with status: {backlink.status}'
        }), 400
    
    # Approve the backlink
    backlink.approve()
    
    # Record approval in usage stats
    from app.models.usage_stats import UsageStats
    UsageStats.record_backlink_activity(
        g.current_user.id,
        target_website.id,
        'approved'
    )
    
    return jsonify({
        'success': True,
        'data': {
            'backlink': backlink.to_dict()
        }
    })


@bp.route('/<int:backlink_id>/reject', methods=['POST'])
@require_auth
@handle_errors
@log_api_usage
def reject_backlink(backlink_id):
    """Reject a backlink request"""
    backlink = Backlink.query.get(backlink_id)
    
    if not backlink:
        return jsonify({
            'success': False,
            'error': 'Backlink not found'
        }), 404
    
    # Check if user owns the target website
    target_website = Website.query.filter_by(
        id=backlink.target_website_id,
        user_id=g.current_user.id
    ).first()
    
    if not target_website:
        return jsonify({
            'success': False,
            'error': 'You can only reject backlinks to your own websites'
        }), 403
    
    if backlink.status not in ['pending', 'approved']:
        return jsonify({
            'success': False,
            'error': f'Cannot reject backlink with status: {backlink.status}'
        }), 400
    
    # Reject the backlink
    backlink.reject()
    
    return jsonify({
        'success': True,
        'data': {
            'backlink': backlink.to_dict()
        }
    })


@bp.route('/<int:backlink_id>/activate', methods=['POST'])
@require_auth
@handle_errors
@log_api_usage
def activate_backlink(backlink_id):
    """Mark backlink as active (live on the website)"""
    backlink = Backlink.query.get(backlink_id)
    
    if not backlink:
        return jsonify({
            'success': False,
            'error': 'Backlink not found'
        }), 404
    
    # Check if user owns the source website
    source_website = Website.query.filter_by(
        id=backlink.source_website_id,
        user_id=g.current_user.id
    ).first()
    
    if not source_website:
        return jsonify({
            'success': False,
            'error': 'You can only activate backlinks from your own websites'
        }), 403
    
    if backlink.status != 'approved':
        return jsonify({
            'success': False,
            'error': 'Backlink must be approved before activation'
        }), 400
    
    # Activate the backlink
    backlink.activate()
    
    # Record activation in usage stats
    from app.models.usage_stats import UsageStats
    UsageStats.record_backlink_activity(
        g.current_user.id,
        source_website.id,
        'activated'
    )
    
    return jsonify({
        'success': True,
        'data': {
            'backlink': backlink.to_dict()
        }
    })


@bp.route('/<int:backlink_id>/remove', methods=['POST'])
@require_auth
@handle_errors
@log_api_usage
def remove_backlink(backlink_id):
    """Remove/deactivate a backlink"""
    backlink = Backlink.query.get(backlink_id)
    
    if not backlink:
        return jsonify({
            'success': False,
            'error': 'Backlink not found'
        }), 404
    
    # Check if user owns the source website
    source_website = Website.query.filter_by(
        id=backlink.source_website_id,
        user_id=g.current_user.id
    ).first()
    
    if not source_website:
        return jsonify({
            'success': False,
            'error': 'You can only remove backlinks from your own websites'
        }), 403
    
    # Remove the backlink
    backlink.remove()
    
    return jsonify({
        'success': True,
        'data': {
            'backlink': backlink.to_dict()
        }
    })


@bp.route('/stats', methods=['GET'])
@require_auth
@handle_errors
@log_api_usage
def get_backlink_stats():
    """Get backlink statistics for the user"""
    user_website_ids = [w.id for w in g.current_user.websites]
    
    # Count backlinks by status
    stats = {}
    for status in ['pending', 'approved', 'active', 'removed', 'rejected']:
        inbound_count = Backlink.query.filter(
            Backlink.target_website_id.in_(user_website_ids),
            Backlink.status == status
        ).count()
        
        outbound_count = Backlink.query.filter(
            Backlink.source_website_id.in_(user_website_ids),
            Backlink.status == status
        ).count()
        
        stats[status] = {
            'inbound': inbound_count,
            'outbound': outbound_count,
            'total': inbound_count + outbound_count
        }
    
    # Get monthly usage
    usage = g.current_user.get_usage_this_month()
    plan_limits = g.current_user.get_plan_limits()
    
    return jsonify({
        'success': True,
        'data': {
            'stats_by_status': stats,
            'monthly_usage': {
                'backlinks_created': usage['backlinks_created'],
                'limit': plan_limits['backlinks_per_month'],
                'remaining': max(0, plan_limits['backlinks_per_month'] - usage['backlinks_created'])
            }
        }
    })
