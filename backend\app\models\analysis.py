"""
Content Analysis model for LinkUp Plugin
"""
from app import db
from datetime import datetime
import json


class ContentAnalysis(db.Model):
    """Content analysis model for storing AI analysis results"""
    
    __tablename__ = 'content_analyses'
    
    # Primary key
    id = db.Column(db.Integer, primary_key=True)
    
    # Foreign keys
    website_id = db.Column(db.In<PERSON>ger, db.ForeignKey('websites.id'), nullable=False)
    
    # Content identification
    content_hash = db.Column(db.String(64), nullable=False, index=True)
    
    # Analysis results (JSON fields)
    keywords = db.Column(db.JSON)  # Extracted keywords and phrases
    categories = db.Column(db.JSON)  # Topic classifications
    analysis_data = db.Column(db.JSON)  # Full analysis results
    
    # Key metrics
    quality_score = db.Column(db.Float)  # Overall content quality (0-10)
    readability_score = db.Column(db.Float)  # Flesch reading ease score
    
    # Analysis metadata
    analysis_version = db.Column(db.String(20), default='1.0')
    analyzed_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    # Processing status
    status = db.Column(db.String(50), default='completed')  # completed, failed, processing
    error_message = db.Column(db.Text)

    # Relationships
    website = db.relationship('Website', back_populates='analyses')

    def __repr__(self):
        return f'<ContentAnalysis {self.id} for Website {self.website_id}>'
    
    def to_dict(self):
        """Convert analysis to dictionary"""
        return {
            'id': self.id,
            'website_id': self.website_id,
            'content_hash': self.content_hash,
            'keywords': self.keywords,
            'categories': self.categories,
            'analysis_data': self.analysis_data,
            'quality_score': self.quality_score,
            'readability_score': self.readability_score,
            'analysis_version': self.analysis_version,
            'analyzed_at': self.analyzed_at.isoformat() if self.analyzed_at else None,
            'status': self.status,
            'error_message': self.error_message
        }
    
    def get_primary_keywords(self, limit=10):
        """Get primary keywords from analysis"""
        if not self.keywords or 'primary_keywords' not in self.keywords:
            return []
        
        return self.keywords['primary_keywords'][:limit]
    
    def get_primary_topics(self, limit=5):
        """Get primary topics from analysis"""
        if not self.categories or 'primary_topics' not in self.categories:
            return []
        
        return self.categories['primary_topics'][:limit]
    
    def get_content_metrics(self):
        """Get basic content metrics"""
        if not self.analysis_data or 'basic_metrics' not in self.analysis_data:
            return {}
        
        return self.analysis_data['basic_metrics']
    
    def get_improvement_suggestions(self):
        """Get improvement suggestions from analysis"""
        if not self.analysis_data or 'improvement_suggestions' not in self.analysis_data:
            return []
        
        return self.analysis_data['improvement_suggestions']
    
    @classmethod
    def get_latest_for_website(cls, website_id):
        """Get latest analysis for a website"""
        return cls.query.filter_by(
            website_id=website_id
        ).order_by(cls.analyzed_at.desc()).first()
    
    @classmethod
    def get_by_content_hash(cls, content_hash):
        """Get analysis by content hash"""
        return cls.query.filter_by(content_hash=content_hash).first()
    
    @classmethod
    def get_website_history(cls, website_id, limit=10):
        """Get analysis history for a website"""
        return cls.query.filter_by(
            website_id=website_id
        ).order_by(cls.analyzed_at.desc()).limit(limit).all()
    
    def update_status(self, status, error_message=None):
        """Update analysis status"""
        self.status = status
        if error_message:
            self.error_message = error_message
        
        db.session.commit()
    
    def is_recent(self, hours=24):
        """Check if analysis is recent"""
        if not self.analyzed_at:
            return False
        
        time_diff = datetime.utcnow() - self.analyzed_at
        return time_diff.total_seconds() < (hours * 3600)
