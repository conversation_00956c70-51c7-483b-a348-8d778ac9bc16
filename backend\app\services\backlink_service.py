"""
Backlink Management Service for LinkUp Plugin
"""
from app import db
from app.models.backlink import Backlink
from app.models.website import Website
from app.models.user import User
from app.models.usage_stats import UsageStats
from app.services.matching_service import MatchingService
from app.services.content_analysis_service import ContentAnalysisService
from datetime import datetime, timedelta
import logging
import random

logger = logging.getLogger(__name__)


class BacklinkService:
    """Service for managing backlink operations"""
    
    @staticmethod
    def create_backlink_request(source_website_id, target_website_id, request_data):
        """Create a new backlink request with validation"""
        try:
            # Validate websites exist
            source_website = Website.query.get(source_website_id)
            target_website = Website.query.get(target_website_id)
            
            if not source_website or not target_website:
                return None, 'Website not found'
            
            # Check if user can create more backlinks
            if not source_website.user.can_create_backlink():
                return None, 'Monthly backlink limit exceeded'
            
            # Prevent self-linking
            if source_website_id == target_website_id:
                return None, 'Cannot create backlink to the same website'
            
            # Check for existing backlink
            existing = Backlink.query.filter_by(
                source_website_id=source_website_id,
                target_website_id=target_website_id,
                source_url=request_data['source_url'],
                target_url=request_data['target_url']
            ).first()
            
            if existing:
                return None, 'Backlink already exists'
            
            # Create backlink
            backlink = Backlink(
                source_website_id=source_website_id,
                target_website_id=target_website_id,
                source_url=request_data['source_url'],
                target_url=request_data['target_url'],
                anchor_text=request_data['anchor_text'],
                context_before=request_data.get('context_before', ''),
                context_after=request_data.get('context_after', ''),
                status='pending'
            )
            
            # Calculate quality scores
            backlink.relevance_score = BacklinkService._calculate_relevance_score(
                source_website, target_website, request_data
            )
            backlink.quality_score = BacklinkService._calculate_quality_score(
                source_website, target_website, request_data
            )
            backlink.link_juice_value = BacklinkService._estimate_link_value(
                source_website, target_website
            )
            
            db.session.add(backlink)
            db.session.commit()
            
            # Record usage
            UsageStats.record_backlink_activity(
                source_website.user_id,
                source_website_id,
                'created'
            )
            
            # Send notification to target website owner
            BacklinkService._send_backlink_notification(backlink, 'request')
            
            return backlink, None
            
        except Exception as e:
            logger.error(f"Error creating backlink request: {str(e)}")
            db.session.rollback()
            return None, 'Failed to create backlink request'
    
    @staticmethod
    def approve_backlink(backlink_id, user_id):
        """Approve a backlink request"""
        try:
            backlink = Backlink.query.get(backlink_id)
            if not backlink:
                return None, 'Backlink not found'
            
            # Verify user owns target website
            if backlink.target_website.user_id != user_id:
                return None, 'Unauthorized'
            
            if backlink.status != 'pending':
                return None, f'Cannot approve backlink with status: {backlink.status}'
            
            # Approve backlink
            backlink.approve()
            
            # Record usage
            UsageStats.record_backlink_activity(
                user_id,
                backlink.target_website_id,
                'approved'
            )
            
            # Send notification to source website owner
            BacklinkService._send_backlink_notification(backlink, 'approved')
            
            # Check for reciprocal opportunity
            BacklinkService._suggest_reciprocal_backlink(backlink)
            
            return backlink, None
            
        except Exception as e:
            logger.error(f"Error approving backlink: {str(e)}")
            return None, 'Failed to approve backlink'
    
    @staticmethod
    def activate_backlink(backlink_id, user_id):
        """Mark backlink as active (live on website)"""
        try:
            backlink = Backlink.query.get(backlink_id)
            if not backlink:
                return None, 'Backlink not found'
            
            # Verify user owns source website
            if backlink.source_website.user_id != user_id:
                return None, 'Unauthorized'
            
            if backlink.status != 'approved':
                return None, 'Backlink must be approved before activation'
            
            # Activate backlink
            backlink.activate()
            
            # Record usage
            UsageStats.record_backlink_activity(
                user_id,
                backlink.source_website_id,
                'activated'
            )
            
            # Send notification to target website owner
            BacklinkService._send_backlink_notification(backlink, 'activated')
            
            # Schedule quality monitoring
            BacklinkService._schedule_quality_monitoring(backlink)
            
            return backlink, None
            
        except Exception as e:
            logger.error(f"Error activating backlink: {str(e)}")
            return None, 'Failed to activate backlink'
    
    @staticmethod
    def get_backlink_opportunities(website_id, limit=10):
        """Get potential backlink opportunities for a website"""
        try:
            website = Website.query.get(website_id)
            if not website:
                return []
            
            # Use matching service to find opportunities
            matches = MatchingService.find_matches(website, limit=limit*2)
            
            opportunities = []
            for match in matches[:limit]:
                partner = match['partner_website']
                
                # Check if backlink already exists
                existing = Backlink.query.filter_by(
                    source_website_id=partner.id,
                    target_website_id=website_id
                ).first()
                
                if not existing:
                    opportunity = {
                        'partner_website': partner.to_dict(),
                        'match_score': match['match_score'],
                        'estimated_value': match['estimated_value'],
                        'reasons': match['reasons'],
                        'suggested_anchor_texts': BacklinkService._suggest_anchor_texts(
                            website, partner
                        )
                    }
                    opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"Error getting backlink opportunities: {str(e)}")
            return []
    
    @staticmethod
    def get_backlink_analytics(website_id, days=30):
        """Get backlink analytics for a website"""
        try:
            website = Website.query.get(website_id)
            if not website:
                return None
            
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # Get backlinks created in date range
            inbound_backlinks = Backlink.query.filter(
                Backlink.target_website_id == website_id,
                Backlink.created_at >= start_date
            ).all()
            
            outbound_backlinks = Backlink.query.filter(
                Backlink.source_website_id == website_id,
                Backlink.created_at >= start_date
            ).all()
            
            # Calculate metrics
            analytics = {
                'summary': {
                    'total_inbound': len(inbound_backlinks),
                    'total_outbound': len(outbound_backlinks),
                    'active_inbound': len([b for b in inbound_backlinks if b.status == 'active']),
                    'active_outbound': len([b for b in outbound_backlinks if b.status == 'active']),
                    'pending_inbound': len([b for b in inbound_backlinks if b.status == 'pending']),
                    'pending_outbound': len([b for b in outbound_backlinks if b.status == 'pending'])
                },
                'quality_metrics': {
                    'avg_relevance_score': BacklinkService._calculate_avg_score(
                        inbound_backlinks, 'relevance_score'
                    ),
                    'avg_quality_score': BacklinkService._calculate_avg_score(
                        inbound_backlinks, 'quality_score'
                    ),
                    'total_link_value': sum(b.link_juice_value or 0 for b in inbound_backlinks)
                },
                'growth_trend': BacklinkService._calculate_growth_trend(
                    website_id, days
                ),
                'top_referring_domains': BacklinkService._get_top_referring_domains(
                    inbound_backlinks
                )
            }
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error getting backlink analytics: {str(e)}")
            return None
    
    @staticmethod
    def _calculate_relevance_score(source_website, target_website, request_data):
        """Calculate relevance score for a backlink"""
        score = 5.0  # Base score
        
        # Category match
        if source_website.category == target_website.category:
            score += 2.0
        
        # Language match
        if source_website.language == target_website.language:
            score += 1.0
        
        # Anchor text relevance (simplified)
        anchor_text = request_data.get('anchor_text', '').lower()
        if target_website.title and any(word in anchor_text for word in target_website.title.lower().split()):
            score += 1.5
        
        # Context relevance
        context = (request_data.get('context_before', '') + ' ' + 
                  request_data.get('context_after', '')).lower()
        if target_website.description and any(word in context for word in target_website.description.lower().split()[:5]):
            score += 1.0
        
        return min(score, 10.0)
    
    @staticmethod
    def _calculate_quality_score(source_website, target_website, request_data):
        """Calculate quality score for a backlink"""
        score = 5.0  # Base score
        
        # Source website authority
        score += (source_website.domain_authority or 0) / 10.0
        
        # Target website quality
        score += (target_website.domain_authority or 0) / 20.0
        
        # Anchor text quality
        anchor_text = request_data.get('anchor_text', '')
        if len(anchor_text) > 5 and not anchor_text.lower() in ['click here', 'read more', 'link']:
            score += 1.0
        
        # Context quality
        context_length = len(request_data.get('context_before', '') + request_data.get('context_after', ''))
        if context_length > 50:
            score += 0.5
        
        return min(score, 10.0)
    
    @staticmethod
    def _estimate_link_value(source_website, target_website):
        """Estimate SEO value of the backlink"""
        base_value = 1.0
        
        # Source website authority factor
        authority_factor = (source_website.domain_authority or 10) / 10.0
        
        # Category relevance factor
        relevance_factor = 1.5 if source_website.category == target_website.category else 1.0
        
        # Calculate estimated value
        estimated_value = base_value * authority_factor * relevance_factor
        
        return round(estimated_value, 2)
    
    @staticmethod
    def _send_backlink_notification(backlink, notification_type):
        """Send notification about backlink status change"""
        # This would integrate with email/notification service
        logger.info(f"Backlink notification: {notification_type} for backlink {backlink.id}")
        # TODO: Implement actual notification sending
    
    @staticmethod
    def _suggest_reciprocal_backlink(backlink):
        """Suggest reciprocal backlink opportunity"""
        # Check if reciprocal backlink would be beneficial
        source_website = backlink.source_website
        target_website = backlink.target_website
        
        # Simple reciprocal suggestion logic
        if (source_website.domain_authority or 0) >= (target_website.domain_authority or 0) * 0.8:
            logger.info(f"Reciprocal backlink opportunity: {target_website.domain} -> {source_website.domain}")
            # TODO: Create reciprocal suggestion
    
    @staticmethod
    def _schedule_quality_monitoring(backlink):
        """Schedule quality monitoring for active backlink"""
        # This would schedule periodic checks of the backlink
        logger.info(f"Scheduled quality monitoring for backlink {backlink.id}")
        # TODO: Implement actual monitoring scheduling
    
    @staticmethod
    def _suggest_anchor_texts(website, partner):
        """Suggest appropriate anchor texts for backlink"""
        suggestions = []
        
        # Brand name
        if website.title:
            suggestions.append(website.title)
        
        # Domain name
        suggestions.append(website.domain)
        
        # Category-based suggestions
        category_anchors = {
            'technology': ['tech solution', 'technology platform', 'innovative tool'],
            'business': ['business solution', 'professional service', 'business tool'],
            'health': ['health resource', 'wellness guide', 'health information'],
            'education': ['educational resource', 'learning platform', 'study guide']
        }
        
        if website.category in category_anchors:
            suggestions.extend(category_anchors[website.category])
        
        return suggestions[:5]
    
    @staticmethod
    def _calculate_avg_score(backlinks, score_field):
        """Calculate average score for a list of backlinks"""
        if not backlinks:
            return 0.0
        
        scores = [getattr(b, score_field) or 0 for b in backlinks]
        return round(sum(scores) / len(scores), 2)
    
    @staticmethod
    def _calculate_growth_trend(website_id, days):
        """Calculate backlink growth trend"""
        # Simplified growth calculation
        end_date = datetime.utcnow()
        mid_date = end_date - timedelta(days=days//2)
        start_date = end_date - timedelta(days=days)
        
        recent_count = Backlink.query.filter(
            Backlink.target_website_id == website_id,
            Backlink.created_at >= mid_date,
            Backlink.status == 'active'
        ).count()
        
        older_count = Backlink.query.filter(
            Backlink.target_website_id == website_id,
            Backlink.created_at >= start_date,
            Backlink.created_at < mid_date,
            Backlink.status == 'active'
        ).count()
        
        if older_count == 0:
            return 100.0 if recent_count > 0 else 0.0
        
        growth_rate = ((recent_count - older_count) / older_count) * 100
        return round(growth_rate, 2)
    
    @staticmethod
    def _get_top_referring_domains(backlinks):
        """Get top referring domains from backlinks"""
        domain_counts = {}
        
        for backlink in backlinks:
            if backlink.status == 'active':
                domain = backlink.source_website.domain
                domain_counts[domain] = domain_counts.get(domain, 0) + 1
        
        # Sort by count and return top 5
        sorted_domains = sorted(domain_counts.items(), key=lambda x: x[1], reverse=True)
        return [{'domain': domain, 'count': count} for domain, count in sorted_domains[:5]]
