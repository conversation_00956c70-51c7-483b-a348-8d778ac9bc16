"""
Intelligent Backlink Matching Service
Uses AI and machine learning to find relevant backlink partners
"""
import logging
import re
from typing import Dict, List, Tuple, Optional, Set
from datetime import datetime, timedelta
from collections import defaultdict

# Handle optional dependencies with fallbacks
try:
    import numpy as np
    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False
    # Create a simple fallback for basic numpy operations
    class NumpyFallback:
        @staticmethod
        def array(data):
            return data
        @staticmethod
        def dot(a, b):
            return sum(x * y for x, y in zip(a, b))
        @staticmethod
        def linalg_norm(a):
            return (sum(x * x for x in a)) ** 0.5
    np = NumpyFallback()

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.preprocessing import StandardScaler
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False
    # Create fallback classes
    class TfidfVectorizerFallback:
        def __init__(self, **kwargs):
            pass
        def fit_transform(self, texts):
            # Simple word count fallback
            return [[len(text.split()) for text in texts]]

    class StandardScalerFallback:
        def fit_transform(self, data):
            return data
        def transform(self, data):
            return data

    def cosine_similarity_fallback(a, b):
        # Simple fallback using basic similarity
        return [[0.5]]  # Return neutral similarity

    TfidfVectorizer = TfidfVectorizerFallback
    StandardScaler = StandardScalerFallback
    cosine_similarity = cosine_similarity_fallback

try:
    from sqlalchemy import and_, or_, func
    HAS_SQLALCHEMY = True
except ImportError:
    HAS_SQLALCHEMY = False
    # This is critical, so we'll need to handle it in the app initialization
    logger = logging.getLogger(__name__)
    logger.error("SQLAlchemy is required but not installed. Please install it with: pip install sqlalchemy")

from app.models.website import Website
from app.models.backlink import Backlink
from app.models.analysis import ContentAnalysis
from app import db, cache
from app.services.advanced_content_matching import AdvancedContentMatcher
from app.services.niche_matching_service import NicheMatchingService
from app.services.quality_assessment_service import QualityAssessmentService
from app.services.filter_management_service import FilterManagementService
from app.services.preferences_service import PreferencesService
from app.database.matching_optimization import MatchingQueryOptimizer

logger = logging.getLogger(__name__)


class MatchingService:
    """Intelligent backlink partner matching service"""

    def __init__(self):
        """Initialize the matching service"""
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=500,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=1,
            max_df=0.8
        )

        self.scaler = StandardScaler()

        # Enhanced matching criteria weights
        self.weights = {
            'content_similarity': 0.25,
            'category_match': 0.15,
            'quality_score': 0.15,
            'domain_authority': 0.12,
            'language_match': 0.08,
            'freshness': 0.05,
            'mutual_benefit': 0.08,
            'traffic_compatibility': 0.07,
            'niche_authority': 0.05
        }

        # Enhanced quality thresholds
        self.thresholds = {
            'min_quality_score': 6.0,
            'min_domain_authority': 20,
            'min_content_similarity': 0.3,
            'max_existing_backlinks': 5,
            'min_days_between_matches': 7,
            'min_niche_authority': 0.4,
            'max_spam_score': 0.3,
            'min_trust_signals': 3
        }

        # Blacklist/Whitelist storage
        self.global_blacklist = set()
        self.global_whitelist = set()
        self.load_blacklist_whitelist()

        # Advanced content matching
        self.advanced_matcher = AdvancedContentMatcher()

        # Niche matching service
        self.niche_matcher = NicheMatchingService()

        # Quality assessment service
        self.quality_assessor = QualityAssessmentService()

        # Filter management service
        self.filter_manager = FilterManagementService()

        # Preferences service
        self.preferences_service = PreferencesService()

        # Database query optimizer
        self.query_optimizer = MatchingQueryOptimizer()

    def load_blacklist_whitelist(self):
        """Load blacklist and whitelist from database or cache"""
        try:
            # Try to load from cache first
            cached_blacklist = cache.get('global_blacklist')
            cached_whitelist = cache.get('global_whitelist')

            if cached_blacklist:
                self.global_blacklist = set(cached_blacklist)
            if cached_whitelist:
                self.global_whitelist = set(cached_whitelist)

            # If not in cache, load from database (implementation depends on your settings model)
            # For now, use some default spam domains
            if not self.global_blacklist:
                self.global_blacklist = {
                    'spam-site.com', 'low-quality-links.net', 'link-farm.org',
                    'fake-authority.com', 'content-scraper.info'
                }
                cache.set('global_blacklist', list(self.global_blacklist), timeout=3600)

        except Exception as e:
            logger.warning(f"Failed to load blacklist/whitelist: {str(e)}")
            self.global_blacklist = set()
            self.global_whitelist = set()

    def is_domain_blacklisted(self, domain: str) -> bool:
        """Check if a domain is blacklisted"""
        if not domain:
            return True

        # Check exact domain match
        if domain.lower() in self.global_blacklist:
            return True

        # Check for subdomain matches
        domain_parts = domain.lower().split('.')
        for i in range(len(domain_parts)):
            partial_domain = '.'.join(domain_parts[i:])
            if partial_domain in self.global_blacklist:
                return True

        return False

    def is_domain_whitelisted(self, domain: str) -> bool:
        """Check if a domain is whitelisted"""
        if not domain:
            return False

        return domain.lower() in self.global_whitelist
    
    def find_matches(self, website_id: int, limit: int = 20) -> List[Dict]:
        """
        Find potential backlink partners for a website
        
        Args:
            website_id: ID of the website to find matches for
            limit: Maximum number of matches to return
            
        Returns:
            List of potential matches with scores
        """
        try:
            # Get the source website
            source_website = Website.query.get(website_id)
            if not source_website:
                return []
            
            # Get source website analysis
            source_analysis = self._get_latest_analysis(website_id)
            if not source_analysis:
                logger.warning(f"No analysis found for website {website_id}")
                return []
            
            # Get potential partner websites
            potential_partners = self._get_potential_partners(source_website)
            
            if not potential_partners:
                return []
            
            # Calculate match scores
            matches = []
            for partner in potential_partners:
                partner_analysis = self._get_latest_analysis(partner.id)
                if not partner_analysis:
                    continue
                
                match_score = self._calculate_match_score(
                    source_website, source_analysis,
                    partner, partner_analysis
                )
                
                if match_score['total_score'] >= 0.6:  # Minimum threshold
                    matches.append({
                        'partner_website': partner,
                        'match_score': match_score,
                        'reasons': self._generate_match_reasons(match_score),
                        'estimated_value': self._estimate_match_value(match_score),
                        'mutual_benefit': match_score['mutual_benefit_score']
                    })
            
            # Sort by total score and return top matches
            matches.sort(key=lambda x: x['match_score']['total_score'], reverse=True)
            return matches[:limit]
            
        except Exception as e:
            logger.error(f"Error finding matches for website {website_id}: {str(e)}")
            return []
    
    def _get_potential_partners(self, source_website: Website) -> List[Website]:
        """Get list of potential partner websites with enhanced filtering"""
        # Exclude websites that already have backlinks with source
        existing_partners = db.session.query(Backlink.target_website_id).filter(
            Backlink.source_website_id == source_website.id
        ).subquery()

        # Also exclude reverse relationships
        existing_reverse_partners = db.session.query(Backlink.source_website_id).filter(
            Backlink.target_website_id == source_website.id
        ).subquery()

        # Get potential partners with basic filtering
        potential_partners = Website.query.filter(
            and_(
                Website.id != source_website.id,  # Not the same website
                Website.status == 'active',  # Active websites only
                Website.user_id != source_website.user_id,  # Different users
                ~Website.id.in_(existing_partners),  # No existing backlinks
                ~Website.id.in_(existing_reverse_partners),  # No reverse backlinks
                Website.domain_authority >= self.thresholds['min_domain_authority']  # Minimum DA
            )
        ).limit(200).all()  # Increased limit for better filtering

        # Apply advanced filtering using filter management service
        filtered_partners = []
        for partner in potential_partners:
            if partner.domain:
                # Check domain filtering
                is_filtered, filter_type, reason = self.filter_manager.is_domain_filtered(
                    partner.domain,
                    user_id=source_website.user_id
                )

                if is_filtered and filter_type == 'blacklist':
                    continue  # Skip blacklisted domains

                # Auto-detect spam
                spam_result = self.filter_manager.auto_detect_spam(partner)
                if spam_result['is_spam']:
                    continue  # Skip auto-detected spam

                # Prioritize whitelisted domains
                if is_filtered and filter_type == 'whitelist':
                    filtered_partners.insert(0, partner)  # Add to front
                else:
                    filtered_partners.append(partner)

        return filtered_partners[:100]  # Return top 100 after filtering
    
    def _get_latest_analysis(self, website_id: int) -> Optional[ContentAnalysis]:
        """Get the latest content analysis for a website using optimized caching"""
        return self.query_optimizer.get_cached_latest_analysis(website_id)
    
    def _calculate_match_score(self, source_website: Website, source_analysis: ContentAnalysis,
                              partner_website: Website, partner_analysis: ContentAnalysis) -> Dict:
        """Calculate comprehensive match score between two websites"""
        
        scores = {}
        
        # 1. Content Similarity Score
        scores['content_similarity'] = self._calculate_content_similarity(
            source_analysis, partner_analysis
        )
        
        # 2. Category Match Score
        scores['category_match'] = self._calculate_category_match(
            source_website, partner_website
        )
        
        # 3. Quality Score
        scores['quality_score'] = self._calculate_quality_compatibility(
            source_analysis, partner_analysis
        )
        
        # 4. Domain Authority Score
        scores['domain_authority'] = self._calculate_authority_score(
            source_website, partner_website
        )
        
        # 5. Language Match Score
        scores['language_match'] = self._calculate_language_match(
            source_website, partner_website
        )
        
        # 6. Content Freshness Score
        scores['freshness'] = self._calculate_freshness_score(
            source_analysis, partner_analysis
        )
        
        # 7. Mutual Benefit Score
        scores['mutual_benefit_score'] = self._calculate_mutual_benefit(
            source_website, source_analysis, partner_website, partner_analysis
        )

        # 8. Traffic Compatibility Score
        scores['traffic_compatibility'] = self._calculate_traffic_compatibility(
            source_website, partner_website
        )

        # 9. Niche Authority Score
        scores['niche_authority'] = self._calculate_niche_authority(
            partner_website, partner_analysis
        )

        # Calculate weighted total score
        total_score = sum([
            scores[key] * self.weights[key]
            for key in self.weights.keys()
            if key in scores
        ])

        scores['total_score'] = min(1.0, max(0.0, total_score))

        return scores
    
    def _calculate_content_similarity(self, source_analysis: ContentAnalysis,
                                    partner_analysis: ContentAnalysis) -> float:
        """Calculate advanced content similarity using multiple methods"""
        try:
            # Use advanced content matching for better similarity calculation
            advanced_results = self.advanced_matcher.calculate_advanced_similarity(
                source_analysis, partner_analysis
            )

            # Return the overall similarity score
            return advanced_results.get('overall_similarity', 0.0)

        except Exception as e:
            logger.warning(f"Advanced content similarity calculation failed, falling back to basic: {str(e)}")

            # Fallback to basic TF-IDF similarity
            try:
                # Get keywords from both analyses
                source_keywords = source_analysis.keywords.get('primary_keywords', []) if source_analysis.keywords else []
                partner_keywords = partner_analysis.keywords.get('primary_keywords', []) if partner_analysis.keywords else []

                if not source_keywords or not partner_keywords:
                    return 0.0

                # Extract keyword text
                source_text = ' '.join([kw.get('keyword', '') for kw in source_keywords])
                partner_text = ' '.join([kw.get('keyword', '') for kw in partner_keywords])

                if not source_text or not partner_text:
                    return 0.0

                # Calculate TF-IDF similarity
                tfidf_matrix = self.tfidf_vectorizer.fit_transform([source_text, partner_text])
                similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]

                return float(similarity)

            except Exception as fallback_error:
                logger.warning(f"Fallback content similarity calculation failed: {str(fallback_error)}")
                return 0.0
    
    def _calculate_category_match(self, source_website: Website, partner_website: Website) -> float:
        """Calculate advanced category compatibility score using niche matching"""
        try:
            if not source_website.category or not partner_website.category:
                return 0.5  # Neutral score if categories unknown

            # Use advanced niche matching for better compatibility scoring
            compatibility = self.niche_matcher.calculate_niche_compatibility(
                source_website.category, partner_website.category
            )

            return compatibility.get('total_score', 0.3)

        except Exception as e:
            logger.warning(f"Advanced category matching failed, using fallback: {str(e)}")

            # Fallback to simple category matching
            if not source_website.category or not partner_website.category:
                return 0.5

            # Exact match
            if source_website.category == partner_website.category:
                return 1.0

            # Related categories (simplified mapping)
            related_categories = {
                'technology': ['business', 'education'],
                'business': ['technology', 'finance'],
                'health': ['lifestyle', 'fitness'],
                'education': ['technology', 'business'],
                'lifestyle': ['health', 'travel', 'food'],
                'finance': ['business', 'technology']
            }

            source_related = related_categories.get(source_website.category, [])
            if partner_website.category in source_related:
                return 0.7

            return 0.3  # Different categories
    
    def _calculate_quality_compatibility(self, source_analysis: ContentAnalysis,
                                       partner_analysis: ContentAnalysis) -> float:
        """Calculate advanced quality compatibility between websites"""
        try:
            # Get websites from analyses (simplified - in production would have better relationship)
            source_website = Website.query.filter_by(id=source_analysis.website_id).first()
            partner_website = Website.query.filter_by(id=partner_analysis.website_id).first()

            if not source_website or not partner_website:
                # Fallback to basic quality comparison
                return self._calculate_basic_quality_compatibility(source_analysis, partner_analysis)

            # Use advanced quality assessment
            source_quality_metrics = self.quality_assessor.assess_website_quality(source_website, source_analysis)
            partner_quality_metrics = self.quality_assessor.assess_website_quality(partner_website, partner_analysis)

            # Both should meet minimum quality threshold
            if (source_quality_metrics.overall_score < self.thresholds['min_quality_score'] or
                partner_quality_metrics.overall_score < self.thresholds['min_quality_score']):
                return 0.0

            # Check for red flags
            if (len(source_quality_metrics.red_flags) > 3 or
                len(partner_quality_metrics.red_flags) > 3):
                return 0.2  # Low compatibility if many red flags

            # Calculate compatibility based on quality difference
            quality_diff = abs(source_quality_metrics.overall_score - partner_quality_metrics.overall_score)

            # Prefer similar quality levels
            if quality_diff <= 1.0:
                compatibility = 1.0
            elif quality_diff <= 2.0:
                compatibility = 0.8
            elif quality_diff <= 3.0:
                compatibility = 0.6
            else:
                compatibility = 0.4

            # Bonus for high-quality sites
            if (source_quality_metrics.overall_score >= 8.0 and
                partner_quality_metrics.overall_score >= 8.0):
                compatibility = min(1.0, compatibility + 0.1)

            return compatibility

        except Exception as e:
            logger.warning(f"Advanced quality compatibility calculation failed: {str(e)}")
            return self._calculate_basic_quality_compatibility(source_analysis, partner_analysis)

    def _calculate_basic_quality_compatibility(self, source_analysis: ContentAnalysis,
                                             partner_analysis: ContentAnalysis) -> float:
        """Fallback basic quality compatibility calculation"""
        source_quality = source_analysis.quality_score or 0
        partner_quality = partner_analysis.quality_score or 0

        # Both should meet minimum quality threshold
        if source_quality < self.thresholds['min_quality_score'] or \
           partner_quality < self.thresholds['min_quality_score']:
            return 0.0

        # Calculate compatibility based on quality difference
        quality_diff = abs(source_quality - partner_quality)

        # Prefer similar quality levels
        if quality_diff <= 1.0:
            return 1.0
        elif quality_diff <= 2.0:
            return 0.8
        elif quality_diff <= 3.0:
            return 0.6
        else:
            return 0.4
    
    def _calculate_authority_score(self, source_website: Website, partner_website: Website) -> float:
        """Calculate domain authority compatibility"""
        source_da = getattr(source_website, 'domain_authority', 0) or 0
        partner_da = getattr(partner_website, 'domain_authority', 0) or 0
        
        # Both should meet minimum DA threshold
        if partner_da < self.thresholds['min_domain_authority']:
            return 0.0
        
        # Prefer partners with similar or higher DA
        if partner_da >= source_da:
            return min(1.0, partner_da / 100)  # Normalize to 0-1
        else:
            # Penalty for lower DA partners
            da_ratio = partner_da / max(source_da, 1)
            return max(0.3, da_ratio)
    
    def _calculate_language_match(self, source_website: Website, partner_website: Website) -> float:
        """Calculate language compatibility"""
        source_lang = getattr(source_website, 'language', 'en') or 'en'
        partner_lang = getattr(partner_website, 'language', 'en') or 'en'
        
        return 1.0 if source_lang == partner_lang else 0.3
    
    def _calculate_freshness_score(self, source_analysis: ContentAnalysis,
                                 partner_analysis: ContentAnalysis) -> float:
        """Calculate content freshness score"""
        now = datetime.utcnow()
        
        # Check how recent the analyses are
        source_age = (now - source_analysis.analyzed_at).days if source_analysis.analyzed_at else 365
        partner_age = (now - partner_analysis.analyzed_at).days if partner_analysis.analyzed_at else 365
        
        # Prefer recent analyses
        avg_age = (source_age + partner_age) / 2
        
        if avg_age <= 7:
            return 1.0
        elif avg_age <= 30:
            return 0.8
        elif avg_age <= 90:
            return 0.6
        else:
            return 0.4
    
    def _calculate_mutual_benefit(self, source_website: Website, source_analysis: ContentAnalysis,
                                partner_website: Website, partner_analysis: ContentAnalysis) -> float:
        """Calculate mutual benefit score"""
        # Check if both websites would benefit from the exchange
        
        # Factor 1: Complementary content
        source_topics = source_analysis.topics.get('primary_topics', []) if source_analysis.topics else []
        partner_topics = partner_analysis.topics.get('primary_topics', []) if partner_analysis.topics else []
        
        # Look for complementary but not identical topics
        topic_overlap = 0
        topic_complement = 0
        
        for source_topic in source_topics:
            for partner_topic in partner_topics:
                if source_topic.get('topic') == partner_topic.get('topic'):
                    topic_overlap += 1
                elif self._are_topics_complementary(source_topic.get('topic'), partner_topic.get('topic')):
                    topic_complement += 1
        
        # Prefer some overlap but not complete overlap
        if topic_overlap > 0 and topic_complement > 0:
            mutual_score = 0.9
        elif topic_complement > 0:
            mutual_score = 0.8
        elif topic_overlap > 0:
            mutual_score = 0.6
        else:
            mutual_score = 0.4
        
        # Factor 2: Audience compatibility
        # (This would require audience data - simplified for now)
        audience_score = 0.7  # Assume moderate compatibility
        
        return (mutual_score + audience_score) / 2
    
    def _are_topics_complementary(self, topic1: str, topic2: str) -> bool:
        """Check if two topics are complementary"""
        complementary_pairs = {
            'technology': ['business', 'education'],
            'business': ['technology', 'finance', 'marketing'],
            'health': ['fitness', 'nutrition', 'lifestyle'],
            'education': ['technology', 'business'],
            'finance': ['business', 'investment'],
            'marketing': ['business', 'technology']
        }
        
        return topic2 in complementary_pairs.get(topic1, [])
    
    def _generate_match_reasons(self, match_score: Dict) -> List[str]:
        """Generate human-readable reasons for the match"""
        reasons = []
        
        if match_score.get('content_similarity', 0) > 0.7:
            reasons.append("High content similarity")
        elif match_score.get('content_similarity', 0) > 0.5:
            reasons.append("Good content relevance")
        
        if match_score.get('category_match', 0) == 1.0:
            reasons.append("Same category")
        elif match_score.get('category_match', 0) > 0.6:
            reasons.append("Related categories")
        
        if match_score.get('quality_score', 0) > 0.8:
            reasons.append("Similar content quality")
        
        if match_score.get('domain_authority', 0) > 0.8:
            reasons.append("Good domain authority")
        
        if match_score.get('mutual_benefit_score', 0) > 0.7:
            reasons.append("Mutual benefit potential")
        
        if match_score.get('language_match', 0) == 1.0:
            reasons.append("Same language")
        
        if not reasons:
            reasons.append("Basic compatibility")
        
        return reasons
    
    def _estimate_match_value(self, match_score: Dict) -> float:
        """Estimate the value of this match (0-10 scale)"""
        total_score = match_score.get('total_score', 0)
        
        # Convert to 0-10 scale
        value = total_score * 10
        
        # Bonus for high-quality matches
        if match_score.get('content_similarity', 0) > 0.8:
            value += 0.5
        
        if match_score.get('mutual_benefit_score', 0) > 0.8:
            value += 0.5
        
        return min(10.0, round(value, 1))
    
    def get_match_statistics(self, website_id: int) -> Dict:
        """Get matching statistics for a website"""
        try:
            website = Website.query.get(website_id)
            if not website:
                return {}
            
            # Get all potential matches
            matches = self.find_matches(website_id, limit=100)
            
            if not matches:
                return {
                    'total_potential_matches': 0,
                    'high_quality_matches': 0,
                    'avg_match_score': 0,
                    'top_categories': [],
                    'recommendations': ['No potential matches found. Consider improving content quality.']
                }
            
            # Calculate statistics
            total_matches = len(matches)
            high_quality_matches = len([m for m in matches if m['match_score']['total_score'] > 0.8])
            avg_score = sum([m['match_score']['total_score'] for m in matches]) / total_matches
            
            # Get top categories
            category_counts = {}
            for match in matches:
                category = match['partner_website'].category
                if category:
                    category_counts[category] = category_counts.get(category, 0) + 1
            
            top_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            # Generate recommendations
            recommendations = self._generate_recommendations(matches, website)
            
            return {
                'total_potential_matches': total_matches,
                'high_quality_matches': high_quality_matches,
                'avg_match_score': round(avg_score, 3),
                'top_categories': [{'category': cat, 'count': count} for cat, count in top_categories],
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"Error getting match statistics for website {website_id}: {str(e)}")
            return {}
    
    def _generate_recommendations(self, matches: List[Dict], website: Website) -> List[str]:
        """Generate recommendations to improve matching"""
        recommendations = []
        
        if not matches:
            recommendations.append("Improve content quality to attract better partners")
            recommendations.append("Add more detailed content analysis")
            return recommendations
        
        avg_score = sum([m['match_score']['total_score'] for m in matches]) / len(matches)
        
        if avg_score < 0.6:
            recommendations.append("Consider improving content quality for better matches")
        
        # Check for common weak points
        content_sim_scores = [m['match_score'].get('content_similarity', 0) for m in matches]
        avg_content_sim = sum(content_sim_scores) / len(content_sim_scores)
        
        if avg_content_sim < 0.5:
            recommendations.append("Focus on more specific, niche content to improve relevance")
        
        quality_scores = [m['match_score'].get('quality_score', 0) for m in matches]
        avg_quality = sum(quality_scores) / len(quality_scores)
        
        if avg_quality < 0.7:
            recommendations.append("Improve content structure and readability")
        
        if len(matches) < 10:
            recommendations.append("Create more content to increase matching opportunities")

        return recommendations

    def _calculate_traffic_compatibility(self, source_website: Website, partner_website: Website) -> float:
        """Calculate traffic pattern compatibility"""
        try:
            # This would ideally use actual traffic data from analytics
            # For now, we'll use domain authority and category as proxies

            source_da = getattr(source_website, 'domain_authority', 0) or 0
            partner_da = getattr(partner_website, 'domain_authority', 0) or 0

            # Similar traffic levels (based on DA) are preferred
            if source_da == 0 or partner_da == 0:
                return 0.5  # Neutral if no data

            da_ratio = min(source_da, partner_da) / max(source_da, partner_da)

            # Bonus for complementary categories (different audiences)
            category_bonus = 0.0
            if source_website.category != partner_website.category:
                category_bonus = 0.2

            return min(1.0, da_ratio + category_bonus)

        except Exception as e:
            logger.warning(f"Traffic compatibility calculation failed: {str(e)}")
            return 0.5

    def _calculate_niche_authority(self, partner_website: Website, partner_analysis: ContentAnalysis) -> float:
        """Calculate niche authority score for potential partner"""
        try:
            score = 0.0

            # Factor 1: Domain Authority (40% weight)
            da = getattr(partner_website, 'domain_authority', 0) or 0
            da_score = min(1.0, da / 100) * 0.4
            score += da_score

            # Factor 2: Content Quality (30% weight)
            quality = partner_analysis.quality_score or 0
            quality_score = min(1.0, quality / 10) * 0.3
            score += quality_score

            # Factor 3: Content Depth (20% weight)
            # Based on analysis complexity and keyword diversity
            depth_score = 0.0
            if partner_analysis.keywords:
                keyword_count = len(partner_analysis.keywords.get('primary_keywords', []))
                depth_score = min(1.0, keyword_count / 20) * 0.2  # Normalize to 20 keywords
            score += depth_score

            # Factor 4: Freshness (10% weight)
            freshness_score = 0.0
            if partner_analysis.analyzed_at:
                days_old = (datetime.utcnow() - partner_analysis.analyzed_at).days
                if days_old <= 30:
                    freshness_score = 0.1
                elif days_old <= 90:
                    freshness_score = 0.05
            score += freshness_score

            return min(1.0, score)

        except Exception as e:
            logger.warning(f"Niche authority calculation failed: {str(e)}")
            return 0.0

    def add_to_blacklist(self, domain: str, user_id: Optional[int] = None):
        """Add domain to blacklist"""
        try:
            if user_id:
                # User-specific blacklist (would need user preferences model)
                pass
            else:
                # Global blacklist
                self.global_blacklist.add(domain.lower())
                cache.set('global_blacklist', list(self.global_blacklist), timeout=3600)
                logger.info(f"Added {domain} to global blacklist")
        except Exception as e:
            logger.error(f"Failed to add {domain} to blacklist: {str(e)}")

    def add_to_whitelist(self, domain: str, user_id: Optional[int] = None):
        """Add domain to whitelist"""
        try:
            if user_id:
                # User-specific whitelist (would need user preferences model)
                pass
            else:
                # Global whitelist
                self.global_whitelist.add(domain.lower())
                cache.set('global_whitelist', list(self.global_whitelist), timeout=3600)
                logger.info(f"Added {domain} to global whitelist")
        except Exception as e:
            logger.error(f"Failed to add {domain} to whitelist: {str(e)}")

    def get_enhanced_match_statistics(self, website_id: int) -> Dict:
        """Get enhanced matching statistics with detailed breakdowns"""
        try:
            website = Website.query.get(website_id)
            if not website:
                return {}

            # Get all potential matches
            matches = self.find_matches(website_id, limit=100)

            if not matches:
                return {
                    'total_potential_matches': 0,
                    'quality_distribution': {},
                    'category_distribution': {},
                    'score_breakdown': {},
                    'recommendations': ['No potential matches found. Consider improving content quality.']
                }

            # Enhanced statistics
            total_matches = len(matches)

            # Quality distribution
            quality_ranges = {'high': 0, 'medium': 0, 'low': 0}
            for match in matches:
                score = match['match_score']['total_score']
                if score >= 0.8:
                    quality_ranges['high'] += 1
                elif score >= 0.6:
                    quality_ranges['medium'] += 1
                else:
                    quality_ranges['low'] += 1

            # Category distribution
            category_stats = defaultdict(lambda: {'count': 0, 'avg_score': 0})
            for match in matches:
                category = match['partner_website'].category or 'uncategorized'
                category_stats[category]['count'] += 1
                category_stats[category]['avg_score'] += match['match_score']['total_score']

            # Calculate average scores per category
            for category in category_stats:
                if category_stats[category]['count'] > 0:
                    category_stats[category]['avg_score'] /= category_stats[category]['count']
                    category_stats[category]['avg_score'] = round(category_stats[category]['avg_score'], 3)

            # Score component breakdown
            score_components = {}
            for component in self.weights.keys():
                scores = [m['match_score'].get(component, 0) for m in matches]
                score_components[component] = {
                    'avg': round(sum(scores) / len(scores), 3),
                    'weight': self.weights[component],
                    'contribution': round((sum(scores) / len(scores)) * self.weights[component], 3)
                }

            return {
                'total_potential_matches': total_matches,
                'quality_distribution': quality_ranges,
                'category_distribution': dict(category_stats),
                'score_breakdown': score_components,
                'recommendations': self._generate_enhanced_recommendations(matches, website)
            }

        except Exception as e:
            logger.error(f"Error getting enhanced match statistics: {str(e)}")
            return {}

    def _generate_enhanced_recommendations(self, matches: List[Dict], website: Website) -> List[str]:
        """Generate enhanced recommendations based on detailed analysis"""
        recommendations = []

        if not matches:
            recommendations.extend([
                "No potential matches found. Consider:",
                "• Improving content quality and depth",
                "• Adding more specific, niche-focused content",
                "• Ensuring proper content analysis is completed"
            ])
            return recommendations

        # Analyze weak scoring components
        avg_scores = {}
        for component in self.weights.keys():
            scores = [m['match_score'].get(component, 0) for m in matches]
            avg_scores[component] = sum(scores) / len(scores)

        # Generate specific recommendations
        if avg_scores.get('content_similarity', 0) < 0.5:
            recommendations.append("• Focus on more specific, targeted keywords to improve content relevance")

        if avg_scores.get('quality_score', 0) < 0.6:
            recommendations.append("• Improve content structure, readability, and overall quality")

        if avg_scores.get('domain_authority', 0) < 0.4:
            recommendations.append("• Work on building domain authority through quality backlinks and content")

        if avg_scores.get('niche_authority', 0) < 0.5:
            recommendations.append("• Establish stronger niche expertise with in-depth, authoritative content")

        if len(matches) < 20:
            recommendations.append("• Create more content to increase matching opportunities")

        # Category-specific recommendations
        category_counts = defaultdict(int)
        for match in matches:
            category = match['partner_website'].category or 'uncategorized'
            category_counts[category] += 1

        if len(category_counts) < 3:
            recommendations.append("• Consider expanding into related niches to find more diverse partners")

        return recommendations

    def get_detailed_similarity_breakdown(self, source_website_id: int, partner_website_id: int) -> Dict:
        """Get detailed similarity breakdown using advanced content matching"""
        try:
            source_analysis = self._get_latest_analysis(source_website_id)
            partner_analysis = self._get_latest_analysis(partner_website_id)

            if not source_analysis or not partner_analysis:
                return {}

            # Get advanced similarity results
            advanced_results = self.advanced_matcher.calculate_advanced_similarity(
                source_analysis, partner_analysis
            )

            # Check for multi-language content
            source_lang = getattr(source_analysis, 'language', 'en') or 'en'
            partner_lang = getattr(partner_analysis, 'language', 'en') or 'en'

            if source_lang != partner_lang:
                # Get multi-language specific results
                multilang_results = self.advanced_matcher.get_multi_language_similarity(
                    source_analysis, partner_analysis
                )
                advanced_results.update(multilang_results)

            return {
                'similarity_breakdown': advanced_results,
                'source_language': source_lang,
                'partner_language': partner_lang,
                'cross_language': source_lang != partner_lang,
                'analysis_timestamps': {
                    'source': source_analysis.analyzed_at.isoformat() if source_analysis.analyzed_at else None,
                    'partner': partner_analysis.analyzed_at.isoformat() if partner_analysis.analyzed_at else None
                }
            }

        except Exception as e:
            logger.error(f"Error getting detailed similarity breakdown: {str(e)}")
            return {}

    def get_niche_matching_insights(self, website_id: int) -> Dict:
        """Get comprehensive niche matching insights for a website"""
        try:
            website = Website.query.get(website_id)
            if not website or not website.category:
                return {}

            # Get category insights
            category_insights = self.niche_matcher.get_category_insights(website.category)

            # Get best niche matches
            best_niche_matches = self.niche_matcher.find_best_niche_matches(website.category, limit=10)

            # Get cross-niche opportunities if website has multiple categories
            # For now, assume single category, but this could be extended
            cross_niche_opportunities = self.niche_matcher.get_cross_niche_opportunities([website.category])

            # Find actual websites in compatible niches
            compatible_websites = []
            for match in best_niche_matches[:5]:  # Top 5 niche matches
                niche_websites = Website.query.filter(
                    Website.category == match['category_id'],
                    Website.status == 'active',
                    Website.id != website_id
                ).limit(3).all()

                for niche_website in niche_websites:
                    compatible_websites.append({
                        'website_id': niche_website.id,
                        'domain': niche_website.domain,
                        'category': niche_website.category,
                        'compatibility_score': match['compatibility_score'],
                        'niche_explanation': match['compatibility_breakdown']['explanation']
                    })

            return {
                'website_category': website.category,
                'category_insights': category_insights,
                'best_niche_matches': best_niche_matches,
                'cross_niche_opportunities': cross_niche_opportunities,
                'compatible_websites': compatible_websites,
                'recommendations': self._generate_niche_recommendations(category_insights, best_niche_matches)
            }

        except Exception as e:
            logger.error(f"Error getting niche matching insights: {str(e)}")
            return {}

    def _generate_niche_recommendations(self, category_insights: Dict, best_matches: List[Dict]) -> List[str]:
        """Generate niche-specific recommendations"""
        recommendations = []

        if not category_insights or not best_matches:
            return ["Unable to generate niche recommendations - insufficient data"]

        # Recommendations based on category level
        category_level = category_insights.get('category', {}).get('level', 0)
        if category_level == 0:
            recommendations.append("• Consider specializing in a sub-niche for better targeting")

        # Recommendations based on available matches
        if len(best_matches) < 5:
            recommendations.append("• Limited niche compatibility - consider broadening content scope")
        elif len(best_matches) > 15:
            recommendations.append("• Many compatible niches available - focus on highest-scoring matches")

        # Recommendations based on complementary categories
        complementary_categories = category_insights.get('complementary_categories', [])
        if complementary_categories:
            comp_names = [cat['name'] for cat in complementary_categories[:3]]
            recommendations.append(f"• Consider partnerships with: {', '.join(comp_names)}")

        # Recommendations based on subcategories
        subcategories = category_insights.get('subcategories', [])
        if subcategories:
            recommendations.append("• Explore sub-niches for more targeted partnerships")

        # Top compatibility recommendations
        if best_matches:
            top_match = best_matches[0]
            if top_match['compatibility_score'] > 0.8:
                recommendations.append(f"• High compatibility with {top_match['category_name']} niche")
            elif top_match['compatibility_score'] < 0.6:
                recommendations.append("• Consider improving niche focus for better match quality")

        return recommendations

    def find_quality_filtered_matches(self, website_id: int, limit: int = 20,
                                    min_quality_score: float = None) -> List[Dict]:
        """Find matches with quality filtering applied"""
        try:
            if min_quality_score is None:
                min_quality_score = self.quality_assessor.quality_thresholds['min_overall_score']

            # Get the source website
            source_website = Website.query.get(website_id)
            if not source_website:
                return []

            # Get source website analysis
            source_analysis = self._get_latest_analysis(website_id)
            if not source_analysis:
                logger.warning(f"No analysis found for website {website_id}")
                return []

            # Get potential partner websites
            potential_partners = self._get_potential_partners(source_website)

            if not potential_partners:
                return []

            # Filter partners by quality first
            quality_filtered_partners = self.quality_assessor.filter_by_quality(
                potential_partners, min_quality_score
            )

            logger.info(f"Quality filtering reduced partners from {len(potential_partners)} to {len(quality_filtered_partners)}")

            # Calculate match scores for quality-filtered partners
            matches = []
            for partner in quality_filtered_partners:
                partner_analysis = self._get_latest_analysis(partner.id)
                if not partner_analysis:
                    continue

                # Get quality assessment for additional insights
                partner_quality = self.quality_assessor.assess_website_quality(partner, partner_analysis)

                # Calculate match score
                match_score = self._calculate_match_score(
                    source_website, source_analysis,
                    partner, partner_analysis
                )

                if match_score['total_score'] >= 0.6:  # Minimum threshold
                    matches.append({
                        'partner_website': partner,
                        'match_score': match_score,
                        'quality_metrics': {
                            'overall_score': partner_quality.overall_score,
                            'quality_level': partner_quality.quality_level,
                            'red_flags': partner_quality.red_flags,
                            'positive_signals': partner_quality.positive_signals
                        },
                        'reasons': self._generate_match_reasons(match_score),
                        'estimated_value': self._estimate_match_value(match_score),
                        'mutual_benefit': match_score['mutual_benefit_score']
                    })

            # Sort by total score and return top matches
            matches.sort(key=lambda x: x['match_score']['total_score'], reverse=True)
            return matches[:limit]

        except Exception as e:
            logger.error(f"Error finding quality-filtered matches: {str(e)}")
            return []

    def get_quality_insights(self, website_id: int) -> Dict:
        """Get comprehensive quality insights for a website"""
        try:
            website = Website.query.get(website_id)
            if not website:
                return {}

            analysis = self._get_latest_analysis(website_id)

            # Generate quality report
            quality_report = self.quality_assessor.generate_quality_report(website, analysis)

            # Get quality comparison with potential partners
            potential_partners = self._get_potential_partners(website)[:20]  # Sample of partners
            if potential_partners:
                quality_distribution = self.quality_assessor.get_quality_distribution(potential_partners)
            else:
                quality_distribution = {}

            # Find high-quality potential matches
            high_quality_matches = self.find_quality_filtered_matches(
                website_id, limit=10, min_quality_score=8.0
            )

            return {
                'website_quality_report': quality_report,
                'partner_quality_distribution': quality_distribution,
                'high_quality_matches': len(high_quality_matches),
                'quality_recommendations': quality_report.get('recommendations', []),
                'competitive_analysis': {
                    'above_average_quality': quality_report.get('overall_score', 0) > 7.0,
                    'quality_percentile': self._calculate_quality_percentile(
                        quality_report.get('overall_score', 0), potential_partners
                    )
                }
            }

        except Exception as e:
            logger.error(f"Error getting quality insights: {str(e)}")
            return {}

    def _calculate_quality_percentile(self, website_score: float, comparison_websites: List[Website]) -> float:
        """Calculate quality percentile compared to other websites"""
        try:
            if not comparison_websites:
                return 50.0  # Neutral percentile

            # Get quality scores for comparison websites
            quality_assessments = self.quality_assessor.batch_assess_quality(comparison_websites)
            comparison_scores = [
                metrics.overall_score for metrics in quality_assessments.values()
                if metrics.overall_score > 0
            ]

            if not comparison_scores:
                return 50.0

            # Calculate percentile
            scores_below = sum(1 for score in comparison_scores if score < website_score)
            percentile = (scores_below / len(comparison_scores)) * 100

            return round(percentile, 1)

        except Exception as e:
            logger.warning(f"Error calculating quality percentile: {str(e)}")
            return 50.0

    def find_matches_with_preferences(self, website_id: int, limit: int = 20) -> List[Dict]:
        """Find matches using user preferences for filtering and scoring"""
        try:
            # Get the source website
            source_website = Website.query.get(website_id)
            if not source_website:
                return []

            # Get user preferences
            user_preferences = self.preferences_service.get_user_preferences(
                source_website.user_id, website_id
            )

            # Get custom weights if user has them
            custom_weights = self.preferences_service.get_matching_weights_for_user(
                source_website.user_id, website_id
            )

            # Temporarily update weights for this search
            original_weights = self.weights.copy()
            self.weights.update(custom_weights)

            # Update thresholds based on user preferences
            quality_thresholds = user_preferences.get('quality_thresholds', {})
            original_thresholds = self.thresholds.copy()

            self.thresholds.update({
                'min_quality_score': quality_thresholds.get('min_quality_score', self.thresholds['min_quality_score']),
                'min_domain_authority': quality_thresholds.get('min_domain_authority', self.thresholds['min_domain_authority']),
                'max_spam_score': quality_thresholds.get('max_spam_score', self.thresholds.get('max_spam_score', 3.0)),
                'min_content_similarity': user_preferences.get('content_preferences', {}).get('min_content_similarity', self.thresholds['min_content_similarity'])
            })

            try:
                # Get source website analysis
                source_analysis = self._get_latest_analysis(website_id)
                if not source_analysis:
                    logger.warning(f"No analysis found for website {website_id}")
                    return []

                # Get potential partner websites with preference-based filtering
                potential_partners = self._get_potential_partners_with_preferences(
                    source_website, user_preferences
                )

                if not potential_partners:
                    return []

                # Calculate match scores
                matches = []
                for partner in potential_partners:
                    partner_analysis = self._get_latest_analysis(partner.id)
                    if not partner_analysis:
                        continue

                    # Apply content filtering based on preferences
                    if self._should_filter_by_content_preferences(partner_analysis, user_preferences):
                        continue

                    # Calculate match score
                    match_score = self._calculate_match_score(
                        source_website, source_analysis,
                        partner, partner_analysis
                    )

                    # Apply preference-based filtering
                    if match_score['total_score'] >= self.thresholds['min_quality_score'] / 10:  # Convert to 0-1 scale

                        # Check auto-approval/rejection
                        auto_approve = self.preferences_service.should_auto_approve(
                            source_website.user_id, match_score['total_score'], website_id
                        )
                        auto_reject = self.preferences_service.should_auto_reject(
                            source_website.user_id, match_score['total_score'], website_id
                        )

                        if auto_reject:
                            continue

                        matches.append({
                            'partner_website': partner,
                            'match_score': match_score,
                            'reasons': self._generate_match_reasons(match_score),
                            'estimated_value': self._estimate_match_value(match_score),
                            'mutual_benefit': match_score['mutual_benefit_score'],
                            'auto_approve': auto_approve,
                            'preference_applied': True
                        })

                # Sort by total score and return top matches
                matches.sort(key=lambda x: x['match_score']['total_score'], reverse=True)
                return matches[:limit]

            finally:
                # Restore original weights and thresholds
                self.weights = original_weights
                self.thresholds = original_thresholds

        except Exception as e:
            logger.error(f"Error finding matches with preferences: {str(e)}")
            return []
