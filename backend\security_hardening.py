"""
Security Hardening Implementation for LinkUp Backend
Implements security fixes and hardening measures
"""
import os
import secrets
import hashlib
import hmac
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, current_app
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
import re


class SecurityManager:
    """Centralized security management"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize security manager with Flask app"""
        # Set secure defaults
        app.config.setdefault('SECRET_KEY', self.generate_secure_secret_key())
        app.config.setdefault('WTF_CSRF_ENABLED', True)
        app.config.setdefault('SESSION_COOKIE_SECURE', True)
        app.config.setdefault('SESSION_COOKIE_HTTPONLY', True)
        app.config.setdefault('SESSION_COOKIE_SAMESITE', 'Lax')
        app.config.setdefault('PERMANENT_SESSION_LIFETIME', timedelta(hours=1))
        
        # Security headers
        @app.after_request
        def add_security_headers(response):
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
            response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
            response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
            return response
    
    @staticmethod
    def generate_secure_secret_key():
        """Generate cryptographically secure secret key"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def generate_secure_token():
        """Generate secure random token"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def hash_password(password):
        """Securely hash password using Werkzeug"""
        return generate_password_hash(password, method='pbkdf2:sha256', salt_length=16)
    
    @staticmethod
    def verify_password(password, password_hash):
        """Verify password against hash"""
        return check_password_hash(password_hash, password)
    
    @staticmethod
    def generate_jwt_token(user_id, expires_in=3600):
        """Generate JWT token for user"""
        payload = {
            'user_id': user_id,
            'exp': datetime.utcnow() + timedelta(seconds=expires_in),
            'iat': datetime.utcnow()
        }
        return jwt.encode(payload, current_app.config['SECRET_KEY'], algorithm='HS256')
    
    @staticmethod
    def verify_jwt_token(token):
        """Verify JWT token"""
        try:
            payload = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
            return payload['user_id']
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None


class InputValidator:
    """Input validation and sanitization"""
    
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_domain(domain):
        """Validate domain format"""
        pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return re.match(pattern, domain) is not None and len(domain) <= 253
    
    @staticmethod
    def validate_url(url):
        """Validate URL format"""
        pattern = r'^https?://[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*(/.*)?$'
        return re.match(pattern, url) is not None
    
    @staticmethod
    def sanitize_string(input_string, max_length=255):
        """Sanitize string input"""
        if not isinstance(input_string, str):
            return ""
        
        # Remove null bytes and control characters
        sanitized = ''.join(char for char in input_string if ord(char) >= 32 or char in '\t\n\r')
        
        # Limit length
        return sanitized[:max_length].strip()
    
    @staticmethod
    def validate_json_schema(data, schema):
        """Validate JSON data against schema"""
        try:
            from jsonschema import validate, ValidationError
            validate(instance=data, schema=schema)
            return True, None
        except ValidationError as e:
            return False, str(e)
        except ImportError:
            # Fallback validation if jsonschema not available
            return True, None


class RateLimiter:
    """Rate limiting implementation"""
    
    def __init__(self, redis_client=None):
        self.redis_client = redis_client
        self.memory_store = {}  # Fallback for when Redis is not available
    
    def is_allowed(self, key, limit, window):
        """Check if request is allowed based on rate limit"""
        if self.redis_client:
            return self._redis_rate_limit(key, limit, window)
        else:
            return self._memory_rate_limit(key, limit, window)
    
    def _redis_rate_limit(self, key, limit, window):
        """Redis-based rate limiting"""
        try:
            pipe = self.redis_client.pipeline()
            pipe.incr(key)
            pipe.expire(key, window)
            results = pipe.execute()
            
            current_requests = results[0]
            return current_requests <= limit
        except Exception:
            # Fallback to allowing request if Redis fails
            return True
    
    def _memory_rate_limit(self, key, limit, window):
        """Memory-based rate limiting (fallback)"""
        now = datetime.utcnow()
        
        if key not in self.memory_store:
            self.memory_store[key] = []
        
        # Clean old entries
        cutoff = now - timedelta(seconds=window)
        self.memory_store[key] = [
            timestamp for timestamp in self.memory_store[key]
            if timestamp > cutoff
        ]
        
        # Check limit
        if len(self.memory_store[key]) >= limit:
            return False
        
        # Add current request
        self.memory_store[key].append(now)
        return True


def require_auth(f):
    """Decorator to require authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Authentication required'}), 401
        
        if token.startswith('Bearer '):
            token = token[7:]
        
        user_id = SecurityManager.verify_jwt_token(token)
        if not user_id:
            return jsonify({'error': 'Invalid or expired token'}), 401
        
        request.current_user_id = user_id
        return f(*args, **kwargs)
    
    return decorated_function


def require_permission(permission):
    """Decorator to require specific permission"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Check if user has required permission
            user_id = getattr(request, 'current_user_id', None)
            if not user_id:
                return jsonify({'error': 'Authentication required'}), 401
            
            # Here you would check user permissions from database
            # For now, we'll assume admin users have all permissions
            if not has_permission(user_id, permission):
                return jsonify({'error': 'Insufficient permissions'}), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def has_permission(user_id, permission):
    """Check if user has specific permission"""
    # This would typically query the database
    # For now, return True for demonstration
    return True


def validate_input(schema):
    """Decorator to validate request input"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.is_json:
                data = request.get_json()
                is_valid, error = InputValidator.validate_json_schema(data, schema)
                if not is_valid:
                    return jsonify({'error': f'Invalid input: {error}'}), 400
                request.validated_data = data
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def rate_limit(limit=100, window=3600):
    """Decorator to apply rate limiting"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Use IP address as key (in production, use user ID if authenticated)
            key = f"rate_limit:{request.remote_addr}:{f.__name__}"
            
            # Get rate limiter instance (would be injected in real app)
            limiter = RateLimiter()
            
            if not limiter.is_allowed(key, limit, window):
                return jsonify({'error': 'Rate limit exceeded'}), 429
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


class CSRFProtection:
    """CSRF protection implementation"""
    
    @staticmethod
    def generate_csrf_token():
        """Generate CSRF token"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def validate_csrf_token(token, session_token):
        """Validate CSRF token"""
        return hmac.compare_digest(token, session_token)


class SQLInjectionPrevention:
    """SQL injection prevention utilities"""
    
    @staticmethod
    def sanitize_sql_identifier(identifier):
        """Sanitize SQL identifier (table/column names)"""
        # Only allow alphanumeric characters and underscores
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', identifier):
            raise ValueError("Invalid SQL identifier")
        return identifier
    
    @staticmethod
    def validate_sql_order_by(order_by, allowed_columns):
        """Validate ORDER BY clause"""
        if order_by not in allowed_columns:
            raise ValueError("Invalid ORDER BY column")
        return order_by


class XSSPrevention:
    """XSS prevention utilities"""
    
    @staticmethod
    def escape_html(text):
        """Escape HTML characters"""
        if not isinstance(text, str):
            return text
        
        escape_chars = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#x27;',
            '/': '&#x2F;'
        }
        
        for char, escape in escape_chars.items():
            text = text.replace(char, escape)
        
        return text
    
    @staticmethod
    def sanitize_json_output(data):
        """Sanitize JSON output to prevent XSS"""
        if isinstance(data, dict):
            return {key: XSSPrevention.sanitize_json_output(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [XSSPrevention.sanitize_json_output(item) for item in data]
        elif isinstance(data, str):
            return XSSPrevention.escape_html(data)
        else:
            return data


class SecurityLogger:
    """Security event logging"""
    
    @staticmethod
    def log_security_event(event_type, details, user_id=None, ip_address=None):
        """Log security event"""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'details': details,
            'user_id': user_id,
            'ip_address': ip_address or request.remote_addr if request else None
        }
        
        # In production, this would write to a secure log file or database
        print(f"SECURITY EVENT: {log_entry}")
    
    @staticmethod
    def log_failed_login(username, ip_address):
        """Log failed login attempt"""
        SecurityLogger.log_security_event(
            'failed_login',
            f'Failed login attempt for username: {username}',
            ip_address=ip_address
        )
    
    @staticmethod
    def log_suspicious_activity(activity, user_id=None):
        """Log suspicious activity"""
        SecurityLogger.log_security_event(
            'suspicious_activity',
            activity,
            user_id=user_id
        )


# Security configuration schemas
WEBSITE_SCHEMA = {
    "type": "object",
    "properties": {
        "domain": {"type": "string", "maxLength": 253},
        "category": {"type": "string", "maxLength": 100},
        "description": {"type": "string", "maxLength": 1000}
    },
    "required": ["domain"],
    "additionalProperties": False
}

ANALYSIS_REQUEST_SCHEMA = {
    "type": "object",
    "properties": {
        "website_id": {"type": "integer", "minimum": 1},
        "competitor_domains": {
            "type": "array",
            "items": {"type": "string", "maxLength": 253},
            "maxItems": 10
        }
    },
    "required": ["website_id"],
    "additionalProperties": False
}

CONTENT_ANALYSIS_SCHEMA = {
    "type": "object",
    "properties": {
        "content_url": {"type": "string", "maxLength": 2048},
        "target_keywords": {
            "type": "array",
            "items": {"type": "string", "maxLength": 100},
            "maxItems": 20
        }
    },
    "required": ["content_url"],
    "additionalProperties": False
}


def init_security(app):
    """Initialize security for Flask app"""
    security_manager = SecurityManager(app)
    
    # Initialize rate limiter
    try:
        import redis
        redis_client = redis.Redis.from_url(app.config.get('REDIS_URL', 'redis://localhost:6379/0'))
        rate_limiter = RateLimiter(redis_client)
    except ImportError:
        rate_limiter = RateLimiter()
    
    app.rate_limiter = rate_limiter
    app.security_manager = security_manager
    
    return security_manager
