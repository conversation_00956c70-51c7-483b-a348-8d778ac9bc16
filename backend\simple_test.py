#!/usr/bin/env python3
"""
Simple test runner for LinkUp Plugin Backend
Runs basic tests without pytest-flask compatibility issues
"""
import os
import sys
import traceback
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Set environment for testing
os.environ['FLASK_ENV'] = 'testing'
os.environ['FLASK_CONFIG'] = 'testing'


class SimpleTestRunner:
    """Simple test runner class"""
    
    def __init__(self):
        self.tests_passed = 0
        self.tests_failed = 0
        self.failures = []
    
    def run_test(self, test_func, test_name):
        """Run a single test function"""
        try:
            print(f"Running {test_name}...", end=" ")
            test_func()
            print("✓ PASSED")
            self.tests_passed += 1
        except Exception as e:
            print("✗ FAILED")
            self.tests_failed += 1
            self.failures.append((test_name, str(e), traceback.format_exc()))
    
    def print_summary(self):
        """Print test summary"""
        total_tests = self.tests_passed + self.tests_failed
        print("\n" + "="*50)
        print(f"TEST SUMMARY")
        print("="*50)
        print(f"Total tests: {total_tests}")
        print(f"Passed: {self.tests_passed}")
        print(f"Failed: {self.tests_failed}")
        
        if self.failures:
            print("\nFAILURES:")
            print("-"*50)
            for test_name, error, traceback_str in self.failures:
                print(f"\n{test_name}:")
                print(f"Error: {error}")
                if "--verbose" in sys.argv:
                    print(f"Traceback:\n{traceback_str}")
        
        print("="*50)
        return self.tests_failed == 0


def test_basic_functionality():
    """Test basic Python functionality"""
    assert 1 + 1 == 2
    assert "LinkUp" in "LinkUp Plugin"
    assert len([1, 2, 3]) == 3


def test_environment_setup():
    """Test environment setup"""
    assert os.environ.get('FLASK_ENV') == 'testing'
    assert os.environ.get('FLASK_CONFIG') == 'testing'


def test_path_setup():
    """Test Python path setup"""
    assert str(backend_dir) in sys.path


def test_imports():
    """Test that we can import required modules"""
    # Test standard library imports
    import json
    import datetime
    import hashlib
    assert json is not None
    assert datetime is not None
    assert hashlib is not None
    
    # Test installed packages
    import flask
    import sqlalchemy
    import pandas
    import numpy
    assert flask is not None
    assert sqlalchemy is not None
    assert pandas is not None
    assert numpy is not None


def test_flask_app_creation():
    """Test Flask app creation"""
    try:
        from app import create_app
        app = create_app('testing')
        assert app is not None
        assert app.config['TESTING'] is True
        print("  Flask app created successfully")
    except Exception as e:
        print(f"  Flask app creation failed: {e}")
        raise


def test_database_models():
    """Test database model imports"""
    try:
        from app.models.user import User
        from app.models.website import Website
        from app.models.api_key import ApiKey
        assert User is not None
        assert Website is not None
        assert ApiKey is not None
        print("  Database models imported successfully")
    except Exception as e:
        print(f"  Database model import failed: {e}")
        raise


def test_services():
    """Test service imports"""
    try:
        from app.services.auth_service import AuthenticationService
        from app.services.content_analysis_service import ContentAnalysisService
        assert AuthenticationService is not None
        assert ContentAnalysisService is not None
        print("  Services imported successfully")
    except Exception as e:
        print(f"  Service import failed: {e}")
        raise


def test_nlp_libraries():
    """Test NLP library availability"""
    try:
        import spacy
        import nltk
        import textblob
        import sklearn
        assert spacy is not None
        assert nltk is not None
        assert textblob is not None
        assert sklearn is not None
        print("  NLP libraries available")
    except ImportError as e:
        print(f"  NLP library not available: {e}")
        # Don't fail the test for missing NLP libraries
        pass


def test_user_model_basic():
    """Test basic User model functionality"""
    try:
        from app import create_app, db
        from app.models.user import User
        
        app = create_app('testing')
        with app.app_context():
            # Test user creation
            user = User(
                email='<EMAIL>',
                first_name='Test',
                last_name='User'
            )
            user.set_password('testpass123')
            
            assert user.email == '<EMAIL>'
            assert user.check_password('testpass123')
            assert not user.check_password('wrongpass')
            print("  User model basic functionality works")
    except Exception as e:
        print(f"  User model test failed: {e}")
        raise


def test_content_analysis_basic():
    """Test basic content analysis functionality"""
    try:
        from app.services.content_analysis_service import ContentAnalysisService
        
        # Create service instance (this might fail if spaCy model is missing)
        try:
            service = ContentAnalysisService()
            print("  Content analysis service created successfully")
        except OSError as e:
            if "en_core_web_sm" in str(e):
                print("  spaCy model not installed - skipping content analysis test")
                return
            else:
                raise
        
        # Test basic analysis methods
        content = "This is a test sentence for analysis."
        syllables = service._count_syllables("testing")
        assert syllables >= 1
        
        reading_level = service._get_reading_level(75.0)
        assert reading_level in ['Very Easy', 'Easy', 'Fairly Easy', 'Standard', 'Fairly Difficult', 'Difficult', 'Very Difficult']
        
        print("  Content analysis basic methods work")
    except Exception as e:
        print(f"  Content analysis test failed: {e}")
        raise


def main():
    """Main test runner"""
    print("LinkUp Plugin Backend - Simple Test Runner")
    print("="*50)
    
    runner = SimpleTestRunner()
    
    # Define tests to run
    tests = [
        (test_basic_functionality, "Basic Functionality"),
        (test_environment_setup, "Environment Setup"),
        (test_path_setup, "Python Path Setup"),
        (test_imports, "Required Imports"),
        (test_flask_app_creation, "Flask App Creation"),
        (test_database_models, "Database Models"),
        (test_services, "Services"),
        (test_nlp_libraries, "NLP Libraries"),
        (test_user_model_basic, "User Model Basic"),
        (test_content_analysis_basic, "Content Analysis Basic"),
    ]
    
    # Run all tests
    for test_func, test_name in tests:
        runner.run_test(test_func, test_name)
    
    # Print summary and return success/failure
    success = runner.print_summary()
    return 0 if success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
