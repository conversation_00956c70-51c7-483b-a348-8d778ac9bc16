"""
Quality Assessment Service for Potential Partners
Implements comprehensive quality scoring including spam detection,
content quality analysis, site health metrics, and trust signals evaluation
"""
import logging
import re
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from app.models.website import Website
from app.models.analysis import ContentAnalysis
from app import cache

logger = logging.getLogger(__name__)


class QualityLevel(Enum):
    """Quality levels for websites"""
    EXCELLENT = (9.0, 10.0)
    VERY_GOOD = (8.0, 8.9)
    GOOD = (7.0, 7.9)
    FAIR = (6.0, 6.9)
    POOR = (4.0, 5.9)
    VERY_POOR = (2.0, 3.9)
    SPAM = (0.0, 1.9)


@dataclass
class QualityMetrics:
    """Container for quality assessment metrics"""
    content_quality: float = 0.0
    technical_health: float = 0.0
    authority_signals: float = 0.0
    trust_indicators: float = 0.0
    spam_score: float = 0.0
    overall_score: float = 0.0
    quality_level: str = "UNKNOWN"
    red_flags: List[str] = None
    positive_signals: List[str] = None
    
    def __post_init__(self):
        if self.red_flags is None:
            self.red_flags = []
        if self.positive_signals is None:
            self.positive_signals = []


class QualityAssessmentService:
    """Comprehensive quality assessment for potential partners"""
    
    def __init__(self):
        """Initialize the quality assessment service"""
        # Quality scoring weights
        self.quality_weights = {
            'content_quality': 0.35,
            'technical_health': 0.20,
            'authority_signals': 0.25,
            'trust_indicators': 0.15,
            'spam_penalty': -0.5  # Negative weight for spam indicators
        }
        
        # Spam detection patterns
        self.spam_patterns = {
            'domain_patterns': [
                r'.*\d{4,}.*',  # Domains with many numbers
                r'.*-{3,}.*',   # Multiple consecutive hyphens
                r'.*\.tk$|.*\.ml$|.*\.ga$|.*\.cf$',  # Free TLD domains
                r'.*buy.*cheap.*',  # Commercial spam patterns
                r'.*free.*download.*',
                r'.*casino.*poker.*'
            ],
            'content_patterns': [
                r'click here',
                r'buy now',
                r'limited time',
                r'act now',
                r'guaranteed',
                r'make money fast',
                r'work from home',
                r'lose weight fast'
            ]
        }
        
        # Trust signal indicators
        self.trust_signals = {
            'domain_age_months': 12,  # Minimum months for trust
            'ssl_certificate': True,
            'privacy_policy': True,
            'contact_information': True,
            'social_media_presence': True,
            'regular_content_updates': True
        }
        
        # Quality thresholds
        self.quality_thresholds = {
            'min_content_quality': 6.0,
            'max_spam_score': 3.0,
            'min_domain_authority': 20,
            'min_trust_signals': 3,
            'min_overall_score': 6.0
        }
    
    def assess_website_quality(self, website: Website, analysis: Optional[ContentAnalysis] = None) -> QualityMetrics:
        """Perform comprehensive quality assessment of a website"""
        try:
            metrics = QualityMetrics()
            
            # 1. Content Quality Assessment
            metrics.content_quality = self._assess_content_quality(website, analysis)
            
            # 2. Technical Health Assessment
            metrics.technical_health = self._assess_technical_health(website)
            
            # 3. Authority Signals Assessment
            metrics.authority_signals = self._assess_authority_signals(website)
            
            # 4. Trust Indicators Assessment
            metrics.trust_indicators = self._assess_trust_indicators(website)
            
            # 5. Spam Detection
            metrics.spam_score = self._calculate_spam_score(website, analysis)
            
            # Calculate overall score
            metrics.overall_score = self._calculate_overall_score(metrics)
            
            # Determine quality level
            metrics.quality_level = self._determine_quality_level(metrics.overall_score)
            
            # Generate insights
            metrics.red_flags = self._identify_red_flags(website, analysis, metrics)
            metrics.positive_signals = self._identify_positive_signals(website, analysis, metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error assessing website quality: {str(e)}")
            return QualityMetrics()
    
    def _assess_content_quality(self, website: Website, analysis: Optional[ContentAnalysis]) -> float:
        """Assess content quality based on analysis data"""
        if not analysis:
            return 5.0  # Neutral score if no analysis available
        
        quality_score = 0.0
        factors = 0
        
        # Factor 1: Base quality score from analysis
        if analysis.quality_score:
            quality_score += analysis.quality_score
            factors += 1
        
        # Factor 2: Content depth and complexity
        if analysis.keywords:
            keyword_count = len(analysis.keywords.get('primary_keywords', []))
            depth_score = min(10.0, (keyword_count / 20) * 10)  # Normalize to 10
            quality_score += depth_score
            factors += 1
        
        # Factor 3: Content structure quality
        if hasattr(analysis, 'content_structure') and analysis.content_structure:
            structure = analysis.content_structure
            structure_score = structure.get('structure_score', 5.0)
            quality_score += structure_score
            factors += 1
        
        # Factor 4: Readability and linguistic quality
        if hasattr(analysis, 'linguistic_analysis') and analysis.linguistic_analysis:
            linguistic = analysis.linguistic_analysis
            readability_score = linguistic.get('readability_score', 5.0)
            quality_score += readability_score
            factors += 1
        
        return quality_score / factors if factors > 0 else 5.0
    
    def _assess_technical_health(self, website: Website) -> float:
        """Assess technical health of the website"""
        # In a production system, this would check:
        # - Page load speed
        # - Mobile responsiveness
        # - SSL certificate
        # - Broken links
        # - SEO technical factors
        
        health_score = 7.0  # Default neutral score
        
        # Check domain authority as a proxy for technical health
        if hasattr(website, 'domain_authority') and website.domain_authority:
            da = website.domain_authority
            if da >= 50:
                health_score = 9.0
            elif da >= 30:
                health_score = 8.0
            elif da >= 20:
                health_score = 7.0
            elif da >= 10:
                health_score = 6.0
            else:
                health_score = 5.0
        
        # Check if domain looks suspicious
        if website.domain:
            domain = website.domain.lower()
            if any(re.search(pattern, domain) for pattern in self.spam_patterns['domain_patterns']):
                health_score -= 2.0
        
        return max(0.0, min(10.0, health_score))
    
    def _assess_authority_signals(self, website: Website) -> float:
        """Assess authority signals of the website"""
        authority_score = 0.0
        factors = 0
        
        # Factor 1: Domain Authority
        if hasattr(website, 'domain_authority') and website.domain_authority:
            da_score = min(10.0, website.domain_authority / 10)  # Normalize DA to 0-10
            authority_score += da_score
            factors += 1
        
        # Factor 2: Page Authority (if available)
        if hasattr(website, 'page_authority') and website.page_authority:
            pa_score = min(10.0, website.page_authority / 10)
            authority_score += pa_score
            factors += 1
        
        # Factor 3: Website age (estimated from creation date)
        if website.created_at:
            age_months = (datetime.utcnow() - website.created_at).days / 30
            age_score = min(10.0, age_months / 12)  # 1 year = max score
            authority_score += age_score
            factors += 1
        
        # Factor 4: Content consistency (regular updates)
        if website.last_analyzed_at:
            days_since_analysis = (datetime.utcnow() - website.last_analyzed_at).days
            freshness_score = max(0.0, 10.0 - (days_since_analysis / 30))
            authority_score += freshness_score
            factors += 1
        
        return authority_score / factors if factors > 0 else 5.0
    
    def _assess_trust_indicators(self, website: Website) -> float:
        """Assess trust indicators of the website"""
        trust_score = 5.0  # Base neutral score
        
        # In a production system, this would check:
        # - SSL certificate presence
        # - Privacy policy existence
        # - Contact information availability
        # - Social media presence
        # - Professional design quality
        # - User reviews and testimonials
        
        # For now, use domain characteristics and basic checks
        if website.domain:
            domain = website.domain.lower()
            
            # Positive trust signals
            if any(tld in domain for tld in ['.edu', '.gov', '.org']):
                trust_score += 2.0
            elif any(tld in domain for tld in ['.com', '.net']):
                trust_score += 0.5
            
            # Check for professional domain structure
            if len(domain.split('.')[0]) > 3 and '-' not in domain:
                trust_score += 0.5
        
        # Check website title and description for professionalism
        if website.title:
            title = website.title.lower()
            if any(word in title for word in ['official', 'professional', 'company', 'corporation']):
                trust_score += 0.5
        
        return max(0.0, min(10.0, trust_score))
    
    def _calculate_spam_score(self, website: Website, analysis: Optional[ContentAnalysis]) -> float:
        """Calculate spam likelihood score (higher = more likely spam)"""
        spam_score = 0.0
        
        # Check domain for spam patterns
        if website.domain:
            domain = website.domain.lower()
            for pattern in self.spam_patterns['domain_patterns']:
                if re.search(pattern, domain):
                    spam_score += 2.0
        
        # Check content for spam patterns
        if analysis and analysis.keywords:
            keywords = analysis.keywords.get('primary_keywords', [])
            keyword_text = ' '.join([kw.get('keyword', '').lower() for kw in keywords])
            
            for pattern in self.spam_patterns['content_patterns']:
                if re.search(pattern, keyword_text):
                    spam_score += 1.0
        
        # Check for suspicious characteristics
        if website.title:
            title = website.title.lower()
            suspicious_words = ['free', 'cheap', 'discount', 'sale', 'buy', 'click']
            spam_words_count = sum(1 for word in suspicious_words if word in title)
            spam_score += spam_words_count * 0.5
        
        # Check domain age (very new domains are suspicious)
        if website.created_at:
            age_days = (datetime.utcnow() - website.created_at).days
            if age_days < 30:
                spam_score += 3.0
            elif age_days < 90:
                spam_score += 1.0
        
        return min(10.0, spam_score)
    
    def _calculate_overall_score(self, metrics: QualityMetrics) -> float:
        """Calculate overall quality score"""
        weighted_score = (
            metrics.content_quality * self.quality_weights['content_quality'] +
            metrics.technical_health * self.quality_weights['technical_health'] +
            metrics.authority_signals * self.quality_weights['authority_signals'] +
            metrics.trust_indicators * self.quality_weights['trust_indicators'] +
            metrics.spam_score * self.quality_weights['spam_penalty']
        )
        
        return max(0.0, min(10.0, weighted_score))
    
    def _determine_quality_level(self, overall_score: float) -> str:
        """Determine quality level based on overall score"""
        for level in QualityLevel:
            min_score, max_score = level.value
            if min_score <= overall_score <= max_score:
                return level.name
        return "UNKNOWN"

    def _identify_red_flags(self, website: Website, analysis: Optional[ContentAnalysis],
                           metrics: QualityMetrics) -> List[str]:
        """Identify red flags that indicate quality issues"""
        red_flags = []

        # High spam score
        if metrics.spam_score > 5.0:
            red_flags.append("High spam likelihood detected")

        # Low content quality
        if metrics.content_quality < 4.0:
            red_flags.append("Poor content quality")

        # Low authority signals
        if metrics.authority_signals < 3.0:
            red_flags.append("Weak authority signals")

        # Technical issues
        if metrics.technical_health < 5.0:
            red_flags.append("Technical health concerns")

        # Domain-specific red flags
        if website.domain:
            domain = website.domain.lower()
            if any(re.search(pattern, domain) for pattern in self.spam_patterns['domain_patterns']):
                red_flags.append("Suspicious domain pattern")

        # Very new website
        if website.created_at:
            age_days = (datetime.utcnow() - website.created_at).days
            if age_days < 30:
                red_flags.append("Very new website (less than 30 days)")

        # No recent analysis
        if not website.last_analyzed_at:
            red_flags.append("No content analysis available")
        elif (datetime.utcnow() - website.last_analyzed_at).days > 180:
            red_flags.append("Outdated content analysis (over 6 months)")

        return red_flags

    def _identify_positive_signals(self, website: Website, analysis: Optional[ContentAnalysis],
                                 metrics: QualityMetrics) -> List[str]:
        """Identify positive signals that indicate high quality"""
        positive_signals = []

        # High overall quality
        if metrics.overall_score >= 8.0:
            positive_signals.append("Excellent overall quality score")
        elif metrics.overall_score >= 7.0:
            positive_signals.append("Good overall quality score")

        # High content quality
        if metrics.content_quality >= 8.0:
            positive_signals.append("High-quality content")

        # Strong authority signals
        if metrics.authority_signals >= 8.0:
            positive_signals.append("Strong authority indicators")

        # Good technical health
        if metrics.technical_health >= 8.0:
            positive_signals.append("Excellent technical health")

        # High trust indicators
        if metrics.trust_indicators >= 8.0:
            positive_signals.append("Strong trust signals")

        # Low spam score
        if metrics.spam_score < 1.0:
            positive_signals.append("Very low spam likelihood")

        # Established website
        if website.created_at:
            age_months = (datetime.utcnow() - website.created_at).days / 30
            if age_months >= 24:
                positive_signals.append("Well-established website (2+ years)")
            elif age_months >= 12:
                positive_signals.append("Established website (1+ year)")

        # Regular content updates
        if website.last_analyzed_at:
            days_since_analysis = (datetime.utcnow() - website.last_analyzed_at).days
            if days_since_analysis <= 30:
                positive_signals.append("Recently updated content")

        # Professional domain
        if website.domain and any(tld in website.domain for tld in ['.com', '.org', '.edu', '.gov']):
            positive_signals.append("Professional domain extension")

        return positive_signals

    def batch_assess_quality(self, websites: List[Website]) -> Dict[int, QualityMetrics]:
        """Perform quality assessment on multiple websites"""
        results = {}

        for website in websites:
            try:
                # Get latest analysis for the website
                analysis = None
                if hasattr(website, 'analyses') and website.analyses:
                    analysis = max(website.analyses, key=lambda a: a.analyzed_at or datetime.min)

                # Assess quality
                quality_metrics = self.assess_website_quality(website, analysis)
                results[website.id] = quality_metrics

            except Exception as e:
                logger.error(f"Error assessing quality for website {website.id}: {str(e)}")
                results[website.id] = QualityMetrics()

        return results

    def filter_by_quality(self, websites: List[Website], min_quality_score: float = None) -> List[Website]:
        """Filter websites by minimum quality score"""
        if min_quality_score is None:
            min_quality_score = self.quality_thresholds['min_overall_score']

        filtered_websites = []
        quality_assessments = self.batch_assess_quality(websites)

        for website in websites:
            quality_metrics = quality_assessments.get(website.id)
            if quality_metrics and quality_metrics.overall_score >= min_quality_score:
                filtered_websites.append(website)

        return filtered_websites

    def get_quality_distribution(self, websites: List[Website]) -> Dict:
        """Get quality distribution statistics for a list of websites"""
        quality_assessments = self.batch_assess_quality(websites)

        distribution = {level.name: 0 for level in QualityLevel}
        scores = []
        red_flags_count = {}
        positive_signals_count = {}

        for website_id, metrics in quality_assessments.items():
            # Count quality levels
            distribution[metrics.quality_level] += 1
            scores.append(metrics.overall_score)

            # Count red flags
            for flag in metrics.red_flags:
                red_flags_count[flag] = red_flags_count.get(flag, 0) + 1

            # Count positive signals
            for signal in metrics.positive_signals:
                positive_signals_count[signal] = positive_signals_count.get(signal, 0) + 1

        # Calculate statistics
        avg_score = sum(scores) / len(scores) if scores else 0

        return {
            'total_websites': len(websites),
            'quality_distribution': distribution,
            'average_score': round(avg_score, 2),
            'score_range': {
                'min': min(scores) if scores else 0,
                'max': max(scores) if scores else 0
            },
            'common_red_flags': sorted(red_flags_count.items(), key=lambda x: x[1], reverse=True)[:5],
            'common_positive_signals': sorted(positive_signals_count.items(), key=lambda x: x[1], reverse=True)[:5]
        }

    def generate_quality_report(self, website: Website, analysis: Optional[ContentAnalysis] = None) -> Dict:
        """Generate comprehensive quality report for a website"""
        try:
            quality_metrics = self.assess_website_quality(website, analysis)

            # Generate recommendations
            recommendations = self._generate_quality_recommendations(quality_metrics, website)

            # Create detailed breakdown
            breakdown = {
                'content_quality': {
                    'score': quality_metrics.content_quality,
                    'weight': self.quality_weights['content_quality'],
                    'contribution': quality_metrics.content_quality * self.quality_weights['content_quality']
                },
                'technical_health': {
                    'score': quality_metrics.technical_health,
                    'weight': self.quality_weights['technical_health'],
                    'contribution': quality_metrics.technical_health * self.quality_weights['technical_health']
                },
                'authority_signals': {
                    'score': quality_metrics.authority_signals,
                    'weight': self.quality_weights['authority_signals'],
                    'contribution': quality_metrics.authority_signals * self.quality_weights['authority_signals']
                },
                'trust_indicators': {
                    'score': quality_metrics.trust_indicators,
                    'weight': self.quality_weights['trust_indicators'],
                    'contribution': quality_metrics.trust_indicators * self.quality_weights['trust_indicators']
                },
                'spam_penalty': {
                    'score': quality_metrics.spam_score,
                    'weight': self.quality_weights['spam_penalty'],
                    'contribution': quality_metrics.spam_score * self.quality_weights['spam_penalty']
                }
            }

            return {
                'website_id': website.id,
                'domain': website.domain,
                'overall_score': quality_metrics.overall_score,
                'quality_level': quality_metrics.quality_level,
                'score_breakdown': breakdown,
                'red_flags': quality_metrics.red_flags,
                'positive_signals': quality_metrics.positive_signals,
                'recommendations': recommendations,
                'assessment_timestamp': datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error generating quality report: {str(e)}")
            return {}

    def _generate_quality_recommendations(self, metrics: QualityMetrics, website: Website) -> List[str]:
        """Generate recommendations to improve quality score"""
        recommendations = []

        # Content quality recommendations
        if metrics.content_quality < 6.0:
            recommendations.append("• Improve content quality with more detailed, well-structured articles")
            recommendations.append("• Focus on keyword optimization and content depth")

        # Technical health recommendations
        if metrics.technical_health < 6.0:
            recommendations.append("• Address technical SEO issues and improve site performance")
            recommendations.append("• Ensure mobile responsiveness and fast loading times")

        # Authority recommendations
        if metrics.authority_signals < 6.0:
            recommendations.append("• Build domain authority through quality backlinks")
            recommendations.append("• Maintain consistent content publishing schedule")

        # Trust recommendations
        if metrics.trust_indicators < 6.0:
            recommendations.append("• Add trust signals like SSL certificate and privacy policy")
            recommendations.append("• Include clear contact information and about page")

        # Spam-related recommendations
        if metrics.spam_score > 3.0:
            recommendations.append("• Review content for spam-like characteristics")
            recommendations.append("• Avoid excessive promotional language")

        # General recommendations based on quality level
        if metrics.overall_score < 5.0:
            recommendations.append("• Consider comprehensive site audit and improvement plan")
        elif metrics.overall_score < 7.0:
            recommendations.append("• Focus on incremental improvements across all quality factors")

        return recommendations
