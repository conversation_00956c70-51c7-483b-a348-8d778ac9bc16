"""
Test configuration and fixtures for LinkUp Plugin Backend
"""
import pytest
import tempfile
import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Set environment for testing
os.environ['FLASK_ENV'] = 'testing'
os.environ['FLASK_CONFIG'] = 'testing'

from app import create_app, db
from app.models.user import User
from app.models.website import Website
from app.models.api_key import ApiKey
from app.models.usage_stats import UsageStats
from app.models.analysis import ContentAnalysis
import factory
from datetime import datetime, timedelta


@pytest.fixture(scope='session')
def app():
    """Create application for testing"""
    # Create temporary database
    db_fd, db_path = tempfile.mkstemp()

    app = create_app('testing')
    app.config.update({
        'TESTING': True,
        'SQLALCHEMY_DATABASE_URI': f'sqlite:///{db_path}',
        'WTF_CSRF_ENABLED': False,
        'SECRET_KEY': 'test-secret-key',
        'JWT_SECRET_KEY': 'test-jwt-secret',
        'REDIS_URL': 'redis://localhost:6379/15',  # Use test database
    })

    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()


@pytest.fixture
def runner(app):
    """Create test CLI runner"""
    return app.test_cli_runner()


@pytest.fixture
def db_session(app):
    """Create database session for testing"""
    with app.app_context():
        yield db.session
        db.session.rollback()


# Factory classes for test data
class UserFactory(factory.alchemy.SQLAlchemyModelFactory):
    """Factory for creating test users"""
    
    class Meta:
        model = User
        sqlalchemy_session = db.session
        sqlalchemy_session_persistence = 'commit'
    
    email = factory.Sequence(lambda n: f'user{n}@example.com')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    company = factory.Faker('company')
    is_active = True
    is_verified = True
    plan = 'free'
    role = 'user'
    data_collection_consent = True
    analytics_consent = True
    
    @factory.post_generation
    def password(obj, create, extracted, **kwargs):
        if extracted:
            obj.set_password(extracted)
        else:
            obj.set_password('testpassword123')


class WebsiteFactory(factory.alchemy.SQLAlchemyModelFactory):
    """Factory for creating test websites"""
    
    class Meta:
        model = Website
        sqlalchemy_session = db.session
        sqlalchemy_session_persistence = 'commit'
    
    domain = factory.Faker('domain_name')
    title = factory.Faker('sentence', nb_words=4)
    description = factory.Faker('text', max_nb_chars=200)
    category = factory.Faker('random_element', elements=['technology', 'business', 'health', 'education'])
    language = 'en'
    status = 'active'
    
    @factory.lazy_attribute
    def url(self):
        return f'https://{self.domain}'


class ApiKeyFactory(factory.alchemy.SQLAlchemyModelFactory):
    """Factory for creating test API keys"""
    
    class Meta:
        model = ApiKey
        sqlalchemy_session = db.session
        sqlalchemy_session_persistence = 'commit'
    
    key_hash = factory.Faker('sha256')
    key_prefix = factory.Faker('lexify', text='lup_????????')
    description = factory.Faker('sentence')
    is_active = True
    rate_limit_per_hour = 1000
    rate_limit_per_day = 10000


class ContentAnalysisFactory(factory.alchemy.SQLAlchemyModelFactory):
    """Factory for creating test content analyses"""
    
    class Meta:
        model = ContentAnalysis
        sqlalchemy_session = db.session
        sqlalchemy_session_persistence = 'commit'
    
    content_hash = factory.Faker('sha256')
    quality_score = factory.Faker('pyfloat', min_value=1.0, max_value=10.0)
    readability_score = factory.Faker('pyfloat', min_value=0.0, max_value=100.0)
    
    @factory.lazy_attribute
    def keywords(self):
        return {
            'primary_keywords': [
                {'keyword': 'test keyword', 'frequency': 5, 'density': 2.5},
                {'keyword': 'example term', 'frequency': 3, 'density': 1.5}
            ]
        }
    
    @factory.lazy_attribute
    def categories(self):
        return {
            'primary_topics': [
                {'topic': 'technology', 'confidence': 0.8}
            ]
        }
    
    @factory.lazy_attribute
    def analysis_data(self):
        return {
            'basic_metrics': {
                'word_count': 500,
                'sentence_count': 25,
                'paragraph_count': 5
            },
            'quality_score': self.quality_score
        }


class UsageStatsFactory(factory.alchemy.SQLAlchemyModelFactory):
    """Factory for creating test usage statistics"""
    
    class Meta:
        model = UsageStats
        sqlalchemy_session = db.session
        sqlalchemy_session_persistence = 'commit'
    
    date = factory.Faker('date_this_month')
    api_requests = factory.Faker('random_int', min=0, max=100)
    backlinks_created = factory.Faker('random_int', min=0, max=10)
    content_analyzed = factory.Faker('random_int', min=0, max=5)
    organic_traffic = factory.Faker('random_int', min=0, max=1000)


@pytest.fixture
def user(db_session):
    """Create a test user"""
    return UserFactory()


@pytest.fixture
def admin_user(db_session):
    """Create a test admin user"""
    return UserFactory(role='admin', plan='agency')


@pytest.fixture
def website(db_session, user):
    """Create a test website"""
    return WebsiteFactory(user_id=user.id)


@pytest.fixture
def api_key(db_session, user, website):
    """Create a test API key"""
    return ApiKeyFactory(user_id=user.id, website_id=website.id)


@pytest.fixture
def content_analysis(db_session, website):
    """Create a test content analysis"""
    return ContentAnalysisFactory(website_id=website.id)


@pytest.fixture
def usage_stats(db_session, user, website):
    """Create test usage statistics"""
    return UsageStatsFactory(user_id=user.id, website_id=website.id)


@pytest.fixture
def auth_headers(user, api_key):
    """Create authentication headers for API requests"""
    return {
        'Authorization': f'Bearer lup_test_api_key_12345',
        'Content-Type': 'application/json'
    }


@pytest.fixture
def sample_content_data():
    """Sample content data for testing"""
    return {
        'title': 'Complete Guide to Web Development',
        'content': '''
        Web development is the process of creating websites and web applications. 
        It involves several technologies including HTML, CSS, and JavaScript. 
        Modern web development also includes frameworks like React, Vue, and Angular.
        
        Frontend development focuses on the user interface and user experience.
        Backend development handles server-side logic and database management.
        Full-stack developers work on both frontend and backend components.
        
        Key skills for web developers include:
        - Programming languages (JavaScript, Python, PHP)
        - Version control systems (Git)
        - Database management (SQL, NoSQL)
        - Web frameworks and libraries
        - Testing and debugging
        
        The web development industry continues to evolve with new technologies
        and best practices emerging regularly.
        ''',
        'excerpt': 'A comprehensive guide covering frontend, backend, and full-stack web development.',
        'url': 'https://example.com/web-development-guide',
        'categories': ['technology', 'programming'],
        'tags': ['web development', 'programming', 'javascript'],
        'language': 'en'
    }


@pytest.fixture
def mock_spacy_doc():
    """Mock spaCy document for testing"""
    class MockToken:
        def __init__(self, text, pos='NOUN', is_alpha=True, is_stop=False, lemma=None):
            self.text = text
            self.pos_ = pos
            self.is_alpha = is_alpha
            self.is_stop = is_stop
            self.is_space = False
            self.is_punct = False
            self.lemma_ = lemma or text.lower()
            self.has_vector = True
            self.vector = [0.1] * 96  # Mock vector
    
    class MockSent:
        def __init__(self, text):
            self.text = text
            self.start = 0
            self.end = len(text)
    
    class MockDoc:
        def __init__(self, text):
            self.text = text
            self.tokens = [
                MockToken('Web', 'NOUN'),
                MockToken('development', 'NOUN'),
                MockToken('is', 'VERB', is_stop=True),
                MockToken('the', 'DET', is_stop=True),
                MockToken('process', 'NOUN'),
            ]
            self.sents = [MockSent('Web development is the process.')]
            self.ents = []
            self.noun_chunks = []
        
        def __iter__(self):
            return iter(self.tokens)
    
    return MockDoc


@pytest.fixture
def mock_redis(monkeypatch):
    """Mock Redis for testing"""
    class MockRedis:
        def __init__(self):
            self.data = {}
        
        def get(self, key):
            return self.data.get(key)
        
        def set(self, key, value, ex=None):
            self.data[key] = value
            return True
        
        def delete(self, key):
            return self.data.pop(key, None) is not None
        
        def exists(self, key):
            return key in self.data
        
        def incr(self, key):
            self.data[key] = self.data.get(key, 0) + 1
            return self.data[key]
    
    mock_redis = MockRedis()
    monkeypatch.setattr('redis.Redis', lambda **kwargs: mock_redis)
    return mock_redis


@pytest.fixture
def mock_celery_task(monkeypatch):
    """Mock Celery tasks for testing"""
    class MockTask:
        def __init__(self, task_id='test-task-id'):
            self.id = task_id
            self.state = 'PENDING'
            self.result = None
        
        def delay(self, *args, **kwargs):
            return self
        
        def apply_async(self, *args, **kwargs):
            return self
    
    def mock_task_decorator(func):
        func.delay = lambda *args, **kwargs: MockTask()
        func.apply_async = lambda *args, **kwargs: MockTask()
        return func
    
    monkeypatch.setattr('app.celery.task', mock_task_decorator)
    return MockTask


# Helper functions for tests
def create_test_user(email='<EMAIL>', password='testpass123', **kwargs):
    """Helper function to create test user"""
    user_data = {
        'email': email,
        'first_name': 'Test',
        'last_name': 'User',
        'is_active': True,
        'is_verified': True,
        'plan': 'free',
        'role': 'user',
        **kwargs
    }
    user = User(**user_data)
    user.set_password(password)
    db.session.add(user)
    db.session.commit()
    return user


def create_test_website(user_id, domain='example.com', **kwargs):
    """Helper function to create test website"""
    website_data = {
        'user_id': user_id,
        'domain': domain,
        'title': 'Test Website',
        'description': 'A test website',
        'category': 'technology',
        'language': 'en',
        'status': 'active',
        'url': f'https://{domain}',
        **kwargs
    }
    website = Website(**website_data)
    db.session.add(website)
    db.session.commit()
    return website


def assert_response_success(response, expected_status=200):
    """Helper to assert successful API response"""
    assert response.status_code == expected_status
    data = response.get_json()
    assert data is not None
    assert data.get('success') is True
    return data


def assert_response_error(response, expected_status=400):
    """Helper to assert error API response"""
    assert response.status_code == expected_status
    data = response.get_json()
    assert data is not None
    assert data.get('success') is False
    assert 'error' in data
    return data
