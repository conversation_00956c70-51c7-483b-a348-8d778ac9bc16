#!/usr/bin/env python3
"""
Automated Code Review System for LinkUp Plugin
Performs comprehensive code review and suggests refactoring opportunities
"""
import os
import sys
import ast
import re
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
import subprocess


class CodeReviewer:
    """Automated code review system"""

    def __init__(self, project_root=None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.review_findings = []
        self.refactoring_suggestions = []

        # Code quality metrics
        self.metrics = {
            'cyclomatic_complexity': {},
            'code_duplication': {},
            'maintainability_index': {},
            'test_coverage': {},
            'documentation_coverage': {}
        }

    def perform_code_review(self):
        """Perform comprehensive code review"""
        print("🔍 Performing Automated Code Review...")
        print("=" * 40)

        # Analyze different file types
        self.review_python_files()
        self.review_javascript_files()
        self.review_php_files()

        # Analyze architecture and design
        self.analyze_architecture()
        self.analyze_design_patterns()

        # Check coding standards
        self.check_coding_standards()

        # Generate metrics
        self.calculate_metrics()

        # Generate review report
        self.generate_review_report()

        print(f"✅ Review completed: {len(self.review_findings)} findings")
        print(f"💡 Refactoring suggestions: {len(self.refactoring_suggestions)}")

    def review_python_files(self):
        """Review Python files for code quality"""
        python_files = list(self.project_root.rglob("*.py"))

        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Parse AST for analysis
                tree = ast.parse(content)

                # Analyze various aspects
                self._analyze_python_complexity(file_path, tree, content)
                self._analyze_python_structure(file_path, tree, content)
                self._analyze_python_naming(file_path, tree, content)
                self._analyze_python_best_practices(file_path, tree, content)

            except Exception as e:
                self._add_finding(
                    'parse_error',
                    'HIGH',
                    str(file_path),
                    0,
                    f"Failed to parse Python file: {e}"
                )

    def _analyze_python_complexity(self, file_path, tree, content):
        """Analyze Python code complexity"""
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                complexity = self._calculate_cyclomatic_complexity(node)

                if complexity > 10:
                    self._add_finding(
                        'high_complexity',
                        'MEDIUM',
                        str(file_path),
                        node.lineno,
                        f"Function '{node.name}' has high cyclomatic complexity: {complexity}"
                    )

                    self._add_refactoring_suggestion(
                        'extract_method',
                        str(file_path),
                        node.lineno,
                        f"Consider breaking down '{node.name}' into smaller functions"
                    )

                # Check function length
                if hasattr(node, 'end_lineno'):
                    func_length = node.end_lineno - node.lineno
                    if func_length > 50:
                        self._add_finding(
                            'long_function',
                            'MEDIUM',
                            str(file_path),
                            node.lineno,
                            f"Function '{node.name}' is {func_length} lines long"
                        )

    def _calculate_cyclomatic_complexity(self, node):
        """Calculate cyclomatic complexity of a function"""
        complexity = 1  # Base complexity

        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, ast.With):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1

        return complexity

    def _analyze_python_structure(self, file_path, tree, content):
        """Analyze Python code structure"""
        # Check for proper class structure
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                # Check for too many methods
                methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
                if len(methods) > 20:
                    self._add_finding(
                        'large_class',
                        'MEDIUM',
                        str(file_path),
                        node.lineno,
                        f"Class '{node.name}' has {len(methods)} methods (consider splitting)"
                    )

                # Check for proper __init__ method
                has_init = any(m.name == '__init__' for m in methods)
                if not has_init and len(methods) > 0:
                    self._add_finding(
                        'missing_init',
                        'LOW',
                        str(file_path),
                        node.lineno,
                        f"Class '{node.name}' missing __init__ method"
                    )

    def _analyze_python_naming(self, file_path, tree, content):
        """Analyze Python naming conventions"""
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # Check function naming (should be snake_case)
                if not re.match(r'^[a-z_][a-z0-9_]*$', node.name) and not node.name.startswith('__'):
                    self._add_finding(
                        'naming_convention',
                        'LOW',
                        str(file_path),
                        node.lineno,
                        f"Function '{node.name}' doesn't follow snake_case convention"
                    )

            elif isinstance(node, ast.ClassDef):
                # Check class naming (should be PascalCase)
                if not re.match(r'^[A-Z][a-zA-Z0-9]*$', node.name):
                    self._add_finding(
                        'naming_convention',
                        'LOW',
                        str(file_path),
                        node.lineno,
                        f"Class '{node.name}' doesn't follow PascalCase convention"
                    )

    def _analyze_python_best_practices(self, file_path, tree, content):
        """Analyze Python best practices"""
        # Check for proper exception handling
        for node in ast.walk(tree):
            if isinstance(node, ast.ExceptHandler):
                if node.type is None:  # Bare except
                    self._add_finding(
                        'bare_except',
                        'MEDIUM',
                        str(file_path),
                        node.lineno,
                        "Bare except clause - specify exception type"
                    )

        # Check for proper imports
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.strip().startswith('from') and '*' in line:
                self._add_finding(
                    'wildcard_import',
                    'MEDIUM',
                    str(file_path),
                    i + 1,
                    "Avoid wildcard imports (from module import *)"
                )

    def review_javascript_files(self):
        """Review JavaScript files for code quality"""
        js_files = list(self.project_root.rglob("*.js"))

        for file_path in js_files:
            if self._should_skip_file(file_path):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                self._analyze_js_quality(file_path, content)

            except Exception as e:
                self._add_finding(
                    'file_read_error',
                    'LOW',
                    str(file_path),
                    0,
                    f"Failed to read JavaScript file: {e}"
                )

    def _analyze_js_quality(self, file_path, content):
        """Analyze JavaScript code quality"""
        lines = content.split('\n')

        for i, line in enumerate(lines):
            line_num = i + 1
            stripped = line.strip()

            # Check for var usage
            if re.search(r'\bvar\s+\w+', stripped):
                self._add_finding(
                    'var_usage',
                    'LOW',
                    str(file_path),
                    line_num,
                    "Use 'let' or 'const' instead of 'var'"
                )

            # Check for == usage
            if '==' in stripped and '===' not in stripped:
                self._add_finding(
                    'loose_equality',
                    'MEDIUM',
                    str(file_path),
                    line_num,
                    "Use strict equality (===) instead of loose equality (==)"
                )

            # Check for console.log in production code
            if 'console.log' in stripped:
                self._add_finding(
                    'debug_code',
                    'LOW',
                    str(file_path),
                    line_num,
                    "Remove console.log statements from production code"
                )

    def review_php_files(self):
        """Review PHP files for code quality"""
        php_files = list(self.project_root.rglob("*.php"))

        for file_path in php_files:
            if self._should_skip_file(file_path):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                self._analyze_php_quality(file_path, content)

            except Exception as e:
                self._add_finding(
                    'file_read_error',
                    'LOW',
                    str(file_path),
                    0,
                    f"Failed to read PHP file: {e}"
                )

    def _analyze_php_quality(self, file_path, content):
        """Analyze PHP code quality"""
        lines = content.split('\n')

        for i, line in enumerate(lines):
            line_num = i + 1
            stripped = line.strip()

            # Check for deprecated functions
            deprecated_functions = ['mysql_connect', 'mysql_query', 'ereg', 'split']
            for func in deprecated_functions:
                if func in stripped:
                    self._add_finding(
                        'deprecated_function',
                        'HIGH',
                        str(file_path),
                        line_num,
                        f"Deprecated function '{func}' used"
                    )

            # Check for proper error handling
            if 'die(' in stripped or 'exit(' in stripped:
                self._add_finding(
                    'improper_error_handling',
                    'MEDIUM',
                    str(file_path),
                    line_num,
                    "Use proper error handling instead of die()/exit()"
                )

    def analyze_architecture(self):
        """Analyze overall architecture"""
        # Check directory structure
        self._check_directory_structure()

        # Check separation of concerns
        self._check_separation_of_concerns()

        # Check dependency management
        self._check_dependency_management()

    def _check_directory_structure(self):
        """Check if directory structure follows best practices"""
        expected_dirs = ['app', 'tests', 'config', 'docs']
        missing_dirs = []

        for dir_name in expected_dirs:
            if not (self.project_root / dir_name).exists():
                missing_dirs.append(dir_name)

        if missing_dirs:
            self._add_finding(
                'directory_structure',
                'LOW',
                str(self.project_root),
                0,
                f"Missing recommended directories: {', '.join(missing_dirs)}"
            )

    def _check_separation_of_concerns(self):
        """Check separation of concerns"""
        # Look for mixed concerns in files
        python_files = list(self.project_root.rglob("*.py"))

        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Check if database and business logic are mixed
                has_db_code = any(keyword in content.lower() for keyword in ['select', 'insert', 'update', 'delete', 'query'])
                has_business_logic = any(keyword in content.lower() for keyword in ['calculate', 'process', 'validate', 'transform'])

                if has_db_code and has_business_logic:
                    self._add_finding(
                        'mixed_concerns',
                        'MEDIUM',
                        str(file_path),
                        0,
                        "File contains both database and business logic (consider separation)"
                    )

            except Exception:
                continue

    def _check_dependency_management(self):
        """Check dependency management"""
        # Check for requirements.txt or similar
        dependency_files = ['requirements.txt', 'package.json', 'composer.json']
        found_files = []

        for dep_file in dependency_files:
            if (self.project_root / dep_file).exists():
                found_files.append(dep_file)

        if not found_files:
            self._add_finding(
                'missing_dependency_file',
                'MEDIUM',
                str(self.project_root),
                0,
                "No dependency management file found (requirements.txt, package.json, etc.)"
            )

    def analyze_design_patterns(self):
        """Analyze design patterns usage"""
        python_files = list(self.project_root.rglob("*.py"))

        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                tree = ast.parse(content)
                self._check_design_patterns(file_path, tree, content)

            except Exception:
                continue

    def _check_design_patterns(self, file_path, tree, content):
        """Check for proper design pattern usage"""
        # Check for Singleton pattern misuse
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                # Look for potential singleton implementations
                methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
                method_names = [m.name for m in methods]

                if 'getInstance' in method_names or '__new__' in method_names:
                    self._add_finding(
                        'potential_singleton',
                        'LOW',
                        str(file_path),
                        node.lineno,
                        f"Class '{node.name}' may be implementing Singleton pattern - consider if necessary"
                    )

    def check_coding_standards(self):
        """Check coding standards compliance"""
        # Check Python PEP 8 compliance
        self._check_pep8_compliance()

        # Check documentation standards
        self._check_documentation_standards()

        # Check naming conventions
        self._check_naming_conventions()

    def _check_pep8_compliance(self):
        """Check PEP 8 compliance"""
        python_files = list(self.project_root.rglob("*.py"))

        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                for i, line in enumerate(lines):
                    line_num = i + 1

                    # Check line length
                    if len(line.rstrip()) > 88:  # Slightly more lenient than PEP 8's 79
                        self._add_finding(
                            'line_too_long',
                            'LOW',
                            str(file_path),
                            line_num,
                            f"Line too long ({len(line.rstrip())} characters)"
                        )

                    # Check trailing whitespace
                    if line.rstrip() != line.rstrip('\n'):
                        self._add_finding(
                            'trailing_whitespace',
                            'LOW',
                            str(file_path),
                            line_num,
                            "Trailing whitespace found"
                        )

            except Exception:
                continue

    def _check_documentation_standards(self):
        """Check documentation standards"""
        python_files = list(self.project_root.rglob("*.py"))

        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                tree = ast.parse(content)

                # Check module docstring
                module_docstring = ast.get_docstring(tree)
                if not module_docstring:
                    self._add_finding(
                        'missing_module_docstring',
                        'LOW',
                        str(file_path),
                        1,
                        "Module missing docstring"
                    )

                # Check function and class docstrings
                for node in ast.walk(tree):
                    if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                        if not ast.get_docstring(node):
                            self._add_finding(
                                'missing_docstring',
                                'LOW',
                                str(file_path),
                                node.lineno,
                                f"{type(node).__name__} '{node.name}' missing docstring"
                            )

            except Exception:
                continue

    def _check_naming_conventions(self):
        """Check naming conventions across all files"""
        # This is already partially implemented in _analyze_python_naming
        # Could be extended for other languages
        pass

    def calculate_metrics(self):
        """Calculate code quality metrics"""
        self._calculate_complexity_metrics()
        self._calculate_duplication_metrics()
        self._calculate_maintainability_metrics()

    def _calculate_complexity_metrics(self):
        """Calculate complexity metrics"""
        python_files = list(self.project_root.rglob("*.py"))
        total_complexity = 0
        function_count = 0

        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                tree = ast.parse(content)
                file_complexity = 0
                file_functions = 0

                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        complexity = self._calculate_cyclomatic_complexity(node)
                        file_complexity += complexity
                        file_functions += 1
                        total_complexity += complexity
                        function_count += 1

                if file_functions > 0:
                    self.metrics['cyclomatic_complexity'][str(file_path)] = {
                        'average': file_complexity / file_functions,
                        'total': file_complexity,
                        'functions': file_functions
                    }

            except Exception:
                continue

        if function_count > 0:
            self.metrics['cyclomatic_complexity']['overall'] = {
                'average': total_complexity / function_count,
                'total': total_complexity,
                'functions': function_count
            }

    def _calculate_duplication_metrics(self):
        """Calculate code duplication metrics"""
        # Simple duplication detection based on similar lines
        python_files = list(self.project_root.rglob("*.py"))

        all_lines = []
        line_counts = {}

        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                for line in lines:
                    stripped = line.strip()
                    if len(stripped) > 10 and not stripped.startswith('#'):
                        all_lines.append(stripped)
                        line_counts[stripped] = line_counts.get(stripped, 0) + 1

            except Exception:
                continue

        duplicated_lines = sum(count - 1 for count in line_counts.values() if count > 1)
        total_lines = len(all_lines)

        if total_lines > 0:
            duplication_percentage = (duplicated_lines / total_lines) * 100
            self.metrics['code_duplication'] = {
                'percentage': duplication_percentage,
                'duplicated_lines': duplicated_lines,
                'total_lines': total_lines
            }

    def _calculate_maintainability_metrics(self):
        """Calculate maintainability metrics"""
        # Simple maintainability index based on various factors
        complexity_score = 0
        duplication_score = 0
        documentation_score = 0

        # Factor in complexity
        if 'overall' in self.metrics['cyclomatic_complexity']:
            avg_complexity = self.metrics['cyclomatic_complexity']['overall']['average']
            complexity_score = max(0, 100 - (avg_complexity * 10))

        # Factor in duplication
        if self.metrics['code_duplication']:
            duplication_percentage = self.metrics['code_duplication']['percentage']
            duplication_score = max(0, 100 - (duplication_percentage * 2))

        # Factor in documentation
        total_findings = len(self.review_findings)
        doc_findings = len([f for f in self.review_findings if 'docstring' in f['type']])
        if total_findings > 0:
            documentation_score = max(0, 100 - ((doc_findings / total_findings) * 100))
        else:
            documentation_score = 100

        # Calculate overall maintainability index
        maintainability_index = (complexity_score + duplication_score + documentation_score) / 3

        self.metrics['maintainability_index'] = {
            'overall': maintainability_index,
            'complexity_score': complexity_score,
            'duplication_score': duplication_score,
            'documentation_score': documentation_score
        }

    def _should_skip_file(self, file_path):
        """Check if file should be skipped"""
        skip_patterns = [
            'venv', '__pycache__', '.git', 'node_modules',
            'vendor', '.pytest_cache', 'htmlcov'
        ]
        return any(pattern in str(file_path) for pattern in skip_patterns)

    def _add_finding(self, finding_type, severity, file_path, line_number, description):
        """Add code review finding"""
        finding = {
            'id': f"CR-{len(self.review_findings) + 1:04d}",
            'type': finding_type,
            'severity': severity,
            'file': file_path,
            'line': line_number,
            'description': description,
            'timestamp': datetime.now().isoformat()
        }
        self.review_findings.append(finding)

    def _add_refactoring_suggestion(self, suggestion_type, file_path, line_number, description):
        """Add refactoring suggestion"""
        suggestion = {
            'id': f"RF-{len(self.refactoring_suggestions) + 1:04d}",
            'type': suggestion_type,
            'file': file_path,
            'line': line_number,
            'description': description,
            'timestamp': datetime.now().isoformat()
        }
        self.refactoring_suggestions.append(suggestion)

    def generate_review_report(self):
        """Generate comprehensive code review report"""
        print("\n📋 CODE REVIEW REPORT")
        print("=" * 50)

        # Summary statistics
        total_findings = len(self.review_findings)
        critical_findings = len([f for f in self.review_findings if f['severity'] == 'HIGH'])
        medium_findings = len([f for f in self.review_findings if f['severity'] == 'MEDIUM'])
        low_findings = len([f for f in self.review_findings if f['severity'] == 'LOW'])

        print(f"Total Findings: {total_findings}")
        print(f"Critical (High): {critical_findings}")
        print(f"Medium: {medium_findings}")
        print(f"Low: {low_findings}")
        print(f"Refactoring Suggestions: {len(self.refactoring_suggestions)}")
        print()

        # Code quality metrics
        if self.metrics['maintainability_index']:
            mi = self.metrics['maintainability_index']['overall']
            print(f"Maintainability Index: {mi:.1f}/100")

            if mi >= 80:
                print("🟢 Excellent maintainability")
            elif mi >= 60:
                print("🟡 Good maintainability")
            elif mi >= 40:
                print("🟠 Fair maintainability - improvements needed")
            else:
                print("🔴 Poor maintainability - significant refactoring needed")
            print()

        # Complexity metrics
        if 'overall' in self.metrics['cyclomatic_complexity']:
            avg_complexity = self.metrics['cyclomatic_complexity']['overall']['average']
            print(f"Average Cyclomatic Complexity: {avg_complexity:.1f}")

            if avg_complexity <= 5:
                print("🟢 Low complexity - easy to maintain")
            elif avg_complexity <= 10:
                print("🟡 Moderate complexity")
            else:
                print("🔴 High complexity - consider refactoring")
            print()

        # Code duplication
        if self.metrics['code_duplication']:
            dup_percentage = self.metrics['code_duplication']['percentage']
            print(f"Code Duplication: {dup_percentage:.1f}%")

            if dup_percentage <= 5:
                print("🟢 Low duplication")
            elif dup_percentage <= 15:
                print("🟡 Moderate duplication")
            else:
                print("🔴 High duplication - refactoring recommended")
            print()

        # Top issues by type
        finding_types = {}
        for finding in self.review_findings:
            finding_type = finding['type']
            finding_types[finding_type] = finding_types.get(finding_type, 0) + 1

        if finding_types:
            print("Top Issue Types:")
            print("-" * 16)
            for issue_type, count in sorted(finding_types.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"• {issue_type.replace('_', ' ').title()}: {count}")
            print()

        # Critical findings
        critical_findings_list = [f for f in self.review_findings if f['severity'] == 'HIGH']
        if critical_findings_list:
            print("🚨 CRITICAL FINDINGS:")
            print("-" * 20)
            for finding in critical_findings_list[:5]:  # Show top 5
                print(f"• {finding['description']}")
                print(f"  File: {finding['file']}:{finding['line']}")
                print()

        # Refactoring suggestions
        if self.refactoring_suggestions:
            print("💡 TOP REFACTORING SUGGESTIONS:")
            print("-" * 32)
            for suggestion in self.refactoring_suggestions[:5]:  # Show top 5
                print(f"• {suggestion['description']}")
                print(f"  File: {suggestion['file']}:{suggestion['line']}")
                print()

        # Overall assessment
        print("📊 OVERALL ASSESSMENT:")
        print("-" * 22)

        if critical_findings == 0 and medium_findings <= 5:
            assessment = "🟢 EXCELLENT - Code quality is high"
            recommendation = "Minor improvements recommended"
        elif critical_findings <= 2 and medium_findings <= 15:
            assessment = "🟡 GOOD - Code quality is acceptable"
            recommendation = "Address critical issues and consider refactoring suggestions"
        elif critical_findings <= 5 and medium_findings <= 30:
            assessment = "🟠 FAIR - Code quality needs improvement"
            recommendation = "Prioritize critical issues and plan refactoring"
        else:
            assessment = "🔴 POOR - Significant code quality issues"
            recommendation = "Comprehensive refactoring required before production"

        print(f"Assessment: {assessment}")
        print(f"Recommendation: {recommendation}")
        print()

        # Save detailed report
        report_data = {
            'summary': {
                'total_findings': total_findings,
                'critical_findings': critical_findings,
                'medium_findings': medium_findings,
                'low_findings': low_findings,
                'refactoring_suggestions': len(self.refactoring_suggestions),
                'assessment': assessment,
                'recommendation': recommendation
            },
            'metrics': self.metrics,
            'findings': self.review_findings,
            'refactoring_suggestions': self.refactoring_suggestions,
            'generated_at': datetime.now().isoformat()
        }

        with open('code_review_report.json', 'w') as f:
            json.dump(report_data, f, indent=2)

        print("=" * 50)
        print(f"Code review completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("📄 Detailed report saved to code_review_report.json")


class RefactoringEngine:
    """Automated refactoring engine"""

    def __init__(self, project_root=None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.refactorings_applied = []

    def apply_safe_refactorings(self, review_findings):
        """Apply safe automated refactorings"""
        print("\n🔧 Applying Safe Refactorings...")
        print("-" * 32)

        for finding in review_findings:
            if finding['severity'] == 'LOW' and self._is_safe_to_refactor(finding):
                if self._apply_refactoring(finding):
                    self.refactorings_applied.append(finding)

        print(f"✅ Applied {len(self.refactorings_applied)} safe refactorings")

    def _is_safe_to_refactor(self, finding):
        """Check if finding is safe to automatically refactor"""
        safe_types = [
            'trailing_whitespace',
            'var_usage',
            'debug_code'
        ]
        return finding['type'] in safe_types

    def _apply_refactoring(self, finding):
        """Apply specific refactoring"""
        try:
            file_path = Path(finding['file'])

            if not file_path.exists():
                return False

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            original_content = content

            # Apply refactoring based on finding type
            if finding['type'] == 'trailing_whitespace':
                lines = content.split('\n')
                lines = [line.rstrip() for line in lines]
                content = '\n'.join(lines)

            elif finding['type'] == 'var_usage':
                content = re.sub(r'\bvar\s+(\w+)\s*=', r'let \1 =', content)

            elif finding['type'] == 'debug_code':
                content = re.sub(r'console\.log\([^)]*\);\s*\n?', '', content)
                content = re.sub(r'print\s*\([^)]*debug[^)]*\)\s*\n?', '', content, flags=re.IGNORECASE)

            # Write back if changes were made
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True

        except Exception as e:
            print(f"Failed to apply refactoring for {finding['id']}: {e}")

        return False


def main():
    """Main function to run code review"""
    import argparse

    parser = argparse.ArgumentParser(description='Run automated code review')
    parser.add_argument('--project-root', help='Project root directory')
    parser.add_argument('--apply-refactorings', action='store_true', help='Apply safe refactorings')

    args = parser.parse_args()

    # Run code review
    reviewer = CodeReviewer(args.project_root)
    reviewer.perform_code_review()

    # Apply refactorings if requested
    if args.apply_refactorings:
        refactoring_engine = RefactoringEngine(args.project_root)
        refactoring_engine.apply_safe_refactorings(reviewer.review_findings)

    # Exit with appropriate code
    critical_findings = len([f for f in reviewer.review_findings if f['severity'] == 'HIGH'])
    exit(0 if critical_findings == 0 else 1)


if __name__ == '__main__':
    main()