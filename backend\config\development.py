"""
Development configuration for LinkUp Plugin Backend
"""
import os
from config.base import BaseConfig


class DevelopmentConfig(BaseConfig):
    """Development configuration"""
    
    DEBUG = True
    TESTING = False
    
    # Database configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://linkup_user:linkup_password@localhost/linkup_dev'
    
    # Enable query logging in development
    SQLALCHEMY_ECHO = True
    
    # Relaxed CORS for development
    CORS_ORIGINS = ['*']
    
    # Development-specific feature flags
    FEATURE_FLAGS = {
        **BaseConfig.FEATURE_FLAGS,
        'DEBUG_MODE': True,
        'MOCK_EXTERNAL_SERVICES': True
    }
    
    @staticmethod
    def init_app(app):
        BaseConfig.init_app(app)
        
        # Development-specific initialization
        import logging
        logging.basicConfig(level=logging.DEBUG)
