#!/usr/bin/env python3
"""
Code Quality Improvements for LinkUp Plugin
Implements code refactoring and quality enhancements
"""
import os
import sys
import re
import ast
from pathlib import Path
from typing import List, Dict, Any


class CodeQualityImprover:
    """Code quality improvement system"""
    
    def __init__(self, project_root=None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.improvements_applied = []
    
    def improve_code_quality(self):
        """Apply code quality improvements"""
        print("✨ Improving Code Quality...")
        print("=" * 30)
        
        # Apply various improvements
        self.improve_python_code()
        self.improve_javascript_code()
        self.improve_php_code()
        self.add_missing_docstrings()
        self.optimize_imports()
        self.standardize_formatting()
        
        print(f"✅ Applied {len(self.improvements_applied)} improvements")
        self.generate_improvement_report()
    
    def improve_python_code(self):
        """Improve Python code quality"""
        python_files = list(self.project_root.rglob("*.py"))
        
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # Apply Python-specific improvements
                content = self._improve_python_imports(content)
                content = self._improve_python_formatting(content)
                content = self._improve_python_logic(content)
                
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.improvements_applied.append({
                        'file': str(file_path),
                        'type': 'python_improvements',
                        'description': 'Applied Python code quality improvements'
                    })
            
            except Exception as e:
                print(f"Failed to improve {file_path}: {e}")
    
    def _improve_python_imports(self, content):
        """Improve Python import statements"""
        lines = content.split('\n')
        improved_lines = []
        
        import_lines = []
        from_import_lines = []
        other_lines = []
        
        in_imports = True
        
        for line in lines:
            stripped = line.strip()
            
            if stripped.startswith('import ') and not stripped.startswith('import '):
                if in_imports:
                    import_lines.append(line)
                else:
                    other_lines.append(line)
            elif stripped.startswith('from ') and ' import ' in stripped:
                if in_imports:
                    from_import_lines.append(line)
                else:
                    other_lines.append(line)
            else:
                if stripped and not stripped.startswith('#') and not stripped.startswith('"""'):
                    in_imports = False
                other_lines.append(line)
        
        # Sort imports
        import_lines.sort()
        from_import_lines.sort()
        
        # Rebuild content
        if import_lines or from_import_lines:
            improved_lines.extend(import_lines)
            if import_lines and from_import_lines:
                improved_lines.append('')
            improved_lines.extend(from_import_lines)
            if import_lines or from_import_lines:
                improved_lines.append('')
        
        improved_lines.extend(other_lines)
        
        return '\n'.join(improved_lines)
    
    def _improve_python_formatting(self, content):
        """Improve Python code formatting"""
        # Add spaces around operators
        content = re.sub(r'(\w)=(\w)', r'\1 = \2', content)
        content = re.sub(r'(\w)\+(\w)', r'\1 + \2', content)
        content = re.sub(r'(\w)-(\w)', r'\1 - \2', content)
        content = re.sub(r'(\w)\*(\w)', r'\1 * \2', content)
        content = re.sub(r'(\w)/(\w)', r'\1 / \2', content)
        
        # Fix multiple blank lines
        content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
        
        # Add blank lines before class and function definitions
        content = re.sub(r'\n(class\s+\w+)', r'\n\n\1', content)
        content = re.sub(r'\n(def\s+\w+)', r'\n\n\1', content)
        
        return content
    
    def _improve_python_logic(self, content):
        """Improve Python logic and patterns"""
        # Replace string concatenation with f-strings (simple cases)
        content = re.sub(
            r'"([^"]*)" \+ str\((\w+)\) \+ "([^"]*)"',
            r'f"\1{\2}\3"',
            content
        )
        
        # Replace .format() with f-strings (simple cases)
        content = re.sub(
            r'"([^"]*)\{(\w+)\}([^"]*)".format\(\2=(\w+)\)',
            r'f"\1{\4}\3"',
            content
        )
        
        # Improve boolean comparisons
        content = re.sub(r'== True\b', ' is True', content)
        content = re.sub(r'== False\b', ' is False', content)
        content = re.sub(r'!= True\b', ' is not True', content)
        content = re.sub(r'!= False\b', ' is not False', content)
        
        return content
    
    def improve_javascript_code(self):
        """Improve JavaScript code quality"""
        js_files = list(self.project_root.rglob("*.js"))
        
        for file_path in js_files:
            if self._should_skip_file(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # Apply JavaScript improvements
                content = self._improve_js_syntax(content)
                content = self._improve_js_formatting(content)
                
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.improvements_applied.append({
                        'file': str(file_path),
                        'type': 'javascript_improvements',
                        'description': 'Applied JavaScript code quality improvements'
                    })
            
            except Exception as e:
                print(f"Failed to improve {file_path}: {e}")
    
    def _improve_js_syntax(self, content):
        """Improve JavaScript syntax"""
        # Replace var with let/const
        content = re.sub(r'\bvar\s+(\w+)\s*=\s*([^;]+);', r'const \1 = \2;', content)
        
        # Use strict equality
        content = re.sub(r'([^=!])==([^=])', r'\1===\2', content)
        content = re.sub(r'([^=!])!=([^=])', r'\1!==\2', content)
        
        # Use arrow functions for simple callbacks
        content = re.sub(
            r'function\s*\(([^)]*)\)\s*\{\s*return\s+([^;]+);\s*\}',
            r'(\1) => \2',
            content
        )
        
        return content
    
    def _improve_js_formatting(self, content):
        """Improve JavaScript formatting"""
        # Add semicolons where missing (simple cases)
        content = re.sub(r'(\w+)\n', r'\1;\n', content)
        
        # Fix spacing around operators
        content = re.sub(r'(\w)\+(\w)', r'\1 + \2', content)
        content = re.sub(r'(\w)-(\w)', r'\1 - \2', content)
        
        return content
    
    def improve_php_code(self):
        """Improve PHP code quality"""
        php_files = list(self.project_root.rglob("*.php"))
        
        for file_path in php_files:
            if self._should_skip_file(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # Apply PHP improvements
                content = self._improve_php_syntax(content)
                content = self._improve_php_security(content)
                
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.improvements_applied.append({
                        'file': str(file_path),
                        'type': 'php_improvements',
                        'description': 'Applied PHP code quality improvements'
                    })
            
            except Exception as e:
                print(f"Failed to improve {file_path}: {e}")
    
    def _improve_php_syntax(self, content):
        """Improve PHP syntax"""
        # Use short array syntax
        content = re.sub(r'\barray\s*\(', '[', content)
        content = re.sub(r'\)\s*;', '];', content)
        
        # Use null coalescing operator
        content = re.sub(
            r'isset\(\$(\w+)\)\s*\?\s*\$\1\s*:\s*([^;]+)',
            r'$\1 ?? \2',
            content
        )
        
        return content
    
    def _improve_php_security(self, content):
        """Improve PHP security"""
        # Add input sanitization reminders
        if '$_GET' in content or '$_POST' in content:
            if 'sanitize' not in content.lower():
                # Add comment reminder
                content = "<?php\n// TODO: Ensure all user input is properly sanitized\n" + content.lstrip('<?php\n')
        
        return content
    
    def add_missing_docstrings(self):
        """Add missing docstrings to functions and classes"""
        python_files = list(self.project_root.rglob("*.py"))
        
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                lines = content.split('\n')
                
                # Find functions and classes without docstrings
                missing_docstrings = []
                
                for node in ast.walk(tree):
                    if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                        if not ast.get_docstring(node):
                            missing_docstrings.append({
                                'type': type(node).__name__,
                                'name': node.name,
                                'line': node.lineno
                            })
                
                if missing_docstrings:
                    self.improvements_applied.append({
                        'file': str(file_path),
                        'type': 'missing_docstrings',
                        'description': f'Found {len(missing_docstrings)} items missing docstrings',
                        'details': missing_docstrings
                    })
            
            except Exception as e:
                continue
    
    def optimize_imports(self):
        """Optimize import statements"""
        python_files = list(self.project_root.rglob("*.py"))
        
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find unused imports (basic detection)
                import_lines = []
                code_lines = []
                
                for line in content.split('\n'):
                    if line.strip().startswith(('import ', 'from ')):
                        import_lines.append(line)
                    else:
                        code_lines.append(line)
                
                code_content = '\n'.join(code_lines)
                unused_imports = []
                
                for import_line in import_lines:
                    # Extract imported names
                    if import_line.strip().startswith('import '):
                        module_name = import_line.split('import ')[1].split(' as ')[0].strip()
                        if module_name not in code_content:
                            unused_imports.append(import_line)
                
                if unused_imports:
                    self.improvements_applied.append({
                        'file': str(file_path),
                        'type': 'unused_imports',
                        'description': f'Found {len(unused_imports)} potentially unused imports',
                        'details': unused_imports
                    })
            
            except Exception as e:
                continue
    
    def standardize_formatting(self):
        """Standardize code formatting"""
        all_files = (
            list(self.project_root.rglob("*.py")) +
            list(self.project_root.rglob("*.js")) +
            list(self.project_root.rglob("*.php"))
        )
        
        formatting_issues = 0
        
        for file_path in all_files:
            if self._should_skip_file(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # Standardize line endings
                content = content.replace('\r\n', '\n').replace('\r', '\n')
                
                # Remove trailing whitespace
                lines = content.split('\n')
                lines = [line.rstrip() for line in lines]
                content = '\n'.join(lines)
                
                # Ensure file ends with newline
                if content and not content.endswith('\n'):
                    content += '\n'
                
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    formatting_issues += 1
            
            except Exception as e:
                continue
        
        if formatting_issues > 0:
            self.improvements_applied.append({
                'type': 'formatting_standardization',
                'description': f'Standardized formatting in {formatting_issues} files'
            })
    
    def _should_skip_file(self, file_path):
        """Check if file should be skipped"""
        skip_patterns = [
            'venv', '__pycache__', '.git', 'node_modules',
            'vendor', '.pytest_cache', 'htmlcov'
        ]
        return any(pattern in str(file_path) for pattern in skip_patterns)
    
    def generate_improvement_report(self):
        """Generate improvement report"""
        print("\n📈 CODE QUALITY IMPROVEMENT REPORT")
        print("=" * 40)
        
        if not self.improvements_applied:
            print("✅ No improvements needed - code quality is excellent!")
            return
        
        # Group improvements by type
        improvement_types = {}
        for improvement in self.improvements_applied:
            imp_type = improvement['type']
            improvement_types[imp_type] = improvement_types.get(imp_type, 0) + 1
        
        print("Improvements Applied:")
        print("-" * 20)
        for imp_type, count in improvement_types.items():
            print(f"• {imp_type.replace('_', ' ').title()}: {count}")
        
        print()
        
        # Detailed improvements
        print("Detailed Improvements:")
        print("-" * 22)
        for improvement in self.improvements_applied[:10]:  # Show first 10
            print(f"• {improvement['description']}")
            if 'file' in improvement:
                print(f"  File: {improvement['file']}")
            print()
        
        if len(self.improvements_applied) > 10:
            print(f"... and {len(self.improvements_applied) - 10} more improvements")
        
        print("=" * 40)


def main():
    """Main function to run code quality improvements"""
    improver = CodeQualityImprover()
    improver.improve_code_quality()


if __name__ == '__main__':
    main()
