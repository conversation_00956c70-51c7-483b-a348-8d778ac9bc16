#!/usr/bin/env python3
"""
Security Vulnerability Assessment for LinkUp Backend
Comprehensive security audit and vulnerability scanning
"""
import os
import sys
import re
import hashlib
import subprocess
import json
from datetime import datetime
from pathlib import Path
import ast
import importlib.util

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))


class SecurityAudit:
    """Main security audit class"""
    
    def __init__(self):
        self.vulnerabilities = []
        self.warnings = []
        self.info = []
        self.project_root = Path(__file__).parent
        
        # Security patterns to check
        self.security_patterns = {
            'sql_injection': [
                r'\.execute\s*\(\s*["\'].*%.*["\']',
                r'\.execute\s*\(\s*f["\'].*\{.*\}.*["\']',
                r'query\s*\(\s*["\'].*%.*["\']',
                r'raw\s*\(\s*["\'].*%.*["\']'
            ],
            'xss_vulnerabilities': [
                r'render_template_string\s*\(',
                r'Markup\s*\(',
                r'|safe\s*\}',
                r'innerHTML\s*=',
                r'document\.write\s*\('
            ],
            'hardcoded_secrets': [
                r'password\s*=\s*["\'][^"\']+["\']',
                r'secret\s*=\s*["\'][^"\']+["\']',
                r'api_key\s*=\s*["\'][^"\']+["\']',
                r'token\s*=\s*["\'][^"\']+["\']'
            ],
            'insecure_random': [
                r'random\.random\s*\(',
                r'random\.randint\s*\(',
                r'random\.choice\s*\('
            ],
            'debug_code': [
                r'print\s*\(',
                r'console\.log\s*\(',
                r'debug\s*=\s*True',
                r'DEBUG\s*=\s*True'
            ]
        }
    
    def run_security_audit(self):
        """Run comprehensive security audit"""
        print("🔒 LinkUp Security Vulnerability Assessment")
        print("=" * 50)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 1. Code Security Analysis
        self.analyze_code_security()
        
        # 2. Dependency Security Check
        self.check_dependency_security()
        
        # 3. Configuration Security
        self.check_configuration_security()
        
        # 4. API Security Assessment
        self.assess_api_security()
        
        # 5. Database Security
        self.check_database_security()
        
        # 6. File System Security
        self.check_filesystem_security()
        
        # 7. Authentication & Authorization
        self.check_auth_security()
        
        # Generate security report
        self.generate_security_report()
        
        return {
            'vulnerabilities': self.vulnerabilities,
            'warnings': self.warnings,
            'info': self.info
        }
    
    def analyze_code_security(self):
        """Analyze source code for security vulnerabilities"""
        print("🔍 Analyzing Code Security...")
        print("-" * 30)
        
        python_files = list(self.project_root.rglob("*.py"))
        js_files = list(self.project_root.rglob("*.js"))
        
        for file_path in python_files + js_files:
            if 'venv' in str(file_path) or '__pycache__' in str(file_path):
                continue
                
            self.scan_file_for_vulnerabilities(file_path)
        
        print(f"✅ Scanned {len(python_files + js_files)} files")
        print()
    
    def scan_file_for_vulnerabilities(self, file_path):
        """Scan individual file for security issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for vuln_type, patterns in self.security_patterns.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        
                        if vuln_type in ['hardcoded_secrets', 'sql_injection']:
                            severity = 'HIGH'
                            self.vulnerabilities.append({
                                'type': vuln_type,
                                'severity': severity,
                                'file': str(file_path),
                                'line': line_num,
                                'code': match.group(),
                                'description': self.get_vulnerability_description(vuln_type)
                            })
                        elif vuln_type in ['xss_vulnerabilities', 'insecure_random']:
                            severity = 'MEDIUM'
                            self.warnings.append({
                                'type': vuln_type,
                                'severity': severity,
                                'file': str(file_path),
                                'line': line_num,
                                'code': match.group(),
                                'description': self.get_vulnerability_description(vuln_type)
                            })
                        else:
                            severity = 'LOW'
                            self.info.append({
                                'type': vuln_type,
                                'severity': severity,
                                'file': str(file_path),
                                'line': line_num,
                                'code': match.group(),
                                'description': self.get_vulnerability_description(vuln_type)
                            })
        
        except Exception as e:
            self.warnings.append({
                'type': 'file_scan_error',
                'severity': 'LOW',
                'file': str(file_path),
                'description': f"Could not scan file: {e}"
            })
    
    def get_vulnerability_description(self, vuln_type):
        """Get description for vulnerability type"""
        descriptions = {
            'sql_injection': 'Potential SQL injection vulnerability. Use parameterized queries.',
            'xss_vulnerabilities': 'Potential XSS vulnerability. Sanitize user input.',
            'hardcoded_secrets': 'Hardcoded secrets found. Use environment variables.',
            'insecure_random': 'Insecure random number generation. Use secrets module.',
            'debug_code': 'Debug code found. Remove before production.'
        }
        return descriptions.get(vuln_type, 'Security issue detected')
    
    def check_dependency_security(self):
        """Check for vulnerable dependencies"""
        print("📦 Checking Dependency Security...")
        print("-" * 32)
        
        requirements_file = self.project_root / 'requirements.txt'
        
        if requirements_file.exists():
            try:
                # Run safety check if available
                result = subprocess.run(
                    ['safety', 'check', '-r', str(requirements_file)],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode != 0:
                    self.warnings.append({
                        'type': 'vulnerable_dependencies',
                        'severity': 'MEDIUM',
                        'description': 'Vulnerable dependencies detected',
                        'details': result.stdout
                    })
                else:
                    self.info.append({
                        'type': 'dependency_check',
                        'severity': 'INFO',
                        'description': 'No known vulnerable dependencies found'
                    })
            
            except (subprocess.TimeoutExpired, FileNotFoundError):
                self.info.append({
                    'type': 'dependency_check',
                    'severity': 'INFO',
                    'description': 'Safety tool not available. Install with: pip install safety'
                })
        
        print("✅ Dependency security check completed")
        print()
    
    def check_configuration_security(self):
        """Check configuration security"""
        print("⚙️ Checking Configuration Security...")
        print("-" * 35)
        
        config_files = [
            'config.py', 'settings.py', '.env', 'docker-compose.yml',
            'app/config.py', 'backend/config.py'
        ]
        
        for config_file in config_files:
            config_path = self.project_root / config_file
            if config_path.exists():
                self.check_config_file_security(config_path)
        
        # Check for insecure defaults
        self.check_insecure_defaults()
        
        print("✅ Configuration security check completed")
        print()
    
    def check_config_file_security(self, config_path):
        """Check individual config file"""
        try:
            with open(config_path, 'r') as f:
                content = f.read()
            
            # Check for insecure configurations
            insecure_patterns = {
                'debug_enabled': r'DEBUG\s*=\s*True',
                'weak_secret_key': r'SECRET_KEY\s*=\s*["\'][^"\']{1,10}["\']',
                'no_csrf_protection': r'WTF_CSRF_ENABLED\s*=\s*False',
                'insecure_session': r'SESSION_COOKIE_SECURE\s*=\s*False'
            }
            
            for issue_type, pattern in insecure_patterns.items():
                if re.search(pattern, content, re.IGNORECASE):
                    self.warnings.append({
                        'type': issue_type,
                        'severity': 'MEDIUM',
                        'file': str(config_path),
                        'description': f'Insecure configuration: {issue_type}'
                    })
        
        except Exception as e:
            self.info.append({
                'type': 'config_scan_error',
                'severity': 'LOW',
                'description': f"Could not scan config file {config_path}: {e}"
            })
    
    def check_insecure_defaults(self):
        """Check for insecure default configurations"""
        # Check Flask security headers
        security_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options', 
            'X-XSS-Protection',
            'Strict-Transport-Security',
            'Content-Security-Policy'
        ]
        
        self.info.append({
            'type': 'security_headers',
            'severity': 'INFO',
            'description': f'Ensure these security headers are configured: {", ".join(security_headers)}'
        })
    
    def assess_api_security(self):
        """Assess API security"""
        print("🌐 Assessing API Security...")
        print("-" * 25)
        
        # Check for common API security issues
        api_files = list(self.project_root.rglob("*api*.py")) + list(self.project_root.rglob("*route*.py"))
        
        for api_file in api_files:
            self.check_api_file_security(api_file)
        
        # Check for API security best practices
        self.check_api_best_practices()
        
        print("✅ API security assessment completed")
        print()
    
    def check_api_file_security(self, api_file):
        """Check API file for security issues"""
        try:
            with open(api_file, 'r') as f:
                content = f.read()
            
            # Check for missing authentication
            if '@app.route' in content and 'login_required' not in content:
                self.warnings.append({
                    'type': 'missing_authentication',
                    'severity': 'MEDIUM',
                    'file': str(api_file),
                    'description': 'API endpoints may be missing authentication'
                })
            
            # Check for missing input validation
            if 'request.json' in content and 'validate' not in content:
                self.warnings.append({
                    'type': 'missing_input_validation',
                    'severity': 'MEDIUM',
                    'file': str(api_file),
                    'description': 'API endpoints may be missing input validation'
                })
            
            # Check for missing rate limiting
            if '@app.route' in content and 'limiter' not in content:
                self.info.append({
                    'type': 'missing_rate_limiting',
                    'severity': 'LOW',
                    'file': str(api_file),
                    'description': 'Consider implementing rate limiting for API endpoints'
                })
        
        except Exception as e:
            pass  # Skip files that can't be read
    
    def check_api_best_practices(self):
        """Check API security best practices"""
        best_practices = [
            'Implement proper authentication and authorization',
            'Use HTTPS for all API communications',
            'Implement rate limiting to prevent abuse',
            'Validate and sanitize all input data',
            'Use proper error handling without exposing sensitive information',
            'Implement CORS policy appropriately',
            'Use API versioning',
            'Log security events for monitoring'
        ]
        
        self.info.append({
            'type': 'api_best_practices',
            'severity': 'INFO',
            'description': 'API Security Best Practices',
            'recommendations': best_practices
        })
    
    def check_database_security(self):
        """Check database security"""
        print("🗄️ Checking Database Security...")
        print("-" * 30)
        
        # Check for SQL injection vulnerabilities
        db_files = list(self.project_root.rglob("*model*.py")) + list(self.project_root.rglob("*db*.py"))
        
        for db_file in db_files:
            self.check_db_file_security(db_file)
        
        # Database security recommendations
        db_recommendations = [
            'Use parameterized queries to prevent SQL injection',
            'Implement proper database access controls',
            'Encrypt sensitive data at rest',
            'Use connection pooling with proper limits',
            'Implement database audit logging',
            'Regular security updates for database software',
            'Use least privilege principle for database users'
        ]
        
        self.info.append({
            'type': 'database_security',
            'severity': 'INFO',
            'description': 'Database Security Recommendations',
            'recommendations': db_recommendations
        })
        
        print("✅ Database security check completed")
        print()
    
    def check_db_file_security(self, db_file):
        """Check database file for security issues"""
        try:
            with open(db_file, 'r') as f:
                content = f.read()
            
            # Check for raw SQL usage
            if re.search(r'\.execute\s*\(\s*["\']', content):
                self.warnings.append({
                    'type': 'raw_sql_usage',
                    'severity': 'MEDIUM',
                    'file': str(db_file),
                    'description': 'Raw SQL usage detected. Ensure parameterized queries are used.'
                })
        
        except Exception as e:
            pass  # Skip files that can't be read
    
    def check_filesystem_security(self):
        """Check file system security"""
        print("📁 Checking File System Security...")
        print("-" * 33)
        
        # Check file permissions
        sensitive_files = [
            'config.py', '.env', 'private_key.pem', 'id_rsa',
            'database.db', 'secrets.json'
        ]
        
        for file_name in sensitive_files:
            file_paths = list(self.project_root.rglob(file_name))
            for file_path in file_paths:
                if file_path.exists():
                    self.check_file_permissions(file_path)
        
        # Check for sensitive files in version control
        self.check_sensitive_files_in_git()
        
        print("✅ File system security check completed")
        print()
    
    def check_file_permissions(self, file_path):
        """Check file permissions"""
        try:
            stat_info = file_path.stat()
            mode = oct(stat_info.st_mode)[-3:]
            
            # Check if file is world-readable
            if mode[2] in ['4', '5', '6', '7']:
                self.warnings.append({
                    'type': 'insecure_file_permissions',
                    'severity': 'MEDIUM',
                    'file': str(file_path),
                    'description': f'File has world-readable permissions: {mode}'
                })
        
        except Exception as e:
            pass  # Skip if can't check permissions
    
    def check_sensitive_files_in_git(self):
        """Check for sensitive files in git"""
        gitignore_path = self.project_root / '.gitignore'
        
        if gitignore_path.exists():
            with open(gitignore_path, 'r') as f:
                gitignore_content = f.read()
            
            sensitive_patterns = ['.env', '*.key', '*.pem', 'config.py', 'secrets.*']
            missing_patterns = []
            
            for pattern in sensitive_patterns:
                if pattern not in gitignore_content:
                    missing_patterns.append(pattern)
            
            if missing_patterns:
                self.warnings.append({
                    'type': 'sensitive_files_not_ignored',
                    'severity': 'MEDIUM',
                    'description': f'Sensitive file patterns not in .gitignore: {", ".join(missing_patterns)}'
                })
        else:
            self.warnings.append({
                'type': 'missing_gitignore',
                'severity': 'LOW',
                'description': '.gitignore file not found'
            })
    
    def check_auth_security(self):
        """Check authentication and authorization security"""
        print("🔐 Checking Authentication & Authorization...")
        print("-" * 42)
        
        auth_files = list(self.project_root.rglob("*auth*.py")) + list(self.project_root.rglob("*login*.py"))
        
        for auth_file in auth_files:
            self.check_auth_file_security(auth_file)
        
        # Authentication security recommendations
        auth_recommendations = [
            'Use strong password policies',
            'Implement account lockout after failed attempts',
            'Use secure session management',
            'Implement proper logout functionality',
            'Use CSRF protection',
            'Implement proper password hashing (bcrypt, scrypt, or Argon2)',
            'Use secure password reset mechanisms',
            'Implement multi-factor authentication where appropriate'
        ]
        
        self.info.append({
            'type': 'auth_security',
            'severity': 'INFO',
            'description': 'Authentication Security Recommendations',
            'recommendations': auth_recommendations
        })
        
        print("✅ Authentication security check completed")
        print()
    
    def check_auth_file_security(self, auth_file):
        """Check authentication file for security issues"""
        try:
            with open(auth_file, 'r') as f:
                content = f.read()
            
            # Check for weak password hashing
            if 'md5' in content.lower() or 'sha1' in content.lower():
                self.vulnerabilities.append({
                    'type': 'weak_password_hashing',
                    'severity': 'HIGH',
                    'file': str(auth_file),
                    'description': 'Weak password hashing algorithm detected'
                })
            
            # Check for missing CSRF protection
            if 'csrf' not in content.lower() and 'form' in content.lower():
                self.warnings.append({
                    'type': 'missing_csrf_protection',
                    'severity': 'MEDIUM',
                    'file': str(auth_file),
                    'description': 'Forms may be missing CSRF protection'
                })
        
        except Exception as e:
            pass  # Skip files that can't be read
    
    def generate_security_report(self):
        """Generate comprehensive security report"""
        print("\n📋 SECURITY AUDIT REPORT")
        print("=" * 50)
        
        total_issues = len(self.vulnerabilities) + len(self.warnings) + len(self.info)
        high_severity = len(self.vulnerabilities)
        medium_severity = len(self.warnings)
        low_severity = len(self.info)
        
        print(f"Total Issues Found: {total_issues}")
        print(f"High Severity (Vulnerabilities): {high_severity}")
        print(f"Medium Severity (Warnings): {medium_severity}")
        print(f"Low Severity (Info): {low_severity}")
        print()
        
        # High severity vulnerabilities
        if self.vulnerabilities:
            print("🚨 HIGH SEVERITY VULNERABILITIES:")
            print("-" * 35)
            for vuln in self.vulnerabilities:
                print(f"❌ {vuln['type']}: {vuln['description']}")
                if 'file' in vuln:
                    print(f"   File: {vuln['file']}")
                if 'line' in vuln:
                    print(f"   Line: {vuln['line']}")
                print()
        
        # Medium severity warnings
        if self.warnings:
            print("⚠️ MEDIUM SEVERITY WARNINGS:")
            print("-" * 30)
            for warning in self.warnings[:5]:  # Show first 5
                print(f"⚠️ {warning['type']}: {warning['description']}")
                if 'file' in warning:
                    print(f"   File: {warning['file']}")
                print()
            
            if len(self.warnings) > 5:
                print(f"   ... and {len(self.warnings) - 5} more warnings")
                print()
        
        # Security recommendations
        print("💡 SECURITY RECOMMENDATIONS:")
        print("-" * 30)
        recommendations = [
            "Implement comprehensive input validation",
            "Use HTTPS for all communications",
            "Implement proper error handling",
            "Use secure session management",
            "Implement rate limiting",
            "Regular security updates",
            "Security monitoring and logging",
            "Regular security audits"
        ]
        
        for i, rec in enumerate(recommendations, 1):
            print(f"{i}. {rec}")
        
        print()
        
        # Overall security score
        if high_severity == 0 and medium_severity <= 2:
            security_score = "🟢 GOOD"
            recommendation = "Security posture is good with minor improvements needed."
        elif high_severity <= 2 and medium_severity <= 5:
            security_score = "🟡 FAIR"
            recommendation = "Security posture is fair but needs attention."
        else:
            security_score = "🔴 POOR"
            recommendation = "Security posture needs immediate attention."
        
        print(f"Overall Security Score: {security_score}")
        print(f"Recommendation: {recommendation}")
        print()
        print("=" * 50)
        print(f"Security audit completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


def main():
    """Main function to run security audit"""
    auditor = SecurityAudit()
    results = auditor.run_security_audit()
    
    # Exit with appropriate code
    high_severity_count = len(results['vulnerabilities'])
    exit(0 if high_severity_count == 0 else 1)


if __name__ == '__main__':
    main()
