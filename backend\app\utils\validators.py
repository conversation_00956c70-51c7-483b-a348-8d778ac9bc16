"""
Validation utilities for LinkUp Plugin
"""
import re
from urllib.parse import urlparse
from typing import Optional, Dict, Any


def validate_email(email: str) -> bool:
    """Validate email address format"""
    if not email or not isinstance(email, str):
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_url(url: str) -> bool:
    """Validate URL format"""
    if not url or not isinstance(url, str):
        return False
    
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def validate_domain(domain: str) -> bool:
    """Validate domain name format"""
    if not domain or not isinstance(domain, str):
        return False
    
    # Remove protocol if present
    domain = domain.replace('http://', '').replace('https://', '')
    domain = domain.split('/')[0]  # Remove path if present
    
    # Basic domain validation
    pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$'
    return re.match(pattern, domain) is not None


def validate_password(password: str) -> Dict[str, Any]:
    """Validate password strength"""
    if not password or not isinstance(password, str):
        return {
            'valid': False,
            'errors': ['Password is required']
        }
    
    errors = []
    
    if len(password) < 8:
        errors.append('Password must be at least 8 characters long')
    
    if not re.search(r'[A-Z]', password):
        errors.append('Password must contain at least one uppercase letter')
    
    if not re.search(r'[a-z]', password):
        errors.append('Password must contain at least one lowercase letter')
    
    if not re.search(r'\d', password):
        errors.append('Password must contain at least one number')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors
    }


def validate_api_key_format(api_key: str) -> bool:
    """Validate API key format"""
    if not api_key or not isinstance(api_key, str):
        return False
    
    # LinkUp API keys should start with 'lup_' and be 32+ characters
    return api_key.startswith('lup_') and len(api_key) >= 32


def validate_content_length(content: str, max_length: int = 50000) -> bool:
    """Validate content length"""
    if not content or not isinstance(content, str):
        return False
    
    return len(content) <= max_length


def validate_language_code(language: str) -> bool:
    """Validate ISO 639-1 language code"""
    if not language or not isinstance(language, str):
        return False
    
    # Common language codes
    valid_codes = {
        'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh',
        'ar', 'hi', 'tr', 'pl', 'nl', 'sv', 'da', 'no', 'fi', 'cs'
    }
    
    return language.lower() in valid_codes


def validate_category(category: str) -> bool:
    """Validate content category"""
    if not category or not isinstance(category, str):
        return False
    
    valid_categories = {
        'technology', 'business', 'health', 'education', 'entertainment',
        'sports', 'travel', 'food', 'fashion', 'lifestyle', 'finance',
        'real-estate', 'automotive', 'science', 'politics', 'news',
        'art', 'music', 'gaming', 'photography', 'marketing'
    }
    
    return category.lower() in valid_categories


def validate_plan_type(plan: str) -> bool:
    """Validate user plan type"""
    if not plan or not isinstance(plan, str):
        return False
    
    valid_plans = {'free', 'pro', 'agency'}
    return plan.lower() in valid_plans


def validate_user_role(role: str) -> bool:
    """Validate user role"""
    if not role or not isinstance(role, str):
        return False
    
    valid_roles = {'user', 'admin', 'moderator'}
    return role.lower() in valid_roles


def validate_website_status(status: str) -> bool:
    """Validate website status"""
    if not status or not isinstance(status, str):
        return False
    
    valid_statuses = {
        'pending_analysis', 'active', 'inactive', 'analysis_failed', 'suspended'
    }
    return status.lower() in valid_statuses


def validate_backlink_status(status: str) -> bool:
    """Validate backlink status"""
    if not status or not isinstance(status, str):
        return False
    
    valid_statuses = {'pending', 'approved', 'active', 'removed', 'rejected'}
    return status.lower() in valid_statuses


def sanitize_input(input_str: str, max_length: Optional[int] = None) -> str:
    """Sanitize user input"""
    if not input_str or not isinstance(input_str, str):
        return ''
    
    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>"\']', '', input_str)
    
    # Trim whitespace
    sanitized = sanitized.strip()
    
    # Limit length if specified
    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized


def validate_json_structure(data: Dict[str, Any], required_fields: list) -> Dict[str, Any]:
    """Validate JSON data structure"""
    errors = []
    
    if not isinstance(data, dict):
        return {
            'valid': False,
            'errors': ['Data must be a JSON object']
        }
    
    for field in required_fields:
        if field not in data:
            errors.append(f'Missing required field: {field}')
        elif data[field] is None or data[field] == '':
            errors.append(f'Field cannot be empty: {field}')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors
    }


def validate_score_range(score: float, min_score: float = 0.0, max_score: float = 10.0) -> bool:
    """Validate score is within valid range"""
    if not isinstance(score, (int, float)):
        return False
    
    return min_score <= score <= max_score


def validate_ip_address(ip: str) -> bool:
    """Validate IP address format"""
    if not ip or not isinstance(ip, str):
        return False

    # Simple IPv4 validation
    pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
    if not re.match(pattern, ip):
        return False

    # Check each octet is 0-255
    octets = ip.split('.')
    for octet in octets:
        if not (0 <= int(octet) <= 255):
            return False

    return True


def validate_json(data: Any) -> bool:
    """Validate that data is JSON serializable"""
    import json
    try:
        json.dumps(data)
        return True
    except (TypeError, ValueError):
        return False
