"""
Admin routes for LinkUp Plugin
"""
from flask import Blueprint, request, jsonify, g
from app.models.user import User
from app.models.website import Website
from app.models.backlink import Backlink
from app.models.usage_stats import UsageStats
from app.utils.decorators import require_auth, require_admin, handle_errors, log_api_usage
from app import db
import logging

logger = logging.getLogger(__name__)

bp = Blueprint('admin', __name__)


@bp.route('/dashboard', methods=['GET'])
@require_auth
@require_admin
@handle_errors
@log_api_usage
def admin_dashboard():
    """Get admin dashboard data"""
    # Get system statistics
    total_users = User.query.count()
    active_users = User.query.filter_by(is_active=True).count()
    total_websites = Website.query.count()
    active_websites = Website.query.filter_by(status='active').count()
    total_backlinks = Backlink.query.count()
    active_backlinks = Backlink.query.filter_by(status='active').count()
    
    # Get user distribution by plan
    plan_distribution = {}
    for plan in ['free', 'pro', 'agency']:
        plan_distribution[plan] = User.query.filter_by(plan=plan).count()
    
    return jsonify({
        'success': True,
        'data': {
            'system_stats': {
                'total_users': total_users,
                'active_users': active_users,
                'total_websites': total_websites,
                'active_websites': active_websites,
                'total_backlinks': total_backlinks,
                'active_backlinks': active_backlinks
            },
            'plan_distribution': plan_distribution
        }
    })


@bp.route('/users', methods=['GET'])
@require_auth
@require_admin
@handle_errors
@log_api_usage
def get_users():
    """Get all users (admin only)"""
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 20)), 100)
    
    users = User.query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'success': True,
        'data': {
            'users': [user.to_dict() for user in users.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': users.total,
                'pages': users.pages
            }
        }
    })


@bp.route('/users/<int:user_id>/suspend', methods=['POST'])
@require_auth
@require_admin
@handle_errors
@log_api_usage
def suspend_user(user_id):
    """Suspend a user account"""
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({
            'success': False,
            'error': 'User not found'
        }), 404
    
    user.is_active = False
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': f'User {user.email} has been suspended'
    })


@bp.route('/users/<int:user_id>/activate', methods=['POST'])
@require_auth
@require_admin
@handle_errors
@log_api_usage
def activate_user(user_id):
    """Activate a user account"""
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({
            'success': False,
            'error': 'User not found'
        }), 404
    
    user.is_active = True
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': f'User {user.email} has been activated'
    })


@bp.route('/system/health', methods=['GET'])
@require_auth
@require_admin
@handle_errors
def system_health():
    """Get system health status"""
    # Basic health checks
    health_status = {
        'database': 'healthy',
        'redis': 'healthy',  # Placeholder
        'celery': 'healthy',  # Placeholder
        'overall': 'healthy'
    }
    
    try:
        # Test database connection
        db.session.execute(db.text('SELECT 1'))
    except Exception as e:
        health_status['database'] = 'unhealthy'
        health_status['overall'] = 'unhealthy'
    
    return jsonify({
        'success': True,
        'data': {
            'health': health_status,
            'timestamp': db.func.now()
        }
    })


@bp.route('/system/stats', methods=['GET'])
@require_auth
@require_admin
@handle_errors
@log_api_usage
def system_stats():
    """Get detailed system statistics"""
    from datetime import datetime, timedelta
    
    # Get date range
    days = int(request.args.get('days', 7))
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days)
    
    # Get usage statistics
    usage_stats = UsageStats.query.filter(
        UsageStats.date >= start_date,
        UsageStats.date <= end_date
    ).all()
    
    # Aggregate data
    total_api_requests = sum(stat.api_requests for stat in usage_stats)
    total_backlinks_created = sum(stat.backlinks_created for stat in usage_stats)
    total_content_analyzed = sum(stat.content_analyzed for stat in usage_stats)
    
    return jsonify({
        'success': True,
        'data': {
            'usage_summary': {
                'total_api_requests': total_api_requests,
                'total_backlinks_created': total_backlinks_created,
                'total_content_analyzed': total_content_analyzed
            },
            'date_range': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            }
        }
    })
