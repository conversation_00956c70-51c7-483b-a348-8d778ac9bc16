"""
Analytics routes for LinkUp Plugin
"""
from flask import Blueprint, request, jsonify, g
from app.models.usage_stats import UsageStats
from app.models.website import Website
from app.models.backlink import Backlink
from app.utils.decorators import require_auth, handle_errors, log_api_usage
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

bp = Blueprint('analytics', __name__)


@bp.route('/dashboard', methods=['GET'])
@require_auth
@handle_errors
@log_api_usage
def get_dashboard_data():
    """Get dashboard analytics data"""
    # Get date range
    days = int(request.args.get('days', 30))
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days)
    
    # Get user's websites
    user_websites = g.current_user.websites
    website_ids = [w.id for w in user_websites]
    
    # Get usage statistics
    usage_stats = UsageStats.query.filter(
        UsageStats.user_id == g.current_user.id,
        UsageStats.date >= start_date,
        UsageStats.date <= end_date
    ).all()
    
    # Aggregate data
    total_api_requests = sum(stat.api_requests for stat in usage_stats)
    total_backlinks_created = sum(stat.backlinks_created for stat in usage_stats)
    total_content_analyzed = sum(stat.content_analyzed for stat in usage_stats)
    
    # Get backlink statistics
    active_backlinks = Backlink.query.filter(
        Backlink.source_website_id.in_(website_ids),
        Backlink.status == 'active'
    ).count()
    
    pending_backlinks = Backlink.query.filter(
        Backlink.target_website_id.in_(website_ids),
        Backlink.status == 'pending'
    ).count()
    
    # Get plan limits and usage
    plan_limits = g.current_user.get_plan_limits()
    monthly_usage = g.current_user.get_usage_this_month()
    
    return jsonify({
        'success': True,
        'data': {
            'summary': {
                'total_websites': len(user_websites),
                'active_backlinks': active_backlinks,
                'pending_backlinks': pending_backlinks,
                'api_requests': total_api_requests,
                'content_analyzed': total_content_analyzed
            },
            'monthly_usage': {
                'backlinks_created': monthly_usage['backlinks_created'],
                'api_requests': monthly_usage['api_requests'],
                'content_analyzed': monthly_usage['content_analyzed']
            },
            'plan_limits': plan_limits,
            'usage_percentage': {
                'backlinks': (monthly_usage['backlinks_created'] / plan_limits['backlinks_per_month']) * 100,
                'api_requests': (monthly_usage['api_requests'] / plan_limits['api_requests_per_hour']) * 100
            }
        }
    })


@bp.route('/usage', methods=['GET'])
@require_auth
@handle_errors
@log_api_usage
def get_usage_stats():
    """Get detailed usage statistics"""
    # Get date range
    days = int(request.args.get('days', 30))
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days)
    
    # Get usage statistics
    usage_stats = UsageStats.query.filter(
        UsageStats.user_id == g.current_user.id,
        UsageStats.date >= start_date,
        UsageStats.date <= end_date
    ).order_by(UsageStats.date.asc()).all()
    
    # Format data for charts
    daily_data = []
    for stat in usage_stats:
        daily_data.append({
            'date': stat.date.isoformat(),
            'api_requests': stat.api_requests,
            'backlinks_created': stat.backlinks_created,
            'content_analyzed': stat.content_analyzed,
            'organic_traffic': stat.organic_traffic
        })
    
    return jsonify({
        'success': True,
        'data': {
            'daily_usage': daily_data,
            'date_range': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            }
        }
    })


@bp.route('/websites/<int:website_id>/stats', methods=['GET'])
@require_auth
@handle_errors
@log_api_usage
def get_website_stats(website_id):
    """Get statistics for a specific website"""
    # Verify user owns the website
    website = Website.query.filter_by(
        id=website_id,
        user_id=g.current_user.id
    ).first()
    
    if not website:
        return jsonify({
            'success': False,
            'error': 'Website not found'
        }), 404
    
    # Get backlink statistics
    inbound_backlinks = Backlink.query.filter_by(
        target_website_id=website_id
    ).all()
    
    outbound_backlinks = Backlink.query.filter_by(
        source_website_id=website_id
    ).all()
    
    # Count by status
    inbound_by_status = {}
    outbound_by_status = {}
    
    for status in ['pending', 'approved', 'active', 'removed', 'rejected']:
        inbound_by_status[status] = len([b for b in inbound_backlinks if b.status == status])
        outbound_by_status[status] = len([b for b in outbound_backlinks if b.status == status])
    
    # Get usage statistics for this website
    days = int(request.args.get('days', 30))
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days)
    
    usage_stats = UsageStats.query.filter(
        UsageStats.website_id == website_id,
        UsageStats.date >= start_date,
        UsageStats.date <= end_date
    ).order_by(UsageStats.date.asc()).all()
    
    return jsonify({
        'success': True,
        'data': {
            'website': website.to_dict(),
            'backlinks': {
                'inbound': {
                    'total': len(inbound_backlinks),
                    'by_status': inbound_by_status
                },
                'outbound': {
                    'total': len(outbound_backlinks),
                    'by_status': outbound_by_status
                }
            },
            'usage_stats': [stat.to_dict() for stat in usage_stats]
        }
    })


@bp.route('/performance', methods=['GET'])
@require_auth
@handle_errors
@log_api_usage
def get_performance_metrics():
    """Get performance metrics"""
    # Get date range
    days = int(request.args.get('days', 7))
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days)
    
    # Get usage statistics with response times
    usage_stats = UsageStats.query.filter(
        UsageStats.user_id == g.current_user.id,
        UsageStats.date >= start_date,
        UsageStats.date <= end_date
    ).all()
    
    # Calculate performance metrics
    total_requests = sum(stat.api_requests for stat in usage_stats)
    avg_response_time = sum(stat.api_response_time_avg or 0 for stat in usage_stats) / len(usage_stats) if usage_stats else 0
    
    return jsonify({
        'success': True,
        'data': {
            'performance': {
                'total_requests': total_requests,
                'avg_response_time': round(avg_response_time, 2),
                'uptime_percentage': 99.9,  # Placeholder
                'error_rate': 0.1  # Placeholder
            },
            'date_range': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            }
        }
    })


@bp.route('/export', methods=['GET'])
@require_auth
@handle_errors
@log_api_usage
def export_analytics():
    """Export analytics data"""
    # Get date range
    days = int(request.args.get('days', 30))
    format_type = request.args.get('format', 'json')  # json, csv
    
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days)
    
    # Get all data
    usage_stats = UsageStats.query.filter(
        UsageStats.user_id == g.current_user.id,
        UsageStats.date >= start_date,
        UsageStats.date <= end_date
    ).order_by(UsageStats.date.asc()).all()
    
    if format_type == 'csv':
        # Return CSV format
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'Date', 'API Requests', 'Backlinks Created', 
            'Content Analyzed', 'Organic Traffic', 'Avg Response Time'
        ])
        
        # Write data
        for stat in usage_stats:
            writer.writerow([
                stat.date.isoformat(),
                stat.api_requests,
                stat.backlinks_created,
                stat.content_analyzed,
                stat.organic_traffic,
                stat.api_response_time_avg or 0
            ])
        
        output.seek(0)
        return output.getvalue(), 200, {
            'Content-Type': 'text/csv',
            'Content-Disposition': f'attachment; filename=analytics_{start_date}_{end_date}.csv'
        }
    
    else:
        # Return JSON format
        return jsonify({
            'success': True,
            'data': {
                'usage_stats': [stat.to_dict() for stat in usage_stats],
                'export_info': {
                    'format': format_type,
                    'date_range': {
                        'start_date': start_date.isoformat(),
                        'end_date': end_date.isoformat()
                    },
                    'total_records': len(usage_stats)
                }
            }
        })
