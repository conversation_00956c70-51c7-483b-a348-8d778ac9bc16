"""
Preferences Management Service for LinkUp Plugin
Handles user matching preferences and configuration
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from app import db, cache
from app.models.matching_preferences import MatchingPreferences
from app.models.website import Website
from app.models.user import User

logger = logging.getLogger(__name__)


class PreferencesService:
    """Service for managing user matching preferences"""
    
    def __init__(self):
        """Initialize the preferences service"""
        self.cache_timeout = 1800  # 30 minutes cache
        
        # Default preference templates
        self.preference_templates = {
            'conservative': {
                'quality_thresholds': {
                    'min_quality_score': 8.0,
                    'min_domain_authority': 40,
                    'max_spam_score': 1.0,
                    'min_trust_signals': 5
                },
                'content_preferences': {
                    'min_content_similarity': 0.6,
                    'content_freshness_weight': 0.1
                },
                'link_velocity': {
                    'max_links_per_month': 5,
                    'min_days_between_links': 14,
                    'preferred_link_timing': 'gradual'
                },
                'auto_approval': {
                    'auto_approve_high_quality': False,
                    'auto_reject_low_quality': True,
                    'auto_reject_threshold': 0.6
                }
            },
            'balanced': {
                'quality_thresholds': {
                    'min_quality_score': 6.0,
                    'min_domain_authority': 20,
                    'max_spam_score': 3.0,
                    'min_trust_signals': 3
                },
                'content_preferences': {
                    'min_content_similarity': 0.3,
                    'content_freshness_weight': 0.05
                },
                'link_velocity': {
                    'max_links_per_month': 10,
                    'min_days_between_links': 7,
                    'preferred_link_timing': 'gradual'
                },
                'auto_approval': {
                    'auto_approve_high_quality': False,
                    'auto_reject_low_quality': True,
                    'auto_reject_threshold': 0.4
                }
            },
            'aggressive': {
                'quality_thresholds': {
                    'min_quality_score': 4.0,
                    'min_domain_authority': 10,
                    'max_spam_score': 5.0,
                    'min_trust_signals': 2
                },
                'content_preferences': {
                    'min_content_similarity': 0.2,
                    'content_freshness_weight': 0.02
                },
                'link_velocity': {
                    'max_links_per_month': 20,
                    'min_days_between_links': 3,
                    'preferred_link_timing': 'immediate'
                },
                'auto_approval': {
                    'auto_approve_high_quality': True,
                    'auto_approve_threshold': 0.7,
                    'auto_reject_low_quality': True,
                    'auto_reject_threshold': 0.3
                }
            }
        }
        
        # Available options for dropdowns/selections
        self.available_options = {
            'content_types': [
                'blog', 'news', 'educational', 'commercial', 'portfolio',
                'directory', 'forum', 'social', 'ecommerce', 'nonprofit'
            ],
            'languages': [
                'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko'
            ],
            'countries': [
                'US', 'GB', 'CA', 'AU', 'DE', 'FR', 'ES', 'IT', 'NL', 'SE'
            ],
            'link_timing': [
                'immediate', 'gradual', 'scheduled'
            ],
            'authority_strategies': [
                'similar', 'higher', 'any'
            ],
            'notification_frequencies': [
                'immediate', 'daily', 'weekly', 'monthly'
            ]
        }
    
    def get_user_preferences(self, user_id: int, website_id: Optional[int] = None) -> Dict:
        """Get user preferences with caching"""
        try:
            cache_key = f"preferences_{user_id}_{website_id}"
            
            # Try cache first
            cached_prefs = cache.get(cache_key)
            if cached_prefs:
                return cached_prefs
            
            # Get from database
            preferences = MatchingPreferences.get_or_create_for_user(user_id, website_id)
            prefs_dict = preferences.to_dict()
            
            # Cache the result
            cache.set(cache_key, prefs_dict, timeout=self.cache_timeout)
            
            return prefs_dict
            
        except Exception as e:
            logger.error(f"Error getting user preferences: {str(e)}")
            return self._get_default_preferences()
    
    def update_user_preferences(self, user_id: int, preferences_data: Dict,
                               website_id: Optional[int] = None) -> bool:
        """Update user preferences"""
        try:
            preferences = MatchingPreferences.get_or_create_for_user(user_id, website_id)
            
            # Validate preferences data
            validated_data = self._validate_preferences_data(preferences_data)
            
            # Update preferences
            success = preferences.update_preferences(validated_data)
            
            if success:
                # Clear cache
                cache_key = f"preferences_{user_id}_{website_id}"
                cache.delete(cache_key)
                
                logger.info(f"Updated preferences for user {user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error updating user preferences: {str(e)}")
            return False
    
    def apply_preference_template(self, user_id: int, template_name: str,
                                 website_id: Optional[int] = None) -> bool:
        """Apply a preference template to user settings"""
        try:
            if template_name not in self.preference_templates:
                logger.error(f"Unknown template: {template_name}")
                return False
            
            template_data = self.preference_templates[template_name]
            return self.update_user_preferences(user_id, template_data, website_id)
            
        except Exception as e:
            logger.error(f"Error applying preference template: {str(e)}")
            return False
    
    def get_preference_templates(self) -> Dict:
        """Get available preference templates"""
        return {
            'templates': self.preference_templates,
            'descriptions': {
                'conservative': 'High quality standards, slower link building, manual approval',
                'balanced': 'Moderate quality standards, steady link building, some automation',
                'aggressive': 'Lower quality standards, fast link building, high automation'
            }
        }
    
    def get_available_options(self) -> Dict:
        """Get available options for preference configuration"""
        return self.available_options
    
    def validate_link_velocity(self, user_id: int, website_id: Optional[int] = None) -> Dict:
        """Validate current link velocity against user preferences"""
        try:
            preferences = self.get_user_preferences(user_id, website_id)
            
            # Get recent backlinks for the user/website
            from app.models.backlink import Backlink
            
            # Calculate current month's links
            current_month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
            query = Backlink.query.filter(
                Backlink.created_at >= current_month_start,
                Backlink.status == 'active'
            )
            
            if website_id:
                query = query.filter(Backlink.source_website_id == website_id)
            else:
                # Get all websites for the user
                user_websites = Website.query.filter_by(user_id=user_id).all()
                website_ids = [w.id for w in user_websites]
                query = query.filter(Backlink.source_website_id.in_(website_ids))
            
            current_month_links = query.count()
            
            # Check against preferences
            max_links = preferences.get('link_velocity', {}).get('max_links_per_month', 10)
            
            return {
                'current_month_links': current_month_links,
                'max_links_per_month': max_links,
                'remaining_links': max(0, max_links - current_month_links),
                'velocity_ok': current_month_links <= max_links,
                'percentage_used': (current_month_links / max_links) * 100 if max_links > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error validating link velocity: {str(e)}")
            return {'velocity_ok': True, 'current_month_links': 0}
    
    def get_matching_weights_for_user(self, user_id: int, website_id: Optional[int] = None) -> Dict[str, float]:
        """Get effective matching weights for a user"""
        try:
            preferences = MatchingPreferences.get_or_create_for_user(user_id, website_id)
            return preferences.get_effective_weights()
            
        except Exception as e:
            logger.error(f"Error getting matching weights: {str(e)}")
            return self._get_default_weights()
    
    def should_auto_approve(self, user_id: int, match_score: float,
                           website_id: Optional[int] = None) -> bool:
        """Check if a match should be auto-approved based on user preferences"""
        try:
            preferences = self.get_user_preferences(user_id, website_id)
            auto_approval = preferences.get('auto_approval', {})
            
            if auto_approval.get('auto_approve_high_quality', False):
                threshold = auto_approval.get('auto_approve_threshold', 0.9)
                return match_score >= threshold
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking auto-approval: {str(e)}")
            return False
    
    def should_auto_reject(self, user_id: int, match_score: float,
                          website_id: Optional[int] = None) -> bool:
        """Check if a match should be auto-rejected based on user preferences"""
        try:
            preferences = self.get_user_preferences(user_id, website_id)
            auto_approval = preferences.get('auto_approval', {})
            
            if auto_approval.get('auto_reject_low_quality', True):
                threshold = auto_approval.get('auto_reject_threshold', 0.4)
                return match_score < threshold
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking auto-rejection: {str(e)}")
            return False
    
    def get_user_preferences_summary(self, user_id: int) -> Dict:
        """Get a summary of user preferences for dashboard display"""
        try:
            preferences = self.get_user_preferences(user_id)
            
            # Determine preference profile
            profile = self._determine_preference_profile(preferences)
            
            # Get key metrics
            quality_score = preferences.get('quality_thresholds', {}).get('min_quality_score', 6.0)
            link_velocity = preferences.get('link_velocity', {}).get('max_links_per_month', 10)
            auto_settings = preferences.get('auto_approval', {})
            
            return {
                'profile': profile,
                'quality_focus': 'High' if quality_score >= 7.0 else 'Medium' if quality_score >= 5.0 else 'Low',
                'link_velocity': 'Fast' if link_velocity >= 15 else 'Medium' if link_velocity >= 8 else 'Slow',
                'automation_level': self._get_automation_level(auto_settings),
                'geographic_targeting': preferences.get('geographic_preferences', {}).get('geographic_targeting', False),
                'language_matching': preferences.get('language_preferences', {}).get('language_matching', True),
                'custom_weights': preferences.get('advanced_settings', {}).get('use_custom_weights', False)
            }
            
        except Exception as e:
            logger.error(f"Error getting preferences summary: {str(e)}")
            return {}
    
    def _validate_preferences_data(self, data: Dict) -> Dict:
        """Validate and sanitize preferences data"""
        validated = {}
        
        # Validate quality thresholds
        if 'quality_thresholds' in data:
            qt = data['quality_thresholds']
            validated['quality_thresholds'] = {
                'min_quality_score': max(0.0, min(10.0, float(qt.get('min_quality_score', 6.0)))),
                'min_domain_authority': max(0, min(100, int(qt.get('min_domain_authority', 20)))),
                'max_spam_score': max(0.0, min(10.0, float(qt.get('max_spam_score', 3.0)))),
                'min_trust_signals': max(0, min(10, int(qt.get('min_trust_signals', 3))))
            }
        
        # Validate content preferences
        if 'content_preferences' in data:
            cp = data['content_preferences']
            validated['content_preferences'] = {
                'min_content_similarity': max(0.0, min(1.0, float(cp.get('min_content_similarity', 0.3)))),
                'preferred_content_types': [
                    ct for ct in cp.get('preferred_content_types', [])
                    if ct in self.available_options['content_types']
                ],
                'content_freshness_weight': max(0.0, min(1.0, float(cp.get('content_freshness_weight', 0.05))))
            }
        
        # Validate link velocity
        if 'link_velocity' in data:
            lv = data['link_velocity']
            validated['link_velocity'] = {
                'max_links_per_month': max(1, min(100, int(lv.get('max_links_per_month', 10)))),
                'min_days_between_links': max(1, min(365, int(lv.get('min_days_between_links', 7)))),
                'preferred_link_timing': lv.get('preferred_link_timing', 'gradual') 
                    if lv.get('preferred_link_timing') in self.available_options['link_timing'] else 'gradual'
            }
        
        # Add other validations as needed...
        
        return validated
    
    def _get_default_preferences(self) -> Dict:
        """Get default preferences structure"""
        return self.preference_templates['balanced']
    
    def _get_default_weights(self) -> Dict[str, float]:
        """Get default matching weights"""
        return {
            'content_similarity': 0.25,
            'category_match': 0.15,
            'quality_score': 0.15,
            'domain_authority': 0.12,
            'language_match': 0.08,
            'freshness': 0.05,
            'mutual_benefit': 0.08,
            'traffic_compatibility': 0.07,
            'niche_authority': 0.05
        }
    
    def _determine_preference_profile(self, preferences: Dict) -> str:
        """Determine which template profile best matches current preferences"""
        # Simple scoring system to match against templates
        scores = {}
        
        for template_name, template_data in self.preference_templates.items():
            score = 0
            comparisons = 0
            
            # Compare quality thresholds
            qt_prefs = preferences.get('quality_thresholds', {})
            qt_template = template_data.get('quality_thresholds', {})
            
            for key in ['min_quality_score', 'min_domain_authority']:
                if key in qt_prefs and key in qt_template:
                    diff = abs(qt_prefs[key] - qt_template[key])
                    max_val = max(qt_prefs[key], qt_template[key])
                    if max_val > 0:
                        score += 1 - (diff / max_val)
                        comparisons += 1
            
            # Compare link velocity
            lv_prefs = preferences.get('link_velocity', {})
            lv_template = template_data.get('link_velocity', {})
            
            if 'max_links_per_month' in lv_prefs and 'max_links_per_month' in lv_template:
                diff = abs(lv_prefs['max_links_per_month'] - lv_template['max_links_per_month'])
                max_val = max(lv_prefs['max_links_per_month'], lv_template['max_links_per_month'])
                if max_val > 0:
                    score += 1 - (diff / max_val)
                    comparisons += 1
            
            if comparisons > 0:
                scores[template_name] = score / comparisons
        
        # Return the template with the highest score
        if scores:
            return max(scores, key=scores.get)
        
        return 'balanced'
    
    def _get_automation_level(self, auto_settings: Dict) -> str:
        """Determine automation level from auto-approval settings"""
        auto_approve = auto_settings.get('auto_approve_high_quality', False)
        auto_reject = auto_settings.get('auto_reject_low_quality', False)
        
        if auto_approve and auto_reject:
            return 'High'
        elif auto_reject:
            return 'Medium'
        else:
            return 'Low'
