"""
API Key model for LinkUp Plugin Backend
"""
from datetime import datetime, timedelta
from app import db
import hashlib
import secrets


class <PERSON><PERSON><PERSON>ey(db.Model):
    """API Key model for authentication and access control"""
    
    __tablename__ = 'api_keys'
    
    # Primary fields
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.Foreign<PERSON>ey('users.id'), nullable=False, index=True)
    website_id = db.Column(db.Integer, db.ForeignKey('websites.id'), nullable=True, index=True)
    
    # Key information
    key_hash = db.Column(db.String(64), nullable=False, unique=True, index=True)
    key_prefix = db.Column(db.String(8), nullable=False)  # First 8 chars for identification
    description = db.Column(db.String(255))
    
    # Access control
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    permissions = db.Column(db.<PERSON><PERSON><PERSON>, default=list)  # List of allowed permissions
    ip_whitelist = db.Column(db.JSON, default=list)  # List of allowed IP addresses
    
    # Rate limiting
    rate_limit_per_hour = db.Column(db.Integer, default=1000)
    rate_limit_per_day = db.Column(db.Integer, default=10000)
    
    # Usage tracking
    last_used_at = db.Column(db.DateTime)
    last_used_ip = db.Column(db.String(45))  # IPv6 compatible
    usage_count = db.Column(db.Integer, default=0)
    
    # Expiration
    expires_at = db.Column(db.DateTime)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    revoked_at = db.Column(db.DateTime)
    
    # Relationships
    user = db.relationship('User', back_populates='api_keys')
    website = db.relationship('Website', back_populates='api_keys')
    
    def __init__(self, **kwargs):
        super(ApiKey, self).__init__(**kwargs)
        if self.key_hash and not self.key_prefix:
            self.key_prefix = self.key_hash[:8]
    
    @classmethod
    def generate_key(cls, user_id, website_id=None, description=None, expires_in_days=None):
        """Generate a new API key"""
        # Generate secure random key
        raw_key = f"lup_{secrets.token_urlsafe(32)}"  # LinkUp Plugin prefix
        key_hash = hashlib.sha256(raw_key.encode()).hexdigest()
        
        # Set expiration if specified
        expires_at = None
        if expires_in_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
        
        # Create API key instance
        api_key = cls(
            user_id=user_id,
            website_id=website_id,
            key_hash=key_hash,
            key_prefix=raw_key[:12],  # Show "lup_" + first 8 chars
            description=description or f"Generated on {datetime.utcnow().strftime('%Y-%m-%d')}",
            expires_at=expires_at
        )
        
        db.session.add(api_key)
        db.session.commit()
        
        return raw_key, api_key
    
    @classmethod
    def validate_key(cls, raw_key):
        """Validate an API key and return the associated record"""
        if not raw_key or not raw_key.startswith('lup_'):
            return None
        
        key_hash = hashlib.sha256(raw_key.encode()).hexdigest()
        
        api_key = cls.query.filter_by(
            key_hash=key_hash,
            is_active=True
        ).first()
        
        if not api_key:
            return None
        
        # Check if key is expired
        if api_key.expires_at and api_key.expires_at < datetime.utcnow():
            return None
        
        return api_key
    
    def is_valid(self):
        """Check if API key is valid"""
        if not self.is_active:
            return False
        
        if self.expires_at and self.expires_at < datetime.utcnow():
            return False
        
        return True
    
    def check_ip_whitelist(self, ip_address):
        """Check if IP address is allowed"""
        if not self.ip_whitelist:
            return True  # No whitelist means all IPs allowed
        
        return ip_address in self.ip_whitelist
    
    def has_permission(self, permission):
        """Check if API key has specific permission"""
        if not self.permissions:
            return True  # No permissions means all allowed (for backward compatibility)
        
        return permission in self.permissions
    
    def record_usage(self, ip_address=None):
        """Record API key usage"""
        self.last_used_at = datetime.utcnow()
        self.usage_count += 1
        
        if ip_address:
            self.last_used_ip = ip_address
        
        db.session.commit()
    
    def get_usage_stats(self, days=30):
        """Get usage statistics for the API key"""
        from app.models.usage_stats import UsageStats
        from sqlalchemy import func
        
        start_date = datetime.utcnow() - timedelta(days=days)
        
        stats = db.session.query(
            func.sum(UsageStats.api_requests).label('total_requests'),
            func.avg(UsageStats.api_requests).label('avg_daily_requests'),
            func.max(UsageStats.api_requests).label('max_daily_requests')
        ).filter(
            UsageStats.user_id == self.user_id,
            UsageStats.date >= start_date.date()
        ).first()
        
        return {
            'total_requests': stats.total_requests or 0,
            'avg_daily_requests': round(stats.avg_daily_requests or 0, 2),
            'max_daily_requests': stats.max_daily_requests or 0,
            'lifetime_usage': self.usage_count
        }
    
    def revoke(self, reason=None):
        """Revoke the API key"""
        self.is_active = False
        self.revoked_at = datetime.utcnow()
        
        if reason:
            self.description = f"{self.description} (Revoked: {reason})"
        
        db.session.commit()
    
    def regenerate(self):
        """Regenerate the API key (creates new key, revokes old one)"""
        # Generate new key
        raw_key = f"lup_{secrets.token_urlsafe(32)}"
        new_key_hash = hashlib.sha256(raw_key.encode()).hexdigest()
        
        # Update current record
        self.key_hash = new_key_hash
        self.key_prefix = raw_key[:12]
        self.created_at = datetime.utcnow()
        self.usage_count = 0
        self.last_used_at = None
        self.last_used_ip = None
        
        db.session.commit()
        
        return raw_key
    
    def to_dict(self, include_sensitive=False):
        """Convert API key to dictionary"""
        data = {
            'id': self.id,
            'key_prefix': self.key_prefix,
            'description': self.description,
            'is_active': self.is_active,
            'permissions': self.permissions,
            'rate_limit_per_hour': self.rate_limit_per_hour,
            'rate_limit_per_day': self.rate_limit_per_day,
            'usage_count': self.usage_count,
            'last_used_at': self.last_used_at.isoformat() if self.last_used_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'website_id': self.website_id
        }
        
        if include_sensitive:
            data.update({
                'ip_whitelist': self.ip_whitelist,
                'last_used_ip': self.last_used_ip,
                'usage_stats': self.get_usage_stats()
            })
        
        return data
    
    def __repr__(self):
        return f'<ApiKey {self.key_prefix}... for User {self.user_id}>'
