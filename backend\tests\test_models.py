"""
Unit tests for database models
"""
import pytest
from datetime import datetime, timedelta, date
from app.models.user import User
from app.models.website import Website
from app.models.api_key import Api<PERSON>ey
from app.models.usage_stats import UsageStats
from app.models.analysis import ContentAnalysis
from app import db
import hashlib


class TestUserModel:
    """Test User model functionality"""
    
    def test_user_creation(self, db_session):
        """Test basic user creation"""
        user = User(
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            plan='free',
            role='user'
        )
        user.set_password('testpassword123')
        
        db.session.add(user)
        db.session.commit()
        
        assert user.id is not None
        assert user.email == '<EMAIL>'
        assert user.check_password('testpassword123')
        assert not user.check_password('wrongpassword')
        assert user.uuid is not None
        assert user.is_active is True
        assert user.plan == 'free'
    
    def test_user_password_hashing(self, db_session):
        """Test password hashing and verification"""
        user = User(email='<EMAIL>')
        password = 'securepassword123'
        
        user.set_password(password)
        
        assert user.password_hash is not None
        assert user.password_hash != password
        assert user.check_password(password)
        assert not user.check_password('wrongpassword')
    
    def test_user_api_key_generation(self, db_session, user):
        """Test API key generation for user"""
        api_key, api_key_record = user.generate_api_key(description='Test API Key')
        
        assert api_key is not None
        assert api_key.startswith('lup_')
        assert api_key_record.user_id == user.id
        assert api_key_record.description == 'Test API Key'
        assert api_key_record.is_active is True
    
    def test_user_plan_limits(self, db_session):
        """Test plan limits for different user plans"""
        # Free user
        free_user = User(email='<EMAIL>', plan='free')
        free_limits = free_user.get_plan_limits()
        assert free_limits['backlinks_per_month'] == 10
        assert free_limits['websites'] == 1
        
        # Pro user
        pro_user = User(email='<EMAIL>', plan='pro')
        pro_limits = pro_user.get_plan_limits()
        assert pro_limits['backlinks_per_month'] == 100
        assert pro_limits['websites'] == 5
        
        # Agency user
        agency_user = User(email='<EMAIL>', plan='agency')
        agency_limits = agency_user.get_plan_limits()
        assert agency_limits['backlinks_per_month'] == 1000
        assert agency_limits['websites'] == 50
    
    def test_user_usage_tracking(self, db_session, user, website):
        """Test user usage statistics tracking"""
        # Create usage stats
        today = date.today()
        usage = UsageStats(
            user_id=user.id,
            website_id=website.id,
            date=today,
            backlinks_created=5,
            api_requests=100,
            content_analyzed=3
        )
        db.session.add(usage)
        db.session.commit()
        
        # Test usage retrieval
        monthly_usage = user.get_usage_this_month()
        assert monthly_usage['backlinks_created'] == 5
        assert monthly_usage['api_requests'] == 100
        assert monthly_usage['content_analyzed'] == 3
    
    def test_user_can_create_backlink(self, db_session, user):
        """Test backlink creation permission check"""
        # Free user with no usage should be able to create backlinks
        assert user.can_create_backlink() is True
        
        # Create usage that exceeds free plan limit
        today = date.today()
        usage = UsageStats(
            user_id=user.id,
            date=today,
            backlinks_created=15  # Exceeds free plan limit of 10
        )
        db.session.add(usage)
        db.session.commit()
        
        # Should not be able to create more backlinks
        assert user.can_create_backlink() is False
    
    def test_user_to_dict(self, db_session, user):
        """Test user serialization to dictionary"""
        user_dict = user.to_dict()
        
        assert user_dict['id'] == user.id
        assert user_dict['email'] == user.email
        assert user_dict['plan'] == user.plan
        assert user_dict['role'] == user.role
        assert 'password_hash' not in user_dict
        assert 'plan_limits' in user_dict
        assert 'usage_this_month' in user_dict


class TestWebsiteModel:
    """Test Website model functionality"""
    
    def test_website_creation(self, db_session, user):
        """Test basic website creation"""
        website = Website(
            user_id=user.id,
            domain='example.com',
            title='Example Website',
            description='A test website',
            category='technology',
            language='en'
        )
        
        db.session.add(website)
        db.session.commit()
        
        assert website.id is not None
        assert website.domain == 'example.com'
        assert website.user_id == user.id
        assert website.status == 'pending_analysis'  # Default status
        assert website.created_at is not None
    
    def test_website_url_generation(self, db_session, user):
        """Test automatic URL generation"""
        website = Website(
            user_id=user.id,
            domain='example.com',
            title='Example Website'
        )
        
        # URL should be auto-generated if not provided
        expected_url = f'https://{website.domain}'
        assert website.url == expected_url or website.url is None
    
    def test_website_relationships(self, db_session, user, website):
        """Test website relationships with other models"""
        # Test user relationship
        assert website.user == user
        assert website in user.websites
        
        # Test API keys relationship
        api_key = ApiKey(
            user_id=user.id,
            website_id=website.id,
            key_hash='test_hash',
            key_prefix='lup_test'
        )
        db.session.add(api_key)
        db.session.commit()
        
        assert api_key in website.api_keys
    
    def test_website_to_dict(self, db_session, website):
        """Test website serialization"""
        website_dict = website.to_dict()
        
        assert website_dict['id'] == website.id
        assert website_dict['domain'] == website.domain
        assert website_dict['title'] == website.title
        assert website_dict['status'] == website.status
        assert 'created_at' in website_dict


class TestApiKeyModel:
    """Test API Key model functionality"""
    
    def test_api_key_generation(self, db_session, user, website):
        """Test API key generation"""
        raw_key, api_key = ApiKey.generate_key(
            user_id=user.id,
            website_id=website.id,
            description='Test API Key'
        )
        
        assert raw_key.startswith('lup_')
        assert api_key.user_id == user.id
        assert api_key.website_id == website.id
        assert api_key.is_active is True
        assert api_key.key_prefix == raw_key[:12]
    
    def test_api_key_validation(self, db_session, user, website):
        """Test API key validation"""
        raw_key, api_key = ApiKey.generate_key(user.id, website.id)
        
        # Valid key should return the API key record
        validated_key = ApiKey.validate_key(raw_key)
        assert validated_key is not None
        assert validated_key.id == api_key.id
        
        # Invalid key should return None
        invalid_key = ApiKey.validate_key('invalid_key')
        assert invalid_key is None
        
        # Wrong format should return None
        wrong_format = ApiKey.validate_key('wrong_format_key')
        assert wrong_format is None
    
    def test_api_key_expiration(self, db_session, user, website):
        """Test API key expiration"""
        # Create expired key
        raw_key, api_key = ApiKey.generate_key(
            user.id, 
            website.id,
            expires_in_days=-1  # Expired yesterday
        )
        
        # Expired key should not validate
        validated_key = ApiKey.validate_key(raw_key)
        assert validated_key is None
    
    def test_api_key_usage_tracking(self, db_session, api_key):
        """Test API key usage tracking"""
        initial_count = api_key.usage_count
        
        api_key.record_usage('***********')
        
        assert api_key.usage_count == initial_count + 1
        assert api_key.last_used_ip == '***********'
        assert api_key.last_used_at is not None
    
    def test_api_key_ip_whitelist(self, db_session, api_key):
        """Test IP whitelist functionality"""
        # No whitelist should allow all IPs
        assert api_key.check_ip_whitelist('***********') is True
        
        # Set whitelist
        api_key.ip_whitelist = ['***********', '********']
        db.session.commit()
        
        # Whitelisted IP should be allowed
        assert api_key.check_ip_whitelist('***********') is True
        
        # Non-whitelisted IP should be blocked
        assert api_key.check_ip_whitelist('***********') is False
    
    def test_api_key_permissions(self, db_session, api_key):
        """Test API key permissions"""
        # No permissions should allow all
        assert api_key.has_permission('read') is True
        
        # Set specific permissions
        api_key.permissions = ['read', 'write']
        db.session.commit()
        
        # Allowed permission should return True
        assert api_key.has_permission('read') is True
        assert api_key.has_permission('write') is True
        
        # Disallowed permission should return False
        assert api_key.has_permission('admin') is False


class TestUsageStatsModel:
    """Test Usage Statistics model functionality"""
    
    def test_usage_stats_creation(self, db_session, user, website):
        """Test usage statistics creation"""
        today = date.today()
        stats = UsageStats(
            user_id=user.id,
            website_id=website.id,
            date=today,
            api_requests=100,
            backlinks_created=5
        )
        
        db.session.add(stats)
        db.session.commit()
        
        assert stats.id is not None
        assert stats.user_id == user.id
        assert stats.date == today
        assert stats.api_requests == 100
    
    def test_get_or_create_today(self, db_session, user, website):
        """Test get or create today's stats"""
        # First call should create new record
        stats1 = UsageStats.get_or_create_today(user.id, website.id)
        assert stats1.date == date.today()
        
        # Second call should return existing record
        stats2 = UsageStats.get_or_create_today(user.id, website.id)
        assert stats1.id == stats2.id
    
    def test_record_api_request(self, db_session, user, website):
        """Test API request recording"""
        UsageStats.record_api_request(user.id, website.id, response_time=150.5)
        
        stats = UsageStats.get_or_create_today(user.id, website.id)
        assert stats.api_requests == 1
        assert stats.api_response_time_avg == 150.5
        
        # Record another request
        UsageStats.record_api_request(user.id, website.id, response_time=200.0)
        
        db.session.refresh(stats)
        assert stats.api_requests == 2
        assert stats.api_response_time_avg == 175.25  # Average of 150.5 and 200.0
    
    def test_record_backlink_activity(self, db_session, user, website):
        """Test backlink activity recording"""
        UsageStats.record_backlink_activity(user.id, website.id, 'created')
        UsageStats.record_backlink_activity(user.id, website.id, 'approved')
        
        stats = UsageStats.get_or_create_today(user.id, website.id)
        assert stats.backlinks_created == 1
        assert stats.backlinks_approved == 1
    
    def test_user_summary(self, db_session, user, website):
        """Test user usage summary"""
        # Create test data for last 30 days
        for i in range(5):
            test_date = date.today() - timedelta(days=i)
            stats = UsageStats(
                user_id=user.id,
                website_id=website.id,
                date=test_date,
                api_requests=10,
                backlinks_created=1,
                content_analyzed=1
            )
            db.session.add(stats)
        
        db.session.commit()
        
        summary = UsageStats.get_user_summary(user.id, days=30)
        assert summary['api_requests'] == 50  # 10 * 5 days
        assert summary['backlinks_created'] == 5  # 1 * 5 days
        assert summary['content_analyzed'] == 5


class TestContentAnalysisModel:
    """Test Content Analysis model functionality"""
    
    def test_content_analysis_creation(self, db_session, website):
        """Test content analysis creation"""
        analysis = ContentAnalysis(
            website_id=website.id,
            content_hash='test_hash_123',
            quality_score=8.5,
            readability_score=75.0,
            keywords={'primary_keywords': [{'keyword': 'test', 'frequency': 5}]},
            categories={'primary_topics': [{'topic': 'technology', 'confidence': 0.8}]}
        )
        
        db.session.add(analysis)
        db.session.commit()
        
        assert analysis.id is not None
        assert analysis.website_id == website.id
        assert analysis.quality_score == 8.5
        assert analysis.analyzed_at is not None
    
    def test_analysis_data_storage(self, db_session, content_analysis):
        """Test analysis data JSON storage"""
        # Test that JSON data is properly stored and retrieved
        assert content_analysis.keywords is not None
        assert 'primary_keywords' in content_analysis.keywords
        
        assert content_analysis.categories is not None
        assert 'primary_topics' in content_analysis.categories
        
        assert content_analysis.analysis_data is not None
        assert 'quality_score' in content_analysis.analysis_data
    
    def test_analysis_to_dict(self, db_session, content_analysis):
        """Test content analysis serialization"""
        analysis_dict = content_analysis.to_dict()
        
        assert analysis_dict['id'] == content_analysis.id
        assert analysis_dict['website_id'] == content_analysis.website_id
        assert analysis_dict['quality_score'] == content_analysis.quality_score
        assert 'analyzed_at' in analysis_dict
        assert 'keywords' in analysis_dict
        assert 'categories' in analysis_dict
