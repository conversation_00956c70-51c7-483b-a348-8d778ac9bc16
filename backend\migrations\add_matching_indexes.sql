-- Migration: Add Matching System Indexes
-- Description: Adds optimized indexes for the LinkUp matching system
-- Date: 2024-12-17

-- Website indexes for matching performance
CREATE INDEX IF NOT EXISTS idx_websites_matching_lookup 
ON websites (status, domain_authority, category, user_id);

CREATE INDEX IF NOT EXISTS idx_websites_domain_lookup 
ON websites (domain, status);

CREATE INDEX IF NOT EXISTS idx_websites_user_category 
ON websites (user_id, category, status);

CREATE INDEX IF NOT EXISTS idx_websites_authority_category 
ON websites (domain_authority DESC, category, status);

CREATE INDEX IF NOT EXISTS idx_websites_created_status 
ON websites (created_at, status);

-- Content analysis indexes for matching
CREATE INDEX IF NOT EXISTS idx_analyses_website_latest 
ON content_analyses (website_id, analyzed_at DESC, quality_score);

CREATE INDEX IF NOT EXISTS idx_analyses_quality_lookup 
ON content_analyses (quality_score DESC, analyzed_at DESC);

CREATE INDEX IF NOT EXISTS idx_analyses_language 
ON content_analyses (language, analyzed_at DESC);

CREATE INDEX IF NOT EXISTS idx_analyses_website_quality 
ON content_analyses (website_id, quality_score DESC);

-- Backlink indexes for exclusion queries
CREATE INDEX IF NOT EXISTS idx_backlinks_source_target 
ON backlinks (source_website_id, target_website_id, status);

CREATE INDEX IF NOT EXISTS idx_backlinks_target_source 
ON backlinks (target_website_id, source_website_id, status);

CREATE INDEX IF NOT EXISTS idx_backlinks_created_status 
ON backlinks (created_at DESC, status);

CREATE INDEX IF NOT EXISTS idx_backlinks_user_activity 
ON backlinks (source_website_id, created_at DESC, status);

-- Domain filter indexes
CREATE INDEX IF NOT EXISTS idx_domain_filters_active_lookup 
ON domain_filters (is_active, filter_type, scope);

CREATE INDEX IF NOT EXISTS idx_domain_filters_domain_active 
ON domain_filters (domain, is_active, filter_type);

CREATE INDEX IF NOT EXISTS idx_domain_filters_user_scope 
ON domain_filters (user_id, scope, is_active);

-- Keyword filter indexes
CREATE INDEX IF NOT EXISTS idx_keyword_filters_active_lookup 
ON keyword_filters (is_active, filter_type, scope);

CREATE INDEX IF NOT EXISTS idx_keyword_filters_keyword_active 
ON keyword_filters (keyword, is_active, filter_type);

CREATE INDEX IF NOT EXISTS idx_keyword_filters_user_scope 
ON keyword_filters (user_id, scope, is_active);

-- Matching preferences indexes
CREATE INDEX IF NOT EXISTS idx_preferences_user_website 
ON matching_preferences (user_id, website_id);

CREATE INDEX IF NOT EXISTS idx_preferences_user_updated 
ON matching_preferences (user_id, updated_at DESC);

-- User indexes for matching
CREATE INDEX IF NOT EXISTS idx_users_status_created 
ON users (status, created_at);

-- Composite indexes for complex matching queries
CREATE INDEX IF NOT EXISTS idx_websites_matching_composite 
ON websites (status, category, domain_authority DESC, user_id);

CREATE INDEX IF NOT EXISTS idx_analyses_matching_composite 
ON content_analyses (website_id, analyzed_at DESC, quality_score DESC, language);

-- Indexes for filtering and exclusion
CREATE INDEX IF NOT EXISTS idx_backlinks_exclusion_composite 
ON backlinks (source_website_id, target_website_id, status, created_at);

-- Performance monitoring indexes
CREATE INDEX IF NOT EXISTS idx_websites_performance 
ON websites (domain_authority DESC, created_at DESC, status);

CREATE INDEX IF NOT EXISTS idx_analyses_performance 
ON content_analyses (quality_score DESC, analyzed_at DESC, website_id);

-- Partitioning preparation (for future use)
-- Note: These would be used if implementing table partitioning

-- ALTER TABLE backlinks 
-- PARTITION BY RANGE (YEAR(created_at)) (
--     PARTITION p2023 VALUES LESS THAN (2024),
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p2025 VALUES LESS THAN (2026),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- Analyze tables for query optimization
ANALYZE TABLE websites;
ANALYZE TABLE content_analyses;
ANALYZE TABLE backlinks;
ANALYZE TABLE domain_filters;
ANALYZE TABLE keyword_filters;
ANALYZE TABLE matching_preferences;

-- Create views for common matching queries
CREATE OR REPLACE VIEW v_active_websites AS
SELECT 
    w.*,
    ca.quality_score,
    ca.analyzed_at,
    ca.language
FROM websites w
LEFT JOIN (
    SELECT 
        website_id,
        quality_score,
        analyzed_at,
        language,
        ROW_NUMBER() OVER (PARTITION BY website_id ORDER BY analyzed_at DESC) as rn
    FROM content_analyses
) ca ON w.id = ca.website_id AND ca.rn = 1
WHERE w.status = 'active';

CREATE OR REPLACE VIEW v_matching_candidates AS
SELECT 
    w.*,
    ca.quality_score,
    ca.analyzed_at,
    ca.language,
    COALESCE(w.domain_authority, 0) as effective_domain_authority
FROM websites w
LEFT JOIN (
    SELECT 
        website_id,
        quality_score,
        analyzed_at,
        language,
        ROW_NUMBER() OVER (PARTITION BY website_id ORDER BY analyzed_at DESC) as rn
    FROM content_analyses
    WHERE quality_score >= 5.0
) ca ON w.id = ca.website_id AND ca.rn = 1
WHERE w.status = 'active'
AND w.domain_authority >= 10;

-- Create stored procedures for common operations
DELIMITER //

CREATE PROCEDURE GetPotentialPartners(
    IN source_website_id INT,
    IN min_domain_authority INT DEFAULT 20,
    IN max_results INT DEFAULT 100
)
BEGIN
    SELECT DISTINCT
        w.id,
        w.domain,
        w.category,
        w.domain_authority,
        w.user_id,
        ca.quality_score,
        ca.analyzed_at
    FROM websites w
    LEFT JOIN (
        SELECT 
            website_id,
            quality_score,
            analyzed_at,
            ROW_NUMBER() OVER (PARTITION BY website_id ORDER BY analyzed_at DESC) as rn
        FROM content_analyses
    ) ca ON w.id = ca.website_id AND ca.rn = 1
    WHERE w.status = 'active'
    AND w.id != source_website_id
    AND w.user_id != (SELECT user_id FROM websites WHERE id = source_website_id)
    AND COALESCE(w.domain_authority, 0) >= min_domain_authority
    AND w.id NOT IN (
        SELECT target_website_id 
        FROM backlinks 
        WHERE source_website_id = source_website_id 
        AND status IN ('active', 'pending')
    )
    AND w.id NOT IN (
        SELECT source_website_id 
        FROM backlinks 
        WHERE target_website_id = source_website_id 
        AND status IN ('active', 'pending')
    )
    ORDER BY w.domain_authority DESC, ca.quality_score DESC
    LIMIT max_results;
END //

CREATE PROCEDURE GetUserMatchingStats(
    IN user_id INT
)
BEGIN
    SELECT 
        COUNT(DISTINCT w.id) as total_websites,
        COUNT(DISTINCT b.id) as total_backlinks,
        AVG(ca.quality_score) as avg_quality_score,
        MAX(w.domain_authority) as max_domain_authority,
        COUNT(DISTINCT CASE WHEN b.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN b.id END) as recent_backlinks
    FROM websites w
    LEFT JOIN content_analyses ca ON w.id = ca.website_id
    LEFT JOIN backlinks b ON w.id = b.source_website_id AND b.status = 'active'
    WHERE w.user_id = user_id
    AND w.status = 'active';
END //

DELIMITER ;

-- Create triggers for cache invalidation
DELIMITER //

CREATE TRIGGER tr_websites_cache_invalidate
AFTER UPDATE ON websites
FOR EACH ROW
BEGIN
    -- In a real implementation, this would trigger cache invalidation
    -- For now, we'll just log the change
    INSERT INTO cache_invalidation_log (table_name, record_id, action, created_at)
    VALUES ('websites', NEW.id, 'UPDATE', NOW());
END //

CREATE TRIGGER tr_analyses_cache_invalidate
AFTER INSERT ON content_analyses
FOR EACH ROW
BEGIN
    INSERT INTO cache_invalidation_log (table_name, record_id, action, created_at)
    VALUES ('content_analyses', NEW.id, 'INSERT', NOW());
END //

DELIMITER ;

-- Create cache invalidation log table
CREATE TABLE IF NOT EXISTS cache_invalidation_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(50) NOT NULL,
    record_id INT NOT NULL,
    action VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_cache_log_table_time (table_name, created_at),
    INDEX idx_cache_log_record (table_name, record_id)
);

-- Performance monitoring table
CREATE TABLE IF NOT EXISTS query_performance_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    query_type VARCHAR(50) NOT NULL,
    execution_time DECIMAL(10,4) NOT NULL,
    rows_examined INT,
    rows_returned INT,
    cache_hit BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_perf_log_type_time (query_type, created_at),
    INDEX idx_perf_log_performance (execution_time DESC, created_at DESC)
);

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE ON linkup_db.* TO 'linkup_user'@'%';
-- GRANT EXECUTE ON PROCEDURE linkup_db.GetPotentialPartners TO 'linkup_user'@'%';
-- GRANT EXECUTE ON PROCEDURE linkup_db.GetUserMatchingStats TO 'linkup_user'@'%';

-- Final optimization
OPTIMIZE TABLE websites;
OPTIMIZE TABLE content_analyses;
OPTIMIZE TABLE backlinks;
OPTIMIZE TABLE domain_filters;
OPTIMIZE TABLE keyword_filters;
OPTIMIZE TABLE matching_preferences;
