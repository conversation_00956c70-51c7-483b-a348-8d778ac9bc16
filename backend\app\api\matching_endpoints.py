"""
API Endpoints for LinkUp Matching System
Provides RESTful endpoints for match retrieval, filtering, and management
"""
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from marshmallow import Schema, fields, validate, ValidationError
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from app.services.matching_service import MatchingService
from app.services.preferences_service import PreferencesService
from app.services.filter_management_service import FilterManagementService
from app.services.quality_assessment_service import QualityAssessmentService
from app.models.website import Website
from app.models.user import User
from app import db, limiter

logger = logging.getLogger(__name__)

# Create blueprint
matching_bp = Blueprint('matching', __name__, url_prefix='/api/v1/matching')

# Initialize services
matching_service = MatchingService()
preferences_service = PreferencesService()
filter_service = FilterManagementService()
quality_service = QualityAssessmentService()


# Request/Response Schemas
class MatchingRequestSchema(Schema):
    """Schema for match request validation"""
    website_id = fields.Integer(required=True, validate=validate.Range(min=1))
    limit = fields.Integer(missing=20, validate=validate.Range(min=1, max=100))
    min_quality_score = fields.Float(missing=None, validate=validate.Range(min=0, max=10))
    use_preferences = fields.Boolean(missing=True)
    include_details = fields.Boolean(missing=False)
    filter_type = fields.String(missing='all', validate=validate.OneOf(['all', 'quality', 'preferences']))


class FilterRequestSchema(Schema):
    """Schema for filter management requests"""
    domain = fields.String(required=False)
    keyword = fields.String(required=False)
    filter_type = fields.String(required=True, validate=validate.OneOf(['blacklist', 'whitelist']))
    scope = fields.String(missing='user', validate=validate.OneOf(['global', 'user', 'website']))
    reason = fields.String(missing=None)


class PreferencesUpdateSchema(Schema):
    """Schema for preferences update requests"""
    quality_thresholds = fields.Dict(missing=None)
    content_preferences = fields.Dict(missing=None)
    niche_preferences = fields.Dict(missing=None)
    geographic_preferences = fields.Dict(missing=None)
    language_preferences = fields.Dict(missing=None)
    link_velocity = fields.Dict(missing=None)
    authority_preferences = fields.Dict(missing=None)
    advanced_settings = fields.Dict(missing=None)
    notification_settings = fields.Dict(missing=None)
    auto_approval = fields.Dict(missing=None)


class BatchMatchingRequestSchema(Schema):
    """Schema for batch matching requests"""
    website_ids = fields.List(fields.Integer(), required=True, validate=validate.Length(min=1, max=10))
    limit_per_website = fields.Integer(missing=10, validate=validate.Range(min=1, max=50))
    use_preferences = fields.Boolean(missing=True)


# Utility functions
def get_current_user() -> Optional[User]:
    """Get current authenticated user"""
    try:
        user_id = get_jwt_identity()
        return User.query.get(user_id)
    except Exception as e:
        logger.error(f"Error getting current user: {str(e)}")
        return None


def validate_website_ownership(website_id: int, user_id: int) -> bool:
    """Validate that user owns the website"""
    website = Website.query.get(website_id)
    return website and website.user_id == user_id


def format_api_response(data: Any, message: str = "Success", status: int = 200) -> Dict:
    """Format standardized API response"""
    return {
        'status': 'success' if status < 400 else 'error',
        'message': message,
        'data': data,
        'timestamp': datetime.utcnow().isoformat()
    }


def handle_api_error(error: Exception, message: str = "An error occurred") -> tuple:
    """Handle API errors with consistent formatting"""
    logger.error(f"API Error: {str(error)}")
    return jsonify(format_api_response(None, f"{message}: {str(error)}", 500)), 500


# API Endpoints

@matching_bp.route('/find', methods=['POST'])
@jwt_required()
@limiter.limit("30 per minute")
def find_matches():
    """Find matches for a website"""
    try:
        # Validate request
        schema = MatchingRequestSchema()
        try:
            data = schema.load(request.json or {})
        except ValidationError as e:
            return jsonify(format_api_response(None, f"Validation error: {e.messages}", 400)), 400
        
        # Get current user
        user = get_current_user()
        if not user:
            return jsonify(format_api_response(None, "Authentication required", 401)), 401
        
        # Validate website ownership
        if not validate_website_ownership(data['website_id'], user.id):
            return jsonify(format_api_response(None, "Website not found or access denied", 403)), 403
        
        # Find matches based on filter type
        if data['filter_type'] == 'quality':
            matches = matching_service.find_quality_filtered_matches(
                data['website_id'],
                limit=data['limit'],
                min_quality_score=data['min_quality_score']
            )
        elif data['filter_type'] == 'preferences' or data['use_preferences']:
            matches = matching_service.find_matches_with_preferences(
                data['website_id'],
                limit=data['limit']
            )
        else:
            matches = matching_service.find_matches(
                data['website_id'],
                limit=data['limit']
            )
        
        # Format response
        formatted_matches = []
        for match in matches:
            partner = match['partner_website']
            match_data = {
                'partner_id': partner.id,
                'partner_domain': partner.domain,
                'partner_category': partner.category,
                'partner_authority': getattr(partner, 'domain_authority', 0),
                'match_score': match['match_score']['total_score'],
                'estimated_value': match.get('estimated_value', 0),
                'mutual_benefit': match.get('mutual_benefit', 0),
                'auto_approve': match.get('auto_approve', False)
            }
            
            # Include detailed breakdown if requested
            if data['include_details']:
                match_data['score_breakdown'] = match['match_score']
                match_data['reasons'] = match.get('reasons', [])
                match_data['quality_metrics'] = match.get('quality_metrics', {})
            
            formatted_matches.append(match_data)
        
        response_data = {
            'matches': formatted_matches,
            'total_found': len(formatted_matches),
            'website_id': data['website_id'],
            'filter_applied': data['filter_type'],
            'preferences_used': data['use_preferences']
        }
        
        return jsonify(format_api_response(response_data, "Matches found successfully"))
        
    except Exception as e:
        return handle_api_error(e, "Error finding matches")


@matching_bp.route('/batch', methods=['POST'])
@jwt_required()
@limiter.limit("10 per minute")
def batch_find_matches():
    """Find matches for multiple websites in batch"""
    try:
        # Validate request
        schema = BatchMatchingRequestSchema()
        try:
            data = schema.load(request.json or {})
        except ValidationError as e:
            return jsonify(format_api_response(None, f"Validation error: {e.messages}", 400)), 400
        
        # Get current user
        user = get_current_user()
        if not user:
            return jsonify(format_api_response(None, "Authentication required", 401)), 401
        
        # Validate all websites belong to user
        for website_id in data['website_ids']:
            if not validate_website_ownership(website_id, user.id):
                return jsonify(format_api_response(None, f"Access denied for website {website_id}", 403)), 403
        
        # Find matches for each website
        batch_results = {}
        for website_id in data['website_ids']:
            try:
                if data['use_preferences']:
                    matches = matching_service.find_matches_with_preferences(
                        website_id,
                        limit=data['limit_per_website']
                    )
                else:
                    matches = matching_service.find_matches(
                        website_id,
                        limit=data['limit_per_website']
                    )
                
                # Format matches
                formatted_matches = []
                for match in matches:
                    partner = match['partner_website']
                    formatted_matches.append({
                        'partner_id': partner.id,
                        'partner_domain': partner.domain,
                        'match_score': match['match_score']['total_score'],
                        'estimated_value': match.get('estimated_value', 0)
                    })
                
                batch_results[website_id] = {
                    'matches': formatted_matches,
                    'count': len(formatted_matches)
                }
                
            except Exception as e:
                logger.error(f"Error finding matches for website {website_id}: {str(e)}")
                batch_results[website_id] = {
                    'matches': [],
                    'count': 0,
                    'error': str(e)
                }
        
        response_data = {
            'batch_results': batch_results,
            'total_websites': len(data['website_ids']),
            'successful_websites': len([r for r in batch_results.values() if 'error' not in r])
        }
        
        return jsonify(format_api_response(response_data, "Batch matching completed"))
        
    except Exception as e:
        return handle_api_error(e, "Error in batch matching")


@matching_bp.route('/score/<int:source_id>/<int:target_id>', methods=['GET'])
@jwt_required()
@limiter.limit("60 per minute")
def get_match_score(source_id: int, target_id: int):
    """Get detailed match score between two specific websites"""
    try:
        # Get current user
        user = get_current_user()
        if not user:
            return jsonify(format_api_response(None, "Authentication required", 401)), 401
        
        # Validate source website ownership
        if not validate_website_ownership(source_id, user.id):
            return jsonify(format_api_response(None, "Source website access denied", 403)), 403
        
        # Get detailed compatibility breakdown
        compatibility = matching_service.get_compatibility_breakdown(source_id, target_id)
        
        if not compatibility:
            return jsonify(format_api_response(None, "Unable to calculate compatibility", 404)), 404
        
        # Get similarity breakdown
        similarity = matching_service.get_detailed_similarity_breakdown(source_id, target_id)
        
        # Get niche insights
        niche_insights = matching_service.get_niche_matching_insights(source_id)
        
        response_data = {
            'source_website_id': source_id,
            'target_website_id': target_id,
            'compatibility_breakdown': compatibility,
            'similarity_breakdown': similarity,
            'niche_insights': niche_insights,
            'calculated_at': datetime.utcnow().isoformat()
        }
        
        return jsonify(format_api_response(response_data, "Match score calculated successfully"))
        
    except Exception as e:
        return handle_api_error(e, "Error calculating match score")


@matching_bp.route('/preferences', methods=['GET'])
@jwt_required()
def get_preferences():
    """Get user matching preferences"""
    try:
        user = get_current_user()
        if not user:
            return jsonify(format_api_response(None, "Authentication required", 401)), 401
        
        website_id = request.args.get('website_id', type=int)
        preferences = preferences_service.get_user_preferences(user.id, website_id)
        
        return jsonify(format_api_response(preferences, "Preferences retrieved successfully"))
        
    except Exception as e:
        return handle_api_error(e, "Error getting preferences")


@matching_bp.route('/preferences', methods=['PUT'])
@jwt_required()
@limiter.limit("20 per minute")
def update_preferences():
    """Update user matching preferences"""
    try:
        # Validate request
        schema = PreferencesUpdateSchema()
        try:
            data = schema.load(request.json or {})
        except ValidationError as e:
            return jsonify(format_api_response(None, f"Validation error: {e.messages}", 400)), 400
        
        user = get_current_user()
        if not user:
            return jsonify(format_api_response(None, "Authentication required", 401)), 401
        
        website_id = request.args.get('website_id', type=int)
        
        # Validate website ownership if website_id provided
        if website_id and not validate_website_ownership(website_id, user.id):
            return jsonify(format_api_response(None, "Website access denied", 403)), 403
        
        success = preferences_service.update_user_preferences(user.id, data, website_id)
        
        if success:
            updated_preferences = preferences_service.get_user_preferences(user.id, website_id)
            return jsonify(format_api_response(updated_preferences, "Preferences updated successfully"))
        else:
            return jsonify(format_api_response(None, "Failed to update preferences", 500)), 500
        
    except Exception as e:
        return handle_api_error(e, "Error updating preferences")


@matching_bp.route('/filters', methods=['GET'])
@jwt_required()
def get_filters():
    """Get user filters (blacklist/whitelist)"""
    try:
        user = get_current_user()
        if not user:
            return jsonify(format_api_response(None, "Authentication required", 401)), 401

        filter_type = request.args.get('filter_type')  # 'blacklist', 'whitelist', or None for all
        filters = filter_service.get_user_filters(user.id, filter_type)

        return jsonify(format_api_response(filters, "Filters retrieved successfully"))

    except Exception as e:
        return handle_api_error(e, "Error getting filters")


@matching_bp.route('/filters', methods=['POST'])
@jwt_required()
@limiter.limit("50 per minute")
def add_filter():
    """Add a new filter (domain or keyword)"""
    try:
        # Validate request
        schema = FilterRequestSchema()
        try:
            data = schema.load(request.json or {})
        except ValidationError as e:
            return jsonify(format_api_response(None, f"Validation error: {e.messages}", 400)), 400

        user = get_current_user()
        if not user:
            return jsonify(format_api_response(None, "Authentication required", 401)), 401

        # Validate that either domain or keyword is provided
        if not data.get('domain') and not data.get('keyword'):
            return jsonify(format_api_response(None, "Either domain or keyword must be provided", 400)), 400

        if data.get('domain') and data.get('keyword'):
            return jsonify(format_api_response(None, "Provide either domain or keyword, not both", 400)), 400

        # Add the filter
        if data.get('domain'):
            filter_obj = filter_service.add_domain_filter(
                domain=data['domain'],
                filter_type=data['filter_type'],
                scope=data['scope'],
                user_id=user.id,
                reason=data.get('reason')
            )
        else:
            filter_obj = filter_service.add_keyword_filter(
                keyword=data['keyword'],
                filter_type=data['filter_type'],
                scope=data['scope'],
                user_id=user.id,
                reason=data.get('reason')
            )

        return jsonify(format_api_response(filter_obj.to_dict(), "Filter added successfully"))

    except Exception as e:
        return handle_api_error(e, "Error adding filter")


@matching_bp.route('/filters/<int:filter_id>', methods=['DELETE'])
@jwt_required()
@limiter.limit("30 per minute")
def remove_filter(filter_id: int):
    """Remove a filter"""
    try:
        user = get_current_user()
        if not user:
            return jsonify(format_api_response(None, "Authentication required", 401)), 401

        filter_model = request.args.get('type', 'domain')  # 'domain' or 'keyword'

        if filter_model not in ['domain', 'keyword']:
            return jsonify(format_api_response(None, "Invalid filter type", 400)), 400

        success = filter_service.remove_filter(filter_id, filter_model, user.id)

        if success:
            return jsonify(format_api_response(None, "Filter removed successfully"))
        else:
            return jsonify(format_api_response(None, "Filter not found or access denied", 404)), 404

    except Exception as e:
        return handle_api_error(e, "Error removing filter")


@matching_bp.route('/quality/assess/<int:website_id>', methods=['GET'])
@jwt_required()
@limiter.limit("30 per minute")
def assess_website_quality(website_id: int):
    """Get quality assessment for a website"""
    try:
        user = get_current_user()
        if not user:
            return jsonify(format_api_response(None, "Authentication required", 401)), 401

        # Get website (don't require ownership for quality assessment)
        website = Website.query.get(website_id)
        if not website:
            return jsonify(format_api_response(None, "Website not found", 404)), 404

        # Get quality report
        quality_report = quality_service.generate_quality_report(website)

        return jsonify(format_api_response(quality_report, "Quality assessment completed"))

    except Exception as e:
        return handle_api_error(e, "Error assessing website quality")


@matching_bp.route('/insights/<int:website_id>', methods=['GET'])
@jwt_required()
def get_matching_insights(website_id: int):
    """Get comprehensive matching insights for a website"""
    try:
        user = get_current_user()
        if not user:
            return jsonify(format_api_response(None, "Authentication required", 401)), 401

        # Validate website ownership
        if not validate_website_ownership(website_id, user.id):
            return jsonify(format_api_response(None, "Website access denied", 403)), 403

        # Get various insights
        match_stats = matching_service.get_enhanced_match_statistics(website_id)
        niche_insights = matching_service.get_niche_matching_insights(website_id)
        quality_insights = matching_service.get_quality_insights(website_id)

        # Get link velocity validation
        velocity_check = preferences_service.validate_link_velocity(user.id, website_id)

        # Get preferences summary
        prefs_summary = preferences_service.get_user_preferences_summary(user.id)

        response_data = {
            'website_id': website_id,
            'match_statistics': match_stats,
            'niche_insights': niche_insights,
            'quality_insights': quality_insights,
            'link_velocity': velocity_check,
            'preferences_summary': prefs_summary,
            'generated_at': datetime.utcnow().isoformat()
        }

        return jsonify(format_api_response(response_data, "Insights generated successfully"))

    except Exception as e:
        return handle_api_error(e, "Error getting matching insights")


@matching_bp.route('/templates', methods=['GET'])
@jwt_required()
def get_preference_templates():
    """Get available preference templates"""
    try:
        templates = preferences_service.get_preference_templates()
        options = preferences_service.get_available_options()

        response_data = {
            'templates': templates,
            'available_options': options
        }

        return jsonify(format_api_response(response_data, "Templates retrieved successfully"))

    except Exception as e:
        return handle_api_error(e, "Error getting templates")


@matching_bp.route('/templates/<template_name>/apply', methods=['POST'])
@jwt_required()
@limiter.limit("10 per minute")
def apply_preference_template(template_name: str):
    """Apply a preference template"""
    try:
        user = get_current_user()
        if not user:
            return jsonify(format_api_response(None, "Authentication required", 401)), 401

        website_id = request.args.get('website_id', type=int)

        # Validate website ownership if website_id provided
        if website_id and not validate_website_ownership(website_id, user.id):
            return jsonify(format_api_response(None, "Website access denied", 403)), 403

        success = preferences_service.apply_preference_template(user.id, template_name, website_id)

        if success:
            updated_preferences = preferences_service.get_user_preferences(user.id, website_id)
            return jsonify(format_api_response(updated_preferences, f"Template '{template_name}' applied successfully"))
        else:
            return jsonify(format_api_response(None, "Failed to apply template or template not found", 400)), 400

    except Exception as e:
        return handle_api_error(e, "Error applying template")


@matching_bp.route('/stats/performance', methods=['GET'])
@jwt_required()
def get_performance_stats():
    """Get matching system performance statistics"""
    try:
        user = get_current_user()
        if not user:
            return jsonify(format_api_response(None, "Authentication required", 401)), 401

        # Get performance metrics from query optimizer
        performance_metrics = matching_service.query_optimizer.get_query_performance_metrics()

        # Get filter statistics
        filter_stats = filter_service.get_filter_statistics(user.id)

        response_data = {
            'performance_metrics': performance_metrics,
            'filter_statistics': filter_stats,
            'user_id': user.id,
            'generated_at': datetime.utcnow().isoformat()
        }

        return jsonify(format_api_response(response_data, "Performance statistics retrieved"))

    except Exception as e:
        return handle_api_error(e, "Error getting performance statistics")


# Error handlers
@matching_bp.errorhandler(404)
def not_found(error):
    return jsonify(format_api_response(None, "Endpoint not found", 404)), 404


@matching_bp.errorhandler(405)
def method_not_allowed(error):
    return jsonify(format_api_response(None, "Method not allowed", 405)), 405


@matching_bp.errorhandler(429)
def rate_limit_exceeded(error):
    return jsonify(format_api_response(None, "Rate limit exceeded", 429)), 429


@matching_bp.errorhandler(500)
def internal_error(error):
    return jsonify(format_api_response(None, "Internal server error", 500)), 500
