<?php
/**
 * Content Suggestions Admin Interface
 * 
 * Handles the WordPress admin interface for viewing and managing content suggestions
 * 
 * @package LinkUp
 * @subpackage Admin
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class LinkUp_Content_Suggestions_Admin {
    
    /**
     * Initialize the admin interface
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_linkup_get_suggestions', array($this, 'ajax_get_suggestions'));
        add_action('wp_ajax_linkup_dismiss_suggestion', array($this, 'ajax_dismiss_suggestion'));
        add_action('wp_ajax_linkup_implement_suggestion', array($this, 'ajax_implement_suggestion'));
        add_action('wp_ajax_linkup_get_trending_topics', array($this, 'ajax_get_trending_topics'));
        add_action('wp_ajax_linkup_analyze_content', array($this, 'ajax_analyze_content'));
    }
    
    /**
     * Add admin menu pages
     */
    public function add_admin_menu() {
        add_submenu_page(
            'linkup-dashboard',
            __('Content Suggestions', 'linkup'),
            __('Content Suggestions', 'linkup'),
            'manage_options',
            'linkup-content-suggestions',
            array($this, 'render_suggestions_page')
        );
        
        add_submenu_page(
            'linkup-dashboard',
            __('Trending Topics', 'linkup'),
            __('Trending Topics', 'linkup'),
            'manage_options',
            'linkup-trending-topics',
            array($this, 'render_trending_page')
        );
        
        add_submenu_page(
            'linkup-dashboard',
            __('Content Optimizer', 'linkup'),
            __('Content Optimizer', 'linkup'),
            'manage_options',
            'linkup-content-optimizer',
            array($this, 'render_optimizer_page')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_scripts($hook) {
        if (strpos($hook, 'linkup-content') === false && strpos($hook, 'linkup-trending') === false) {
            return;
        }
        
        wp_enqueue_script(
            'linkup-content-suggestions',
            LINKUP_PLUGIN_URL . 'assets/js/content-suggestions.js',
            array('jquery', 'wp-util'),
            LINKUP_VERSION,
            true
        );
        
        wp_enqueue_style(
            'linkup-content-suggestions',
            LINKUP_PLUGIN_URL . 'assets/css/content-suggestions.css',
            array(),
            LINKUP_VERSION
        );
        
        wp_localize_script('linkup-content-suggestions', 'linkupContentSuggestions', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('linkup_content_suggestions'),
            'strings' => array(
                'loading' => __('Loading...', 'linkup'),
                'error' => __('An error occurred. Please try again.', 'linkup'),
                'success' => __('Action completed successfully.', 'linkup'),
                'confirm_dismiss' => __('Are you sure you want to dismiss this suggestion?', 'linkup'),
                'confirm_implement' => __('Are you sure you want to implement this suggestion?', 'linkup')
            )
        ));
    }
    
    /**
     * Render the content suggestions page
     */
    public function render_suggestions_page() {
        ?>
        <div class="wrap linkup-content-suggestions">
            <h1><?php _e('Content Suggestions', 'linkup'); ?></h1>
            
            <div class="linkup-suggestions-header">
                <div class="linkup-filters">
                    <select id="suggestion-type-filter">
                        <option value=""><?php _e('All Types', 'linkup'); ?></option>
                        <option value="keyword_targeting"><?php _e('Keyword Targeting', 'linkup'); ?></option>
                        <option value="content_expansion"><?php _e('Content Expansion', 'linkup'); ?></option>
                        <option value="trending_opportunity"><?php _e('Trending Opportunity', 'linkup'); ?></option>
                        <option value="competitive_response"><?php _e('Competitive Response', 'linkup'); ?></option>
                    </select>
                    
                    <select id="priority-filter">
                        <option value=""><?php _e('All Priorities', 'linkup'); ?></option>
                        <option value="critical"><?php _e('Critical', 'linkup'); ?></option>
                        <option value="high"><?php _e('High', 'linkup'); ?></option>
                        <option value="medium"><?php _e('Medium', 'linkup'); ?></option>
                        <option value="low"><?php _e('Low', 'linkup'); ?></option>
                    </select>
                    
                    <button id="refresh-suggestions" class="button">
                        <?php _e('Refresh Suggestions', 'linkup'); ?>
                    </button>
                </div>
                
                <div class="linkup-stats">
                    <div class="stat-box">
                        <span class="stat-number" id="total-suggestions">-</span>
                        <span class="stat-label"><?php _e('Total Suggestions', 'linkup'); ?></span>
                    </div>
                    <div class="stat-box">
                        <span class="stat-number" id="high-priority">-</span>
                        <span class="stat-label"><?php _e('High Priority', 'linkup'); ?></span>
                    </div>
                    <div class="stat-box">
                        <span class="stat-number" id="estimated-traffic">-</span>
                        <span class="stat-label"><?php _e('Est. Traffic Potential', 'linkup'); ?></span>
                    </div>
                </div>
            </div>
            
            <div id="suggestions-loading" class="linkup-loading" style="display: none;">
                <div class="spinner is-active"></div>
                <p><?php _e('Loading content suggestions...', 'linkup'); ?></p>
            </div>
            
            <div id="suggestions-container" class="linkup-suggestions-grid">
                <!-- Suggestions will be loaded here via AJAX -->
            </div>
            
            <div id="no-suggestions" style="display: none;">
                <div class="linkup-empty-state">
                    <h3><?php _e('No Content Suggestions Available', 'linkup'); ?></h3>
                    <p><?php _e('We\'re analyzing your content and competitors to generate suggestions. Check back soon!', 'linkup'); ?></p>
                    <button id="generate-suggestions" class="button button-primary">
                        <?php _e('Generate New Suggestions', 'linkup'); ?>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Suggestion Detail Modal -->
        <div id="suggestion-modal" class="linkup-modal" style="display: none;">
            <div class="linkup-modal-content">
                <div class="linkup-modal-header">
                    <h2 id="modal-title"></h2>
                    <button class="linkup-modal-close">&times;</button>
                </div>
                <div class="linkup-modal-body">
                    <div class="suggestion-details">
                        <div class="detail-section">
                            <h3><?php _e('Description', 'linkup'); ?></h3>
                            <p id="modal-description"></p>
                        </div>
                        
                        <div class="detail-section">
                            <h3><?php _e('Rationale', 'linkup'); ?></h3>
                            <p id="modal-rationale"></p>
                        </div>
                        
                        <div class="detail-section">
                            <h3><?php _e('Target Keywords', 'linkup'); ?></h3>
                            <div id="modal-keywords" class="keyword-tags"></div>
                        </div>
                        
                        <div class="detail-section">
                            <h3><?php _e('Content Outline', 'linkup'); ?></h3>
                            <ul id="modal-outline"></ul>
                        </div>
                        
                        <div class="detail-section">
                            <h3><?php _e('SEO Recommendations', 'linkup'); ?></h3>
                            <ul id="modal-seo-recommendations"></ul>
                        </div>
                        
                        <div class="detail-section">
                            <h3><?php _e('Action Steps', 'linkup'); ?></h3>
                            <ol id="modal-action-steps"></ol>
                        </div>
                        
                        <div class="detail-metrics">
                            <div class="metric">
                                <span class="metric-label"><?php _e('Opportunity Score', 'linkup'); ?></span>
                                <span class="metric-value" id="modal-opportunity-score"></span>
                            </div>
                            <div class="metric">
                                <span class="metric-label"><?php _e('Difficulty', 'linkup'); ?></span>
                                <span class="metric-value" id="modal-difficulty"></span>
                            </div>
                            <div class="metric">
                                <span class="metric-label"><?php _e('Est. Traffic', 'linkup'); ?></span>
                                <span class="metric-value" id="modal-traffic"></span>
                            </div>
                            <div class="metric">
                                <span class="metric-label"><?php _e('ROI Estimate', 'linkup'); ?></span>
                                <span class="metric-value" id="modal-roi"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="linkup-modal-footer">
                    <button id="modal-dismiss" class="button">
                        <?php _e('Dismiss', 'linkup'); ?>
                    </button>
                    <button id="modal-implement" class="button button-primary">
                        <?php _e('Start Implementation', 'linkup'); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render the trending topics page
     */
    public function render_trending_page() {
        ?>
        <div class="wrap linkup-trending-topics">
            <h1><?php _e('Trending Topics', 'linkup'); ?></h1>
            
            <div class="linkup-trending-header">
                <div class="linkup-filters">
                    <select id="niche-filter">
                        <option value=""><?php _e('Select Niche', 'linkup'); ?></option>
                        <option value="technology"><?php _e('Technology', 'linkup'); ?></option>
                        <option value="business"><?php _e('Business', 'linkup'); ?></option>
                        <option value="health"><?php _e('Health', 'linkup'); ?></option>
                        <option value="finance"><?php _e('Finance', 'linkup'); ?></option>
                        <option value="lifestyle"><?php _e('Lifestyle', 'linkup'); ?></option>
                    </select>
                    
                    <select id="timeframe-filter">
                        <option value="7d"><?php _e('Last 7 Days', 'linkup'); ?></option>
                        <option value="30d"><?php _e('Last 30 Days', 'linkup'); ?></option>
                        <option value="90d"><?php _e('Last 90 Days', 'linkup'); ?></option>
                    </select>
                    
                    <button id="refresh-trending" class="button">
                        <?php _e('Refresh Trends', 'linkup'); ?>
                    </button>
                </div>
            </div>
            
            <div id="trending-loading" class="linkup-loading" style="display: none;">
                <div class="spinner is-active"></div>
                <p><?php _e('Loading trending topics...', 'linkup'); ?></p>
            </div>
            
            <div id="trending-container">
                <div class="trending-sections">
                    <div class="trending-section">
                        <h2><?php _e('Rising Topics', 'linkup'); ?></h2>
                        <div id="rising-topics" class="topic-list"></div>
                    </div>
                    
                    <div class="trending-section">
                        <h2><?php _e('Emerging Keywords', 'linkup'); ?></h2>
                        <div id="emerging-keywords" class="keyword-cloud"></div>
                    </div>
                    
                    <div class="trending-section">
                        <h2><?php _e('Content Opportunities', 'linkup'); ?></h2>
                        <div id="content-opportunities" class="opportunity-list"></div>
                    </div>
                </div>
                
                <div class="trending-insights">
                    <h2><?php _e('Trend Insights', 'linkup'); ?></h2>
                    <div id="trend-insights" class="insights-list"></div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * AJAX handler for getting content suggestions
     */
    public function ajax_get_suggestions() {
        check_ajax_referer('linkup_content_suggestions', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'linkup'));
        }

        try {
            $website_id = get_option('linkup_website_id', 1);
            $type_filter = sanitize_text_field($_POST['type_filter'] ?? '');
            $priority_filter = sanitize_text_field($_POST['priority_filter'] ?? '');

            // Make API call to get suggestions
            $api_client = new LinkUp_API_Client();
            $response = $api_client->get_content_suggestions($website_id, array(
                'type_filter' => $type_filter,
                'priority_filter' => $priority_filter,
                'limit' => 20
            ));

            if (is_wp_error($response)) {
                wp_send_json_error(array(
                    'message' => $response->get_error_message()
                ));
            }

            wp_send_json_success($response);

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to load suggestions. Please try again.', 'linkup')
            ));
        }
    }

    /**
     * AJAX handler for dismissing a suggestion
     */
    public function ajax_dismiss_suggestion() {
        check_ajax_referer('linkup_content_suggestions', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'linkup'));
        }

        try {
            $suggestion_id = sanitize_text_field($_POST['suggestion_id']);

            if (empty($suggestion_id)) {
                wp_send_json_error(array(
                    'message' => __('Invalid suggestion ID', 'linkup')
                ));
            }

            // Store dismissed suggestion
            $dismissed = get_option('linkup_dismissed_suggestions', array());
            $dismissed[] = $suggestion_id;
            update_option('linkup_dismissed_suggestions', $dismissed);

            wp_send_json_success(array(
                'message' => __('Suggestion dismissed successfully', 'linkup')
            ));

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to dismiss suggestion. Please try again.', 'linkup')
            ));
        }
    }

    /**
     * AJAX handler for implementing a suggestion
     */
    public function ajax_implement_suggestion() {
        check_ajax_referer('linkup_content_suggestions', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'linkup'));
        }

        try {
            $suggestion_id = sanitize_text_field($_POST['suggestion_id']);
            $action_type = sanitize_text_field($_POST['action_type'] ?? 'draft');

            if (empty($suggestion_id)) {
                wp_send_json_error(array(
                    'message' => __('Invalid suggestion ID', 'linkup')
                ));
            }

            // Get suggestion details
            $api_client = new LinkUp_API_Client();
            $suggestion = $api_client->get_suggestion_details($suggestion_id);

            if (is_wp_error($suggestion)) {
                wp_send_json_error(array(
                    'message' => $suggestion->get_error_message()
                ));
            }

            // Create draft post based on suggestion
            $post_data = array(
                'post_title' => $suggestion['title'],
                'post_content' => $this->generate_content_from_suggestion($suggestion),
                'post_status' => $action_type === 'publish' ? 'publish' : 'draft',
                'post_type' => 'post',
                'meta_input' => array(
                    '_linkup_suggestion_id' => $suggestion_id,
                    '_linkup_target_keywords' => implode(',', $suggestion['target_keywords']),
                    '_linkup_opportunity_score' => $suggestion['opportunity_score']
                )
            );

            $post_id = wp_insert_post($post_data);

            if (is_wp_error($post_id)) {
                wp_send_json_error(array(
                    'message' => __('Failed to create post. Please try again.', 'linkup')
                ));
            }

            // Mark suggestion as implemented
            $implemented = get_option('linkup_implemented_suggestions', array());
            $implemented[] = $suggestion_id;
            update_option('linkup_implemented_suggestions', $implemented);

            wp_send_json_success(array(
                'message' => __('Content created successfully', 'linkup'),
                'post_id' => $post_id,
                'edit_url' => admin_url('post.php?post=' . $post_id . '&action=edit')
            ));

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to implement suggestion. Please try again.', 'linkup')
            ));
        }
    }

    /**
     * AJAX handler for getting trending topics
     */
    public function ajax_get_trending_topics() {
        check_ajax_referer('linkup_content_suggestions', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'linkup'));
        }

        try {
            $niche = sanitize_text_field($_POST['niche'] ?? 'technology');
            $timeframe = sanitize_text_field($_POST['timeframe'] ?? '7d');

            // Make API call to get trending topics
            $api_client = new LinkUp_API_Client();
            $response = $api_client->get_trending_topics($niche, $timeframe);

            if (is_wp_error($response)) {
                wp_send_json_error(array(
                    'message' => $response->get_error_message()
                ));
            }

            wp_send_json_success($response);

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to load trending topics. Please try again.', 'linkup')
            ));
        }
    }

    /**
     * AJAX handler for content analysis
     */
    public function ajax_analyze_content() {
        check_ajax_referer('linkup_content_suggestions', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'linkup'));
        }

        try {
            $content_url = esc_url_raw($_POST['content_url']);
            $target_keywords = sanitize_textarea_field($_POST['target_keywords'] ?? '');

            if (empty($content_url)) {
                wp_send_json_error(array(
                    'message' => __('Content URL is required', 'linkup')
                ));
            }

            // Parse keywords
            $keywords = array();
            if (!empty($target_keywords)) {
                $keywords = array_map('trim', explode(',', $target_keywords));
                $keywords = array_filter($keywords);
            }

            // Make API call to analyze content
            $api_client = new LinkUp_API_Client();
            $response = $api_client->analyze_content($content_url, $keywords);

            if (is_wp_error($response)) {
                wp_send_json_error(array(
                    'message' => $response->get_error_message()
                ));
            }

            wp_send_json_success($response);

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to analyze content. Please try again.', 'linkup')
            ));
        }
    }

    /**
     * Generate content from suggestion
     */
    private function generate_content_from_suggestion($suggestion) {
        $content = '';

        // Add introduction
        $content .= '<p>' . esc_html($suggestion['description']) . '</p>' . "\n\n";

        // Add content outline as structure
        if (!empty($suggestion['content_outline'])) {
            foreach ($suggestion['content_outline'] as $index => $outline_item) {
                $heading_level = $index === 0 ? 'h2' : 'h3';
                $content .= '<' . $heading_level . '>' . esc_html($outline_item) . '</' . $heading_level . '>' . "\n";
                $content .= '<p>[Add content for: ' . esc_html($outline_item) . ']</p>' . "\n\n";
            }
        }

        // Add target keywords as a comment
        if (!empty($suggestion['target_keywords'])) {
            $content .= '<!-- Target Keywords: ' . esc_html(implode(', ', $suggestion['target_keywords'])) . ' -->' . "\n";
        }

        // Add SEO recommendations as comments
        if (!empty($suggestion['seo_recommendations'])) {
            $content .= '<!-- SEO Recommendations:' . "\n";
            foreach ($suggestion['seo_recommendations'] as $recommendation) {
                $content .= '- ' . esc_html($recommendation) . "\n";
            }
            $content .= '-->' . "\n";
        }

        return $content;
    }
}
