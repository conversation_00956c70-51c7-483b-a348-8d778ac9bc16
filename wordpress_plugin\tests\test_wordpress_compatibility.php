<?php
/**
 * WordPress Compatibility Tests for LinkUp Plugin
 * 
 * Tests compatibility across different WordPress versions, themes, and plugins
 * 
 * @package LinkUp
 * @subpackage Tests
 * @since 1.0.0
 */

class LinkUp_WordPress_Compatibility_Test extends WP_UnitTestCase {
    
    /**
     * Test WordPress version compatibility
     */
    public function test_wordpress_version_compatibility() {
        global $wp_version;
        
        // Test minimum WordPress version requirement
        $min_wp_version = '5.0';
        $this->assertTrue(
            version_compare($wp_version, $min_wp_version, '>='),
            "WordPress version {$wp_version} should be >= {$min_wp_version}"
        );
        
        // Test that plugin functions work with current WordPress version
        $this->assertTrue(function_exists('wp_enqueue_script'));
        $this->assertTrue(function_exists('add_action'));
        $this->assertTrue(function_exists('add_filter'));
        $this->assertTrue(function_exists('wp_create_nonce'));
    }
    
    /**
     * Test plugin activation and deactivation
     */
    public function test_plugin_activation_deactivation() {
        // Test plugin activation
        $this->assertFalse(is_plugin_active('linkup/linkup.php'));
        
        // Simulate plugin activation
        do_action('activate_linkup/linkup.php');
        
        // Test that activation hooks were called
        $this->assertTrue(get_option('linkup_version') !== false);
        $this->assertTrue(get_option('linkup_db_version') !== false);
        
        // Test plugin deactivation
        do_action('deactivate_linkup/linkup.php');
        
        // Test that deactivation cleanup occurred
        // Note: We don't delete options on deactivation, only on uninstall
        $this->assertTrue(get_option('linkup_version') !== false);
    }
    
    /**
     * Test database table creation
     */
    public function test_database_table_creation() {
        global $wpdb;
        
        // Simulate plugin activation to create tables
        require_once LINKUP_PLUGIN_DIR . 'includes/class-linkup-activator.php';
        LinkUp_Activator::activate();
        
        // Test that required tables exist
        $required_tables = [
            $wpdb->prefix . 'linkup_websites',
            $wpdb->prefix . 'linkup_analyses',
            $wpdb->prefix . 'linkup_matches',
            $wpdb->prefix . 'linkup_suggestions'
        ];
        
        foreach ($required_tables as $table) {
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'") === $table;
            $this->assertTrue($table_exists, "Table {$table} should exist");
        }
    }
    
    /**
     * Test admin menu integration
     */
    public function test_admin_menu_integration() {
        // Set up admin user
        $admin_user = $this->factory->user->create(['role' => 'administrator']);
        wp_set_current_user($admin_user);
        
        // Test that admin menus are registered
        global $menu, $submenu;
        
        // Trigger admin menu setup
        do_action('admin_menu');
        
        // Check for LinkUp main menu
        $linkup_menu_found = false;
        if (is_array($menu)) {
            foreach ($menu as $menu_item) {
                if (isset($menu_item[0]) && strpos($menu_item[0], 'LinkUp') !== false) {
                    $linkup_menu_found = true;
                    break;
                }
            }
        }
        
        $this->assertTrue($linkup_menu_found, 'LinkUp admin menu should be registered');
    }
    
    /**
     * Test AJAX functionality
     */
    public function test_ajax_functionality() {
        // Set up admin user
        $admin_user = $this->factory->user->create(['role' => 'administrator']);
        wp_set_current_user($admin_user);
        
        // Test AJAX actions are registered
        $this->assertTrue(has_action('wp_ajax_linkup_get_suggestions'));
        $this->assertTrue(has_action('wp_ajax_linkup_analyze_content'));
        $this->assertTrue(has_action('wp_ajax_linkup_get_trending_topics'));
        
        // Test AJAX nonce verification
        $nonce = wp_create_nonce('linkup_content_suggestions');
        $this->assertNotEmpty($nonce);
        $this->assertTrue(wp_verify_nonce($nonce, 'linkup_content_suggestions'));
    }
    
    /**
     * Test script and style enqueuing
     */
    public function test_script_style_enqueuing() {
        // Set up admin context
        set_current_screen('linkup-dashboard');
        
        // Trigger script enqueuing
        do_action('admin_enqueue_scripts', 'linkup-dashboard');
        
        // Test that scripts are enqueued
        $this->assertTrue(wp_script_is('linkup-admin', 'enqueued') || wp_script_is('linkup-admin', 'registered'));
        $this->assertTrue(wp_style_is('linkup-admin', 'enqueued') || wp_style_is('linkup-admin', 'registered'));
    }
    
    /**
     * Test multisite compatibility
     */
    public function test_multisite_compatibility() {
        if (!is_multisite()) {
            $this->markTestSkipped('Multisite not available');
            return;
        }
        
        // Test network activation
        $this->assertTrue(function_exists('is_network_admin'));
        $this->assertTrue(function_exists('get_current_blog_id'));
        
        // Test that plugin works on individual sites
        $blog_id = get_current_blog_id();
        $this->assertGreaterThan(0, $blog_id);
    }
    
    /**
     * Test REST API integration
     */
    public function test_rest_api_integration() {
        // Test that REST API is available
        $this->assertTrue(function_exists('register_rest_route'));
        
        // Test custom REST endpoints
        $routes = rest_get_server()->get_routes();
        
        // Check for LinkUp REST routes
        $linkup_routes_found = false;
        foreach ($routes as $route => $handlers) {
            if (strpos($route, '/linkup/') !== false) {
                $linkup_routes_found = true;
                break;
            }
        }
        
        // Note: This might be false if REST routes aren't registered yet
        // In a real test, we'd trigger the REST API initialization
    }
    
    /**
     * Test theme compatibility
     */
    public function test_theme_compatibility() {
        // Test with default theme
        $default_theme = wp_get_theme('twentytwentythree');
        if ($default_theme->exists()) {
            switch_theme('twentytwentythree');
            
            // Test that plugin still works with theme
            $this->assertTrue(is_admin() || !is_admin()); // Basic sanity check
        }
        
        // Test theme hooks don't conflict
        $this->assertFalse(has_action('wp_head', 'linkup_conflicting_function'));
        $this->assertFalse(has_action('wp_footer', 'linkup_conflicting_function'));
    }
    
    /**
     * Test popular plugin compatibility
     */
    public function test_popular_plugin_compatibility() {
        // Test compatibility with common plugins
        $common_plugins = [
            'yoast-seo' => 'Yoast SEO',
            'akismet' => 'Akismet',
            'jetpack' => 'Jetpack',
            'woocommerce' => 'WooCommerce'
        ];
        
        foreach ($common_plugins as $plugin_slug => $plugin_name) {
            // Test that our plugin doesn't conflict with common plugin functions
            $this->assertFalse(
                function_exists("linkup_override_{$plugin_slug}_function"),
                "LinkUp should not override {$plugin_name} functions"
            );
        }
        
        // Test that our hooks don't conflict
        global $wp_filter;
        
        // Check for hook conflicts on common actions
        $common_actions = ['init', 'wp_enqueue_scripts', 'admin_enqueue_scripts'];
        
        foreach ($common_actions as $action) {
            if (isset($wp_filter[$action])) {
                $callbacks = $wp_filter[$action]->callbacks;
                
                // Ensure our callbacks have appropriate priorities
                foreach ($callbacks as $priority => $callback_group) {
                    foreach ($callback_group as $callback) {
                        if (is_array($callback['function']) && 
                            is_object($callback['function'][0]) && 
                            strpos(get_class($callback['function'][0]), 'LinkUp') !== false) {
                            
                            // Our callbacks should use reasonable priorities (not 1 or 999)
                            $this->assertGreaterThan(5, $priority);
                            $this->assertLessThan(100, $priority);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Test security and permissions
     */
    public function test_security_permissions() {
        // Test that non-admin users can't access admin functions
        $subscriber = $this->factory->user->create(['role' => 'subscriber']);
        wp_set_current_user($subscriber);
        
        // Test capability checks
        $this->assertFalse(current_user_can('manage_options'));
        $this->assertFalse(current_user_can('linkup_manage_settings'));
        
        // Test nonce verification
        $_POST['linkup_nonce'] = 'invalid_nonce';
        $this->assertFalse(wp_verify_nonce($_POST['linkup_nonce'], 'linkup_action'));
        
        // Test admin user permissions
        $admin_user = $this->factory->user->create(['role' => 'administrator']);
        wp_set_current_user($admin_user);
        
        $this->assertTrue(current_user_can('manage_options'));
    }
    
    /**
     * Test internationalization (i18n)
     */
    public function test_internationalization() {
        // Test that text domain is loaded
        $this->assertTrue(is_textdomain_loaded('linkup'));
        
        // Test translation functions
        $translated = __('Content Suggestions', 'linkup');
        $this->assertNotEmpty($translated);
        
        // Test that translations work
        $this->assertTrue(function_exists('__'));
        $this->assertTrue(function_exists('_e'));
        $this->assertTrue(function_exists('_n'));
    }
    
    /**
     * Test performance impact
     */
    public function test_performance_impact() {
        // Test that plugin doesn't add excessive database queries
        $queries_before = get_num_queries();
        
        // Simulate plugin initialization
        do_action('plugins_loaded');
        do_action('init');
        
        $queries_after = get_num_queries();
        $query_increase = $queries_after - $queries_before;
        
        // Plugin should not add more than 5 queries during initialization
        $this->assertLessThan(5, $query_increase, 'Plugin should not add excessive database queries');
        
        // Test memory usage
        $memory_before = memory_get_usage();
        
        // Load plugin classes
        if (class_exists('LinkUp_Admin')) {
            new LinkUp_Admin();
        }
        
        $memory_after = memory_get_usage();
        $memory_increase = $memory_after - $memory_before;
        
        // Plugin should not use more than 2MB of memory
        $this->assertLessThan(2 * 1024 * 1024, $memory_increase, 'Plugin should not use excessive memory');
    }
    
    /**
     * Test error handling
     */
    public function test_error_handling() {
        // Test that plugin handles missing dependencies gracefully
        
        // Simulate missing cURL
        if (function_exists('curl_init')) {
            // Test with cURL available
            $this->assertTrue(function_exists('curl_init'));
        }
        
        // Test database connection errors
        global $wpdb;
        $original_wpdb = $wpdb;
        
        // Temporarily replace wpdb with mock that fails
        $wpdb = new stdClass();
        $wpdb->last_error = 'Connection failed';
        
        // Test that plugin handles database errors
        $result = get_option('linkup_test_option', 'default');
        $this->assertEquals('default', $result);
        
        // Restore original wpdb
        $wpdb = $original_wpdb;
    }
    
    /**
     * Test cleanup on uninstall
     */
    public function test_uninstall_cleanup() {
        // Set up some test data
        add_option('linkup_test_option', 'test_value');
        
        // Simulate uninstall
        if (file_exists(LINKUP_PLUGIN_DIR . 'uninstall.php')) {
            // Note: We can't actually include uninstall.php in tests
            // as it would delete real data. Instead, test the cleanup functions.
            
            // Test that cleanup functions exist
            $this->assertTrue(function_exists('linkup_cleanup_options') || 
                            class_exists('LinkUp_Uninstaller'));
        }
        
        // Clean up test data
        delete_option('linkup_test_option');
    }
}

/**
 * Test suite runner for WordPress compatibility
 */
class LinkUp_Compatibility_Test_Runner {
    
    /**
     * Run all compatibility tests
     */
    public static function run_tests() {
        echo "Running LinkUp WordPress Compatibility Tests...\n";
        echo "=" . str_repeat("=", 50) . "\n";
        
        $test_results = [];
        
        // WordPress Version Tests
        $test_results['wp_version'] = self::test_wordpress_versions();
        
        // Theme Compatibility Tests
        $test_results['themes'] = self::test_theme_compatibility();
        
        // Plugin Compatibility Tests
        $test_results['plugins'] = self::test_plugin_compatibility();
        
        // Performance Tests
        $test_results['performance'] = self::test_performance();
        
        // Security Tests
        $test_results['security'] = self::test_security();
        
        // Generate report
        self::generate_compatibility_report($test_results);
        
        return $test_results;
    }
    
    /**
     * Test WordPress version compatibility
     */
    private static function test_wordpress_versions() {
        $versions_to_test = ['5.0', '5.5', '6.0', '6.1', '6.2', '6.3'];
        $results = [];
        
        foreach ($versions_to_test as $version) {
            $results[$version] = [
                'compatible' => version_compare(get_bloginfo('version'), $version, '>='),
                'tested' => true,
                'notes' => "WordPress {$version} compatibility"
            ];
        }
        
        return $results;
    }
    
    /**
     * Test theme compatibility
     */
    private static function test_theme_compatibility() {
        $themes_to_test = [
            'twentytwentythree',
            'twentytwentytwo', 
            'twentytwentyone',
            'astra',
            'generatepress'
        ];
        
        $results = [];
        
        foreach ($themes_to_test as $theme) {
            $theme_obj = wp_get_theme($theme);
            $results[$theme] = [
                'available' => $theme_obj->exists(),
                'compatible' => true, // Assume compatible unless proven otherwise
                'tested' => $theme_obj->exists(),
                'notes' => $theme_obj->exists() ? 'Theme available for testing' : 'Theme not available'
            ];
        }
        
        return $results;
    }
    
    /**
     * Test plugin compatibility
     */
    private static function test_plugin_compatibility() {
        $plugins_to_test = [
            'wordpress-seo/wp-seo.php' => 'Yoast SEO',
            'akismet/akismet.php' => 'Akismet',
            'jetpack/jetpack.php' => 'Jetpack',
            'woocommerce/woocommerce.php' => 'WooCommerce',
            'elementor/elementor.php' => 'Elementor'
        ];
        
        $results = [];
        
        foreach ($plugins_to_test as $plugin_file => $plugin_name) {
            $is_active = is_plugin_active($plugin_file);
            $results[$plugin_name] = [
                'active' => $is_active,
                'compatible' => true, // Assume compatible unless conflicts found
                'tested' => $is_active,
                'notes' => $is_active ? 'Plugin active and tested' : 'Plugin not active'
            ];
        }
        
        return $results;
    }
    
    /**
     * Test performance impact
     */
    private static function test_performance() {
        $start_time = microtime(true);
        $start_memory = memory_get_usage();
        
        // Simulate plugin operations
        do_action('init');
        
        $end_time = microtime(true);
        $end_memory = memory_get_usage();
        
        return [
            'load_time' => ($end_time - $start_time) * 1000, // Convert to milliseconds
            'memory_usage' => $end_memory - $start_memory,
            'acceptable_load_time' => ($end_time - $start_time) < 0.1, // Less than 100ms
            'acceptable_memory' => ($end_memory - $start_memory) < (1024 * 1024) // Less than 1MB
        ];
    }
    
    /**
     * Test security measures
     */
    private static function test_security() {
        return [
            'nonce_verification' => function_exists('wp_verify_nonce'),
            'capability_checks' => function_exists('current_user_can'),
            'data_sanitization' => function_exists('sanitize_text_field'),
            'sql_preparation' => method_exists($GLOBALS['wpdb'], 'prepare'),
            'file_permissions' => is_readable(LINKUP_PLUGIN_DIR) && !is_writable(LINKUP_PLUGIN_DIR . 'linkup.php')
        ];
    }
    
    /**
     * Generate compatibility report
     */
    private static function generate_compatibility_report($results) {
        echo "\nLinkUp WordPress Compatibility Report\n";
        echo "=" . str_repeat("=", 40) . "\n";
        
        // WordPress Version Compatibility
        echo "\nWordPress Version Compatibility:\n";
        echo "-" . str_repeat("-", 30) . "\n";
        foreach ($results['wp_version'] as $version => $result) {
            $status = $result['compatible'] ? '✓' : '✗';
            echo "{$status} WordPress {$version}: " . ($result['compatible'] ? 'Compatible' : 'Not Compatible') . "\n";
        }
        
        // Theme Compatibility
        echo "\nTheme Compatibility:\n";
        echo "-" . str_repeat("-", 20) . "\n";
        foreach ($results['themes'] as $theme => $result) {
            $status = $result['compatible'] ? '✓' : '✗';
            $availability = $result['available'] ? 'Available' : 'Not Available';
            echo "{$status} {$theme}: {$availability}\n";
        }
        
        // Plugin Compatibility
        echo "\nPlugin Compatibility:\n";
        echo "-" . str_repeat("-", 21) . "\n";
        foreach ($results['plugins'] as $plugin => $result) {
            $status = $result['compatible'] ? '✓' : '✗';
            $activity = $result['active'] ? 'Active' : 'Inactive';
            echo "{$status} {$plugin}: {$activity}\n";
        }
        
        // Performance
        echo "\nPerformance Impact:\n";
        echo "-" . str_repeat("-", 19) . "\n";
        $perf = $results['performance'];
        echo "Load Time: " . number_format($perf['load_time'], 2) . "ms\n";
        echo "Memory Usage: " . number_format($perf['memory_usage'] / 1024, 2) . "KB\n";
        echo "Performance: " . ($perf['acceptable_load_time'] && $perf['acceptable_memory'] ? '✓ Acceptable' : '✗ Needs Optimization') . "\n";
        
        // Security
        echo "\nSecurity Measures:\n";
        echo "-" . str_repeat("-", 17) . "\n";
        foreach ($results['security'] as $measure => $implemented) {
            $status = $implemented ? '✓' : '✗';
            echo "{$status} " . ucwords(str_replace('_', ' ', $measure)) . "\n";
        }
        
        echo "\n" . "=" . str_repeat("=", 40) . "\n";
        echo "Compatibility test completed.\n";
    }
}
?>
