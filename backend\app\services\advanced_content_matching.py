"""
Advanced Content Relevance Matching Service
Extends beyond TF-IDF to include semantic similarity, topic modeling, and contextual analysis
"""
import logging
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
from collections import defaultdict
import re

# ML/NLP imports (would need to be installed)
try:
    from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
    from sklearn.decomposition import LatentDirichletAllocation
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.preprocessing import normalize
    import nltk
    from nltk.corpus import stopwords
    from nltk.tokenize import word_tokenize, sent_tokenize
    from nltk.stem import WordNetLemmatizer
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

from app.models.analysis import ContentAnalysis
from app import cache

logger = logging.getLogger(__name__)


class AdvancedContentMatcher:
    """Advanced content matching using multiple similarity algorithms"""
    
    def __init__(self):
        """Initialize the advanced content matcher"""
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 3),
            min_df=1,
            max_df=0.8
        )
        
        # Topic modeling
        self.lda_model = None
        self.topic_vectorizer = CountVectorizer(
            max_features=500,
            stop_words='english',
            min_df=2,
            max_df=0.8
        )
        
        # Language-specific configurations
        self.language_configs = {
            'en': {'stopwords': 'english', 'stemmer': 'english'},
            'es': {'stopwords': 'spanish', 'stemmer': 'spanish'},
            'fr': {'stopwords': 'french', 'stemmer': 'french'},
            'de': {'stopwords': 'german', 'stemmer': 'german'},
        }
        
        # Semantic similarity weights
        self.similarity_weights = {
            'tfidf_similarity': 0.3,
            'topic_similarity': 0.25,
            'semantic_similarity': 0.2,
            'keyword_overlap': 0.15,
            'contextual_relevance': 0.1
        }
        
        # Initialize NLTK components if available
        if NLTK_AVAILABLE:
            try:
                nltk.download('punkt', quiet=True)
                nltk.download('stopwords', quiet=True)
                nltk.download('wordnet', quiet=True)
                self.lemmatizer = WordNetLemmatizer()
            except Exception as e:
                logger.warning(f"NLTK initialization failed: {str(e)}")
                self.lemmatizer = None
        else:
            self.lemmatizer = None
    
    def calculate_advanced_similarity(self, source_analysis: ContentAnalysis, 
                                    target_analysis: ContentAnalysis) -> Dict[str, float]:
        """Calculate advanced content similarity using multiple methods"""
        try:
            results = {}
            
            # 1. Enhanced TF-IDF Similarity
            results['tfidf_similarity'] = self._calculate_enhanced_tfidf_similarity(
                source_analysis, target_analysis
            )
            
            # 2. Topic Modeling Similarity
            results['topic_similarity'] = self._calculate_topic_similarity(
                source_analysis, target_analysis
            )
            
            # 3. Semantic Similarity (simplified - would use word embeddings in production)
            results['semantic_similarity'] = self._calculate_semantic_similarity(
                source_analysis, target_analysis
            )
            
            # 4. Keyword Overlap Analysis
            results['keyword_overlap'] = self._calculate_keyword_overlap(
                source_analysis, target_analysis
            )
            
            # 5. Contextual Relevance
            results['contextual_relevance'] = self._calculate_contextual_relevance(
                source_analysis, target_analysis
            )
            
            # Calculate weighted overall similarity
            overall_similarity = sum([
                results[key] * self.similarity_weights[key]
                for key in self.similarity_weights.keys()
                if key in results
            ])
            
            results['overall_similarity'] = min(1.0, max(0.0, overall_similarity))
            results['confidence_score'] = self._calculate_confidence_score(results)
            
            return results
            
        except Exception as e:
            logger.error(f"Advanced similarity calculation failed: {str(e)}")
            return {'overall_similarity': 0.0, 'confidence_score': 0.0}
    
    def _calculate_enhanced_tfidf_similarity(self, source_analysis: ContentAnalysis,
                                           target_analysis: ContentAnalysis) -> float:
        """Enhanced TF-IDF similarity with preprocessing"""
        try:
            # Extract and preprocess content
            source_content = self._extract_content_for_analysis(source_analysis)
            target_content = self._extract_content_for_analysis(target_analysis)
            
            if not source_content or not target_content:
                return 0.0
            
            # Apply language-specific preprocessing
            source_lang = getattr(source_analysis, 'language', 'en') or 'en'
            target_lang = getattr(target_analysis, 'language', 'en') or 'en'
            
            # For now, only process if same language
            if source_lang != target_lang:
                return 0.0
            
            # Preprocess content
            source_processed = self._preprocess_content(source_content, source_lang)
            target_processed = self._preprocess_content(target_content, target_lang)
            
            # Calculate TF-IDF similarity
            tfidf_matrix = self.tfidf_vectorizer.fit_transform([source_processed, target_processed])
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            
            return float(similarity)
            
        except Exception as e:
            logger.warning(f"Enhanced TF-IDF calculation failed: {str(e)}")
            return 0.0
    
    def _calculate_topic_similarity(self, source_analysis: ContentAnalysis,
                                  target_analysis: ContentAnalysis) -> float:
        """Calculate topic-based similarity using LDA"""
        try:
            # Extract topics from analyses
            source_topics = source_analysis.topics.get('primary_topics', []) if source_analysis.topics else []
            target_topics = target_analysis.topics.get('primary_topics', []) if target_analysis.topics else []
            
            if not source_topics or not target_topics:
                return 0.0
            
            # Create topic vectors
            source_topic_vector = self._create_topic_vector(source_topics)
            target_topic_vector = self._create_topic_vector(target_topics)
            
            # Calculate cosine similarity between topic vectors
            similarity = cosine_similarity([source_topic_vector], [target_topic_vector])[0][0]
            
            return float(similarity)
            
        except Exception as e:
            logger.warning(f"Topic similarity calculation failed: {str(e)}")
            return 0.0
    
    def _calculate_semantic_similarity(self, source_analysis: ContentAnalysis,
                                     target_analysis: ContentAnalysis) -> float:
        """Calculate semantic similarity (simplified version)"""
        try:
            # In a production system, this would use word embeddings like Word2Vec, GloVe, or BERT
            # For now, we'll use a simplified approach based on keyword semantic relationships
            
            source_keywords = source_analysis.keywords.get('primary_keywords', []) if source_analysis.keywords else []
            target_keywords = target_analysis.keywords.get('primary_keywords', []) if target_analysis.keywords else []
            
            if not source_keywords or not target_keywords:
                return 0.0
            
            # Extract keyword text
            source_terms = [kw.get('keyword', '').lower() for kw in source_keywords]
            target_terms = [kw.get('keyword', '').lower() for kw in target_keywords]
            
            # Calculate semantic relationships (simplified)
            semantic_matches = 0
            total_comparisons = 0
            
            for source_term in source_terms:
                for target_term in target_terms:
                    total_comparisons += 1
                    if self._are_semantically_related(source_term, target_term):
                        semantic_matches += 1
            
            return semantic_matches / total_comparisons if total_comparisons > 0 else 0.0
            
        except Exception as e:
            logger.warning(f"Semantic similarity calculation failed: {str(e)}")
            return 0.0
    
    def _calculate_keyword_overlap(self, source_analysis: ContentAnalysis,
                                 target_analysis: ContentAnalysis) -> float:
        """Calculate keyword overlap with importance weighting"""
        try:
            source_keywords = source_analysis.keywords.get('primary_keywords', []) if source_analysis.keywords else []
            target_keywords = target_analysis.keywords.get('primary_keywords', []) if target_analysis.keywords else []
            
            if not source_keywords or not target_keywords:
                return 0.0
            
            # Create weighted keyword sets
            source_weighted = {}
            target_weighted = {}
            
            for kw in source_keywords:
                term = kw.get('keyword', '').lower()
                weight = kw.get('importance', 1.0)
                source_weighted[term] = weight
            
            for kw in target_keywords:
                term = kw.get('keyword', '').lower()
                weight = kw.get('importance', 1.0)
                target_weighted[term] = weight
            
            # Calculate weighted overlap
            overlap_score = 0.0
            total_weight = 0.0
            
            all_terms = set(source_weighted.keys()) | set(target_weighted.keys())
            
            for term in all_terms:
                source_weight = source_weighted.get(term, 0)
                target_weight = target_weighted.get(term, 0)
                
                if source_weight > 0 and target_weight > 0:
                    # Both have the term - add to overlap
                    overlap_score += min(source_weight, target_weight)
                
                total_weight += max(source_weight, target_weight)
            
            return overlap_score / total_weight if total_weight > 0 else 0.0
            
        except Exception as e:
            logger.warning(f"Keyword overlap calculation failed: {str(e)}")
            return 0.0
    
    def _calculate_contextual_relevance(self, source_analysis: ContentAnalysis,
                                      target_analysis: ContentAnalysis) -> float:
        """Calculate contextual relevance based on content structure and intent"""
        try:
            relevance_score = 0.0
            factors = 0
            
            # Factor 1: Content type similarity
            source_structure = source_analysis.content_structure if hasattr(source_analysis, 'content_structure') else {}
            target_structure = target_analysis.content_structure if hasattr(target_analysis, 'content_structure') else {}
            
            if source_structure and target_structure:
                structure_similarity = self._compare_content_structures(source_structure, target_structure)
                relevance_score += structure_similarity
                factors += 1
            
            # Factor 2: Content depth similarity
            source_depth = self._calculate_content_depth(source_analysis)
            target_depth = self._calculate_content_depth(target_analysis)
            
            if source_depth > 0 and target_depth > 0:
                depth_similarity = min(source_depth, target_depth) / max(source_depth, target_depth)
                relevance_score += depth_similarity
                factors += 1
            
            # Factor 3: Content freshness compatibility
            freshness_compatibility = self._calculate_freshness_compatibility(source_analysis, target_analysis)
            relevance_score += freshness_compatibility
            factors += 1
            
            return relevance_score / factors if factors > 0 else 0.0
            
        except Exception as e:
            logger.warning(f"Contextual relevance calculation failed: {str(e)}")
            return 0.0

    def _extract_content_for_analysis(self, analysis: ContentAnalysis) -> str:
        """Extract content text from analysis for processing"""
        content_parts = []

        # Add keywords
        if analysis.keywords:
            keywords = analysis.keywords.get('primary_keywords', [])
            keyword_text = ' '.join([kw.get('keyword', '') for kw in keywords])
            content_parts.append(keyword_text)

        # Add topics
        if analysis.topics:
            topics = analysis.topics.get('primary_topics', [])
            topic_text = ' '.join([topic.get('topic', '') for topic in topics])
            content_parts.append(topic_text)

        # Add any other content fields available
        if hasattr(analysis, 'content_summary') and analysis.content_summary:
            content_parts.append(analysis.content_summary)

        return ' '.join(content_parts)

    def _preprocess_content(self, content: str, language: str = 'en') -> str:
        """Preprocess content for analysis"""
        if not content:
            return ""

        # Basic cleaning
        content = re.sub(r'[^\w\s]', ' ', content.lower())
        content = re.sub(r'\s+', ' ', content).strip()

        # Language-specific processing
        if NLTK_AVAILABLE and self.lemmatizer:
            try:
                # Tokenize
                tokens = word_tokenize(content)

                # Remove stopwords
                lang_config = self.language_configs.get(language, self.language_configs['en'])
                stop_words = set(stopwords.words(lang_config['stopwords']))
                tokens = [token for token in tokens if token not in stop_words and len(token) > 2]

                # Lemmatize
                tokens = [self.lemmatizer.lemmatize(token) for token in tokens]

                return ' '.join(tokens)
            except Exception as e:
                logger.warning(f"NLTK preprocessing failed: {str(e)}")

        return content

    def _create_topic_vector(self, topics: List[Dict]) -> List[float]:
        """Create a vector representation of topics"""
        # Simplified topic vector creation
        # In production, this would use proper topic modeling

        topic_weights = {}
        for topic in topics:
            topic_name = topic.get('topic', '')
            weight = topic.get('weight', 1.0)
            topic_weights[topic_name] = weight

        # Create a fixed-size vector (simplified)
        common_topics = [
            'technology', 'business', 'health', 'education', 'lifestyle',
            'finance', 'marketing', 'science', 'entertainment', 'sports'
        ]

        vector = []
        for topic in common_topics:
            vector.append(topic_weights.get(topic, 0.0))

        # Normalize vector
        total = sum(vector)
        if total > 0:
            vector = [v / total for v in vector]

        return vector

    def _are_semantically_related(self, term1: str, term2: str) -> bool:
        """Check if two terms are semantically related (simplified)"""
        # In production, this would use word embeddings or semantic networks
        # For now, use a simple rule-based approach

        if term1 == term2:
            return True

        # Simple semantic relationships
        semantic_groups = {
            'technology': ['tech', 'software', 'digital', 'computer', 'internet', 'web'],
            'business': ['company', 'corporate', 'enterprise', 'commercial', 'industry'],
            'health': ['medical', 'wellness', 'fitness', 'healthcare', 'medicine'],
            'education': ['learning', 'teaching', 'academic', 'school', 'university'],
            'finance': ['money', 'financial', 'banking', 'investment', 'economic']
        }

        # Check if terms belong to the same semantic group
        for group, terms in semantic_groups.items():
            if (term1 in terms or term1 == group) and (term2 in terms or term2 == group):
                return True

        return False

    def _compare_content_structures(self, structure1: Dict, structure2: Dict) -> float:
        """Compare content structures for similarity"""
        try:
            similarity_score = 0.0
            comparisons = 0

            # Compare heading structures
            if 'headings' in structure1 and 'headings' in structure2:
                h1_1 = structure1['headings'].get('h1', 0)
                h1_2 = structure2['headings'].get('h1', 0)
                h2_1 = structure1['headings'].get('h2', 0)
                h2_2 = structure2['headings'].get('h2', 0)

                # Similar heading patterns indicate similar content structure
                if h1_1 > 0 and h1_2 > 0:
                    similarity_score += min(h1_1, h1_2) / max(h1_1, h1_2)
                    comparisons += 1

                if h2_1 > 0 and h2_2 > 0:
                    similarity_score += min(h2_1, h2_2) / max(h2_1, h2_2)
                    comparisons += 1

            # Compare list usage
            if 'lists' in structure1 and 'lists' in structure2:
                lists_1 = structure1['lists'].get('total', 0)
                lists_2 = structure2['lists'].get('total', 0)

                if lists_1 > 0 and lists_2 > 0:
                    similarity_score += min(lists_1, lists_2) / max(lists_1, lists_2)
                    comparisons += 1

            return similarity_score / comparisons if comparisons > 0 else 0.5

        except Exception as e:
            logger.warning(f"Structure comparison failed: {str(e)}")
            return 0.5

    def _calculate_content_depth(self, analysis: ContentAnalysis) -> float:
        """Calculate content depth score"""
        depth_score = 0.0

        # Factor 1: Keyword diversity
        if analysis.keywords:
            keyword_count = len(analysis.keywords.get('primary_keywords', []))
            depth_score += min(1.0, keyword_count / 20)  # Normalize to 20 keywords

        # Factor 2: Topic diversity
        if analysis.topics:
            topic_count = len(analysis.topics.get('primary_topics', []))
            depth_score += min(1.0, topic_count / 10)  # Normalize to 10 topics

        # Factor 3: Quality score
        if analysis.quality_score:
            depth_score += min(1.0, analysis.quality_score / 10)

        return depth_score / 3  # Average of factors

    def _calculate_freshness_compatibility(self, source_analysis: ContentAnalysis,
                                         target_analysis: ContentAnalysis) -> float:
        """Calculate freshness compatibility between content"""
        try:
            if not source_analysis.analyzed_at or not target_analysis.analyzed_at:
                return 0.5  # Neutral if no timestamp data

            # Calculate age difference
            age_diff = abs((source_analysis.analyzed_at - target_analysis.analyzed_at).days)

            # Prefer content analyzed around the same time
            if age_diff <= 7:
                return 1.0
            elif age_diff <= 30:
                return 0.8
            elif age_diff <= 90:
                return 0.6
            else:
                return 0.4

        except Exception as e:
            logger.warning(f"Freshness compatibility calculation failed: {str(e)}")
            return 0.5

    def _calculate_confidence_score(self, results: Dict[str, float]) -> float:
        """Calculate confidence score for the similarity results"""
        # Higher confidence when multiple methods agree
        scores = [results.get(key, 0) for key in self.similarity_weights.keys()]

        # Calculate variance - lower variance means higher confidence
        mean_score = sum(scores) / len(scores)
        variance = sum([(score - mean_score) ** 2 for score in scores]) / len(scores)

        # Convert variance to confidence (inverse relationship)
        confidence = max(0.0, 1.0 - variance)

        return confidence

    def get_multi_language_similarity(self, source_analysis: ContentAnalysis,
                                    target_analysis: ContentAnalysis) -> Dict[str, float]:
        """Calculate similarity for multi-language content"""
        try:
            source_lang = getattr(source_analysis, 'language', 'en') or 'en'
            target_lang = getattr(target_analysis, 'language', 'en') or 'en'

            if source_lang == target_lang:
                # Same language - use full analysis
                return self.calculate_advanced_similarity(source_analysis, target_analysis)
            else:
                # Different languages - limited analysis
                results = {
                    'tfidf_similarity': 0.0,  # Can't compare different languages directly
                    'topic_similarity': self._calculate_topic_similarity(source_analysis, target_analysis),
                    'semantic_similarity': 0.0,  # Would need translation
                    'keyword_overlap': 0.0,  # Different languages
                    'contextual_relevance': self._calculate_contextual_relevance(source_analysis, target_analysis)
                }

                # Calculate weighted score with available methods
                available_weights = {
                    'topic_similarity': 0.6,
                    'contextual_relevance': 0.4
                }

                overall_similarity = sum([
                    results[key] * available_weights[key]
                    for key in available_weights.keys()
                    if key in results
                ])

                results['overall_similarity'] = overall_similarity
                results['confidence_score'] = 0.3  # Lower confidence for cross-language
                results['language_penalty'] = True

                return results

        except Exception as e:
            logger.error(f"Multi-language similarity calculation failed: {str(e)}")
            return {'overall_similarity': 0.0, 'confidence_score': 0.0}
