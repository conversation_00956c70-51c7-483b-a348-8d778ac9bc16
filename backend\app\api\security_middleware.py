"""
Security Middleware for LinkUp API
Implements comprehensive security measures for API endpoints
"""
import logging
import hashlib
import hmac
import time
from functools import wraps
from flask import request, jsonify, current_app, g
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity, get_jwt
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta

from app import db, cache
from app.models.user import User

logger = logging.getLogger(__name__)


class APISecurityManager:
    """Manages API security features"""
    
    def __init__(self):
        """Initialize security manager"""
        self.rate_limit_cache_prefix = "rate_limit:"
        self.api_key_cache_prefix = "api_key:"
        self.request_signature_cache_prefix = "req_sig:"
        
        # Security settings
        self.max_request_size = 10 * 1024 * 1024  # 10MB
        self.signature_timeout = 300  # 5 minutes
        self.suspicious_activity_threshold = 100  # requests per minute
        
        # Blocked IPs and user agents
        self.blocked_ips = set()
        self.blocked_user_agents = {
            'bot', 'crawler', 'spider', 'scraper', 'automated'
        }
    
    def validate_api_key(self, api_key: str) -> Optional[Dict]:
        """Validate API key and return associated data"""
        try:
            # Check cache first
            cache_key = f"{self.api_key_cache_prefix}{api_key}"
            cached_data = cache.get(cache_key)
            
            if cached_data:
                return cached_data
            
            # In a real implementation, you'd validate against a database
            # For now, we'll use a simple validation
            if api_key.startswith('lnk_') and len(api_key) == 32:
                api_data = {
                    'valid': True,
                    'user_id': None,  # Would be extracted from database
                    'permissions': ['read', 'write'],
                    'rate_limit': 1000  # requests per hour
                }
                
                # Cache for 1 hour
                cache.set(cache_key, api_data, timeout=3600)
                return api_data
            
            return None
            
        except Exception as e:
            logger.error(f"Error validating API key: {str(e)}")
            return None
    
    def verify_request_signature(self, signature: str, payload: str, secret: str) -> bool:
        """Verify HMAC signature for request integrity"""
        try:
            expected_signature = hmac.new(
                secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            logger.error(f"Error verifying signature: {str(e)}")
            return False
    
    def check_rate_limit(self, identifier: str, limit: int, window: int = 3600) -> Dict:
        """Check rate limit for identifier"""
        try:
            cache_key = f"{self.rate_limit_cache_prefix}{identifier}"
            current_time = int(time.time())
            window_start = current_time - window
            
            # Get current requests
            requests = cache.get(cache_key) or []
            
            # Filter requests within window
            recent_requests = [req_time for req_time in requests if req_time > window_start]
            
            # Check if limit exceeded
            if len(recent_requests) >= limit:
                return {
                    'allowed': False,
                    'current_count': len(recent_requests),
                    'limit': limit,
                    'reset_time': window_start + window
                }
            
            # Add current request
            recent_requests.append(current_time)
            cache.set(cache_key, recent_requests, timeout=window)
            
            return {
                'allowed': True,
                'current_count': len(recent_requests),
                'limit': limit,
                'remaining': limit - len(recent_requests)
            }
            
        except Exception as e:
            logger.error(f"Error checking rate limit: {str(e)}")
            return {'allowed': True, 'current_count': 0, 'limit': limit}
    
    def is_suspicious_request(self, request_data: Dict) -> bool:
        """Detect suspicious request patterns"""
        try:
            # Check IP reputation
            client_ip = request_data.get('ip')
            if client_ip in self.blocked_ips:
                return True
            
            # Check user agent
            user_agent = request_data.get('user_agent', '').lower()
            if any(blocked_agent in user_agent for blocked_agent in self.blocked_user_agents):
                return True
            
            # Check request frequency
            rate_check = self.check_rate_limit(
                f"suspicious:{client_ip}", 
                self.suspicious_activity_threshold, 
                60  # 1 minute window
            )
            
            if not rate_check['allowed']:
                logger.warning(f"Suspicious activity detected from IP: {client_ip}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking suspicious request: {str(e)}")
            return False
    
    def log_security_event(self, event_type: str, details: Dict):
        """Log security events for monitoring"""
        try:
            security_log = {
                'event_type': event_type,
                'timestamp': datetime.utcnow().isoformat(),
                'ip_address': request.remote_addr,
                'user_agent': request.headers.get('User-Agent'),
                'endpoint': request.endpoint,
                'method': request.method,
                'details': details
            }
            
            # In production, this would go to a security monitoring system
            logger.warning(f"Security Event: {event_type} - {details}")
            
        except Exception as e:
            logger.error(f"Error logging security event: {str(e)}")


# Initialize security manager
security_manager = APISecurityManager()


def require_api_key(f: Callable) -> Callable:
    """Decorator to require valid API key"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            api_key = request.headers.get('X-API-Key')
            
            if not api_key:
                security_manager.log_security_event('missing_api_key', {
                    'endpoint': request.endpoint
                })
                return jsonify({'error': 'API key required'}), 401
            
            api_data = security_manager.validate_api_key(api_key)
            
            if not api_data or not api_data.get('valid'):
                security_manager.log_security_event('invalid_api_key', {
                    'api_key_prefix': api_key[:8] + '...' if len(api_key) > 8 else api_key
                })
                return jsonify({'error': 'Invalid API key'}), 401
            
            # Store API data in request context
            g.api_data = api_data
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"Error in API key validation: {str(e)}")
            return jsonify({'error': 'Authentication error'}), 500
    
    return decorated_function


def require_signature(secret_key: str = None) -> Callable:
    """Decorator to require request signature verification"""
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                signature = request.headers.get('X-Signature')
                timestamp = request.headers.get('X-Timestamp')
                
                if not signature or not timestamp:
                    security_manager.log_security_event('missing_signature', {
                        'endpoint': request.endpoint
                    })
                    return jsonify({'error': 'Request signature required'}), 401
                
                # Check timestamp freshness
                try:
                    request_time = int(timestamp)
                    current_time = int(time.time())
                    
                    if abs(current_time - request_time) > security_manager.signature_timeout:
                        security_manager.log_security_event('expired_signature', {
                            'timestamp_diff': abs(current_time - request_time)
                        })
                        return jsonify({'error': 'Request signature expired'}), 401
                        
                except ValueError:
                    return jsonify({'error': 'Invalid timestamp format'}), 400
                
                # Get request payload
                payload = request.get_data(as_text=True)
                signature_payload = f"{request.method}:{request.path}:{timestamp}:{payload}"
                
                # Use provided secret or get from config
                secret = secret_key or current_app.config.get('API_SECRET_KEY')
                
                if not secret:
                    logger.error("No API secret key configured")
                    return jsonify({'error': 'Server configuration error'}), 500
                
                # Verify signature
                if not security_manager.verify_request_signature(signature, signature_payload, secret):
                    security_manager.log_security_event('invalid_signature', {
                        'endpoint': request.endpoint
                    })
                    return jsonify({'error': 'Invalid request signature'}), 401
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"Error in signature verification: {str(e)}")
                return jsonify({'error': 'Signature verification error'}), 500
        
        return decorated_function
    return decorator


def enhanced_rate_limit(limit: int, window: int = 3600, per: str = 'user') -> Callable:
    """Enhanced rate limiting decorator"""
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Determine identifier based on 'per' parameter
                if per == 'user':
                    try:
                        verify_jwt_in_request()
                        identifier = f"user:{get_jwt_identity()}"
                    except:
                        identifier = f"ip:{request.remote_addr}"
                elif per == 'ip':
                    identifier = f"ip:{request.remote_addr}"
                elif per == 'api_key':
                    api_key = request.headers.get('X-API-Key')
                    identifier = f"api_key:{api_key}" if api_key else f"ip:{request.remote_addr}"
                else:
                    identifier = f"ip:{request.remote_addr}"
                
                # Check rate limit
                rate_check = security_manager.check_rate_limit(identifier, limit, window)
                
                if not rate_check['allowed']:
                    security_manager.log_security_event('rate_limit_exceeded', {
                        'identifier': identifier,
                        'limit': limit,
                        'current_count': rate_check['current_count']
                    })
                    
                    return jsonify({
                        'error': 'Rate limit exceeded',
                        'limit': limit,
                        'window': window,
                        'reset_time': rate_check.get('reset_time')
                    }), 429
                
                # Add rate limit headers to response
                response = f(*args, **kwargs)
                
                if hasattr(response, 'headers'):
                    response.headers['X-RateLimit-Limit'] = str(limit)
                    response.headers['X-RateLimit-Remaining'] = str(rate_check.get('remaining', 0))
                    response.headers['X-RateLimit-Window'] = str(window)
                
                return response
                
            except Exception as e:
                logger.error(f"Error in rate limiting: {str(e)}")
                return f(*args, **kwargs)  # Allow request on error
        
        return decorated_function
    return decorator


def security_headers(f: Callable) -> Callable:
    """Add security headers to response"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        response = f(*args, **kwargs)
        
        if hasattr(response, 'headers'):
            # Security headers
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
            response.headers['Content-Security-Policy'] = "default-src 'self'"
            response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
            
            # API-specific headers
            response.headers['X-API-Version'] = '1.0'
            response.headers['X-Response-Time'] = str(int(time.time() * 1000))
        
        return response
    
    return decorated_function


def validate_request_size(max_size: int = None) -> Callable:
    """Validate request content length"""
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            max_request_size = max_size or security_manager.max_request_size
            
            content_length = request.content_length
            if content_length and content_length > max_request_size:
                security_manager.log_security_event('request_too_large', {
                    'content_length': content_length,
                    'max_size': max_request_size
                })
                return jsonify({'error': 'Request too large'}), 413
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator


def detect_suspicious_activity(f: Callable) -> Callable:
    """Detect and block suspicious activity"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            request_data = {
                'ip': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', ''),
                'endpoint': request.endpoint,
                'method': request.method
            }
            
            if security_manager.is_suspicious_request(request_data):
                security_manager.log_security_event('suspicious_activity_blocked', request_data)
                return jsonify({'error': 'Request blocked due to suspicious activity'}), 403
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"Error in suspicious activity detection: {str(e)}")
            return f(*args, **kwargs)  # Allow request on error
    
    return decorated_function
