"""
Comprehensive Unit Tests for LinkUp Matching System
Tests all components of the enhanced matching system including algorithms, APIs, and integrations
"""
import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import json
from flask import Flask
from flask_testing import TestCase

# Import all services and models
from app import create_app, db
from app.models.website import Website
from app.models.user import User
from app.models.analysis import ContentAnalysis
from app.models.backlink import Backlink
from app.models.matching_preferences import MatchingPreferences
from app.models.blacklist_whitelist import DomainFilter, KeywordFilter

from app.services.matching_service import MatchingService
from app.services.advanced_content_matching import AdvancedContentMatcher
from app.services.niche_matching_service import NicheMatchingService
from app.services.quality_assessment_service import QualityAssessmentService
from app.services.filter_management_service import FilterManagementService
from app.services.preferences_service import PreferencesService
from app.services.match_transparency_service import MatchTransparencyService
from app.database.matching_optimization import MatchingQueryOptimizer


class MatchingSystemTestCase(TestCase):
    """Base test case for matching system tests"""
    
    def create_app(self):
        """Create test app"""
        app = create_app('testing')
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        return app
    
    def setUp(self):
        """Set up test environment"""
        db.create_all()
        self.create_test_data()
        
        # Initialize services
        self.matching_service = MatchingService()
        self.content_matcher = AdvancedContentMatcher()
        self.niche_matcher = NicheMatchingService()
        self.quality_assessor = QualityAssessmentService()
        self.filter_manager = FilterManagementService()
        self.preferences_service = PreferencesService()
        self.transparency_service = MatchTransparencyService()
        self.query_optimizer = MatchingQueryOptimizer()
    
    def tearDown(self):
        """Clean up test environment"""
        db.session.remove()
        db.drop_all()
    
    def create_test_data(self):
        """Create comprehensive test data"""
        # Create test users
        self.user1 = User(
            id=1,
            email='<EMAIL>',
            username='user1',
            status='active'
        )
        self.user2 = User(
            id=2,
            email='<EMAIL>',
            username='user2',
            status='active'
        )
        
        db.session.add_all([self.user1, self.user2])
        
        # Create test websites
        self.website1 = Website(
            id=1,
            user_id=1,
            domain='tech-blog.com',
            title='Tech Blog',
            category='technology',
            domain_authority=45,
            status='active',
            created_at=datetime.utcnow() - timedelta(days=365)
        )
        
        self.website2 = Website(
            id=2,
            user_id=2,
            domain='business-insights.com',
            title='Business Insights',
            category='business',
            domain_authority=52,
            status='active',
            created_at=datetime.utcnow() - timedelta(days=200)
        )
        
        self.website3 = Website(
            id=3,
            user_id=2,
            domain='health-tips.com',
            title='Health Tips',
            category='health',
            domain_authority=38,
            status='active',
            created_at=datetime.utcnow() - timedelta(days=100)
        )
        
        # Low quality website for testing
        self.website4 = Website(
            id=4,
            user_id=2,
            domain='spam-site.tk',
            title='Buy Cheap Products Now!',
            category='commercial',
            domain_authority=5,
            status='active',
            created_at=datetime.utcnow() - timedelta(days=10)
        )
        
        db.session.add_all([self.website1, self.website2, self.website3, self.website4])
        
        # Create content analyses
        self.analysis1 = ContentAnalysis(
            id=1,
            website_id=1,
            quality_score=8.5,
            language='en',
            keywords={
                'primary_keywords': [
                    {'keyword': 'artificial intelligence', 'importance': 0.9},
                    {'keyword': 'machine learning', 'importance': 0.8},
                    {'keyword': 'technology trends', 'importance': 0.7}
                ]
            },
            topics={
                'primary_topics': [
                    {'topic': 'technology', 'weight': 0.8},
                    {'topic': 'artificial intelligence', 'weight': 0.6}
                ]
            },
            analyzed_at=datetime.utcnow() - timedelta(days=5)
        )
        
        self.analysis2 = ContentAnalysis(
            id=2,
            website_id=2,
            quality_score=7.8,
            language='en',
            keywords={
                'primary_keywords': [
                    {'keyword': 'business strategy', 'importance': 0.9},
                    {'keyword': 'digital transformation', 'importance': 0.7},
                    {'keyword': 'technology adoption', 'importance': 0.6}
                ]
            },
            topics={
                'primary_topics': [
                    {'topic': 'business', 'weight': 0.9},
                    {'topic': 'technology', 'weight': 0.4}
                ]
            },
            analyzed_at=datetime.utcnow() - timedelta(days=3)
        )
        
        self.analysis3 = ContentAnalysis(
            id=3,
            website_id=3,
            quality_score=6.5,
            language='en',
            keywords={
                'primary_keywords': [
                    {'keyword': 'healthy lifestyle', 'importance': 0.8},
                    {'keyword': 'nutrition tips', 'importance': 0.7},
                    {'keyword': 'wellness', 'importance': 0.6}
                ]
            },
            topics={
                'primary_topics': [
                    {'topic': 'health', 'weight': 0.9},
                    {'topic': 'lifestyle', 'weight': 0.6}
                ]
            },
            analyzed_at=datetime.utcnow() - timedelta(days=7)
        )
        
        # Low quality analysis
        self.analysis4 = ContentAnalysis(
            id=4,
            website_id=4,
            quality_score=2.1,
            language='en',
            keywords={
                'primary_keywords': [
                    {'keyword': 'buy now', 'importance': 0.9},
                    {'keyword': 'cheap products', 'importance': 0.8},
                    {'keyword': 'limited time', 'importance': 0.7}
                ]
            },
            topics={
                'primary_topics': [
                    {'topic': 'commercial', 'weight': 0.9}
                ]
            },
            analyzed_at=datetime.utcnow() - timedelta(days=1)
        )
        
        db.session.add_all([self.analysis1, self.analysis2, self.analysis3, self.analysis4])
        
        # Create test preferences
        self.preferences1 = MatchingPreferences(
            user_id=1,
            min_quality_score=7.0,
            min_domain_authority=30,
            max_spam_score=2.0,
            min_content_similarity=0.4,
            cross_niche_matching=True,
            auto_reject_low_quality=True,
            auto_reject_threshold=0.3
        )
        
        db.session.add(self.preferences1)
        
        # Create test filters
        self.domain_filter = DomainFilter(
            domain='spam-site.tk',
            filter_type='blacklist',
            scope='global',
            is_active=True,
            reason='Known spam domain'
        )
        
        self.keyword_filter = KeywordFilter(
            keyword='buy now',
            filter_type='blacklist',
            scope='global',
            is_active=True,
            reason='Spam keyword'
        )
        
        db.session.add_all([self.domain_filter, self.keyword_filter])
        
        db.session.commit()


class TestAdvancedContentMatching(MatchingSystemTestCase):
    """Test advanced content matching functionality"""
    
    def test_content_similarity_calculation(self):
        """Test advanced content similarity calculation"""
        similarity_result = self.content_matcher.calculate_advanced_similarity(
            self.analysis1, self.analysis2
        )
        
        self.assertIsInstance(similarity_result, dict)
        self.assertIn('overall_similarity', similarity_result)
        self.assertIn('confidence_score', similarity_result)
        self.assertIn('tfidf_similarity', similarity_result)
        self.assertIn('topic_similarity', similarity_result)
        
        # Should have some similarity due to 'technology' overlap
        self.assertGreater(similarity_result['overall_similarity'], 0.1)
        self.assertLessEqual(similarity_result['overall_similarity'], 1.0)
    
    def test_semantic_relationship_detection(self):
        """Test semantic relationship detection"""
        # Test related terms
        self.assertTrue(self.content_matcher._are_semantically_related('technology', 'tech'))
        self.assertTrue(self.content_matcher._are_semantically_related('business', 'corporate'))
        
        # Test unrelated terms
        self.assertFalse(self.content_matcher._are_semantically_related('technology', 'cooking'))
    
    def test_topic_vector_creation(self):
        """Test topic vector creation"""
        topics = [
            {'topic': 'technology', 'weight': 0.8},
            {'topic': 'business', 'weight': 0.6}
        ]
        
        vector = self.content_matcher._create_topic_vector(topics)
        
        self.assertIsInstance(vector, list)
        self.assertEqual(len(vector), 10)  # Should have 10 common topics
        self.assertGreater(sum(vector), 0)  # Should have some non-zero values
    
    def test_multi_language_similarity(self):
        """Test multi-language content similarity"""
        # Create analysis with different language
        analysis_es = ContentAnalysis(
            website_id=999,
            quality_score=7.0,
            language='es',
            keywords={'primary_keywords': [{'keyword': 'tecnología', 'importance': 0.8}]},
            analyzed_at=datetime.utcnow()
        )
        
        result = self.content_matcher.get_multi_language_similarity(
            self.analysis1, analysis_es
        )
        
        self.assertIsInstance(result, dict)
        self.assertIn('language_penalty', result)
        self.assertTrue(result['language_penalty'])
        self.assertLess(result['confidence_score'], 0.5)  # Lower confidence for cross-language


class TestNicheMatching(MatchingSystemTestCase):
    """Test niche matching functionality"""
    
    def test_niche_compatibility_calculation(self):
        """Test niche compatibility calculation"""
        result = self.niche_matcher.calculate_niche_compatibility('tech', 'business')
        
        self.assertIsInstance(result, dict)
        self.assertIn('total_score', result)
        self.assertIn('base_compatibility', result)
        self.assertIn('explanation', result)
        
        # Tech and business should be compatible
        self.assertGreater(result['total_score'], 0.5)
    
    def test_category_hierarchy(self):
        """Test category hierarchy functionality"""
        tech_hierarchy = self.niche_matcher.get_category_hierarchy('tech')
        
        self.assertIsInstance(tech_hierarchy, dict)
        self.assertIn('path', tech_hierarchy)
        self.assertIn('depth', tech_hierarchy)
        self.assertEqual(tech_hierarchy['depth'], 1)  # Root category
        
        # Test sub-category
        software_hierarchy = self.niche_matcher.get_category_hierarchy('tech_software')
        self.assertEqual(software_hierarchy['depth'], 2)
        self.assertEqual(software_hierarchy['root'], 'tech')
    
    def test_find_best_niche_matches(self):
        """Test finding best niche matches"""
        matches = self.niche_matcher.find_best_niche_matches('tech', limit=5)
        
        self.assertIsInstance(matches, list)
        self.assertLessEqual(len(matches), 5)
        
        if matches:
            match = matches[0]
            self.assertIn('category_id', match)
            self.assertIn('compatibility_score', match)
            self.assertIn('category_name', match)
    
    def test_cross_niche_opportunities(self):
        """Test cross-niche opportunity identification"""
        categories = ['tech', 'business', 'health']
        opportunities = self.niche_matcher.get_cross_niche_opportunities(categories)
        
        self.assertIn('high_compatibility', opportunities)
        self.assertIn('complementary_pairs', opportunities)
        self.assertIn('expansion_suggestions', opportunities)
        self.assertIsInstance(opportunities['high_compatibility'], list)


class TestQualityAssessment(MatchingSystemTestCase):
    """Test quality assessment functionality"""

    def test_website_quality_assessment(self):
        """Test comprehensive website quality assessment"""
        quality_metrics = self.quality_assessor.assess_website_quality(self.website1, self.analysis1)

        self.assertIsInstance(quality_metrics.overall_score, float)
        self.assertGreaterEqual(quality_metrics.overall_score, 0.0)
        self.assertLessEqual(quality_metrics.overall_score, 10.0)
        self.assertIsInstance(quality_metrics.red_flags, list)
        self.assertIsInstance(quality_metrics.positive_signals, list)

    def test_spam_detection(self):
        """Test spam detection functionality"""
        # Test high-quality website
        spam_score_good = self.quality_assessor._calculate_spam_score(self.website1, self.analysis1)
        self.assertLess(spam_score_good, 3.0)

        # Test spam website
        spam_score_bad = self.quality_assessor._calculate_spam_score(self.website4, self.analysis4)
        self.assertGreater(spam_score_bad, 5.0)

    def test_quality_level_determination(self):
        """Test quality level determination"""
        excellent_level = self.quality_assessor._determine_quality_level(9.5)
        self.assertEqual(excellent_level, "EXCELLENT")

        poor_level = self.quality_assessor._determine_quality_level(4.5)
        self.assertEqual(poor_level, "POOR")

        spam_level = self.quality_assessor._determine_quality_level(1.0)
        self.assertEqual(spam_level, "SPAM")

    def test_batch_quality_assessment(self):
        """Test batch quality assessment"""
        websites = [self.website1, self.website2, self.website3]
        results = self.quality_assessor.batch_assess_quality(websites)

        self.assertEqual(len(results), 3)
        for website_id, metrics in results.items():
            self.assertIsInstance(metrics.overall_score, float)

    def test_quality_filtering(self):
        """Test quality-based filtering"""
        websites = [self.website1, self.website2, self.website3, self.website4]
        filtered = self.quality_assessor.filter_by_quality(websites, min_quality_score=6.0)

        # Should filter out the spam website
        filtered_ids = [w.id for w in filtered]
        self.assertNotIn(self.website4.id, filtered_ids)
        self.assertIn(self.website1.id, filtered_ids)


class TestFilterManagement(MatchingSystemTestCase):
    """Test filter management functionality"""

    def test_domain_filter_creation(self):
        """Test domain filter creation"""
        filter_obj = self.filter_manager.add_domain_filter(
            domain='test-domain.com',
            filter_type='blacklist',
            scope='user',
            user_id=1,
            reason='Test filter'
        )

        self.assertIsNotNone(filter_obj)
        self.assertEqual(filter_obj.domain, 'test-domain.com')
        self.assertEqual(filter_obj.filter_type, 'blacklist')

    def test_keyword_filter_creation(self):
        """Test keyword filter creation"""
        filter_obj = self.filter_manager.add_keyword_filter(
            keyword='test keyword',
            filter_type='blacklist',
            scope='user',
            user_id=1,
            reason='Test filter'
        )

        self.assertIsNotNone(filter_obj)
        self.assertEqual(filter_obj.keyword, 'test keyword')
        self.assertEqual(filter_obj.filter_type, 'blacklist')

    def test_domain_filtering_check(self):
        """Test domain filtering check"""
        # Should be filtered (blacklisted)
        is_filtered, filter_type, reason = self.filter_manager.is_domain_filtered('spam-site.tk')
        self.assertTrue(is_filtered)
        self.assertEqual(filter_type, 'blacklist')

        # Should not be filtered
        is_filtered, filter_type, reason = self.filter_manager.is_domain_filtered('good-site.com')
        self.assertFalse(is_filtered)

    def test_content_filtering_check(self):
        """Test content filtering check"""
        content_data = {
            'title': 'Buy now for limited time offer',
            'content': 'Great products available',
            'keywords': ['buy now', 'limited time']
        }

        is_filtered, filter_type, matched_keywords = self.filter_manager.is_content_filtered(content_data)
        self.assertTrue(is_filtered)
        self.assertEqual(filter_type, 'blacklist')
        self.assertIn('buy now', matched_keywords)

    def test_auto_spam_detection(self):
        """Test automatic spam detection"""
        spam_result = self.filter_manager.auto_detect_spam(self.website4, self.analysis4)

        self.assertIsInstance(spam_result, dict)
        self.assertIn('is_spam', spam_result)
        self.assertIn('confidence_score', spam_result)
        self.assertIn('spam_indicators', spam_result)

        # Should detect spam
        self.assertTrue(spam_result['is_spam'])
        self.assertGreater(spam_result['confidence_score'], 0.5)

    def test_bulk_filter_addition(self):
        """Test bulk filter addition"""
        filters_data = [
            {'type': 'domain', 'value': 'bulk1.com', 'filter_type': 'blacklist'},
            {'type': 'domain', 'value': 'bulk2.com', 'filter_type': 'blacklist'},
            {'type': 'keyword', 'value': 'bulk keyword', 'filter_type': 'blacklist'}
        ]

        results = self.filter_manager.bulk_add_filters(filters_data, user_id=1)

        self.assertEqual(results['added'], 3)
        self.assertEqual(results['skipped'], 0)
        self.assertEqual(len(results['errors']), 0)


class TestPreferencesService(MatchingSystemTestCase):
    """Test preferences service functionality"""

    def test_get_user_preferences(self):
        """Test getting user preferences"""
        preferences = self.preferences_service.get_user_preferences(1)

        self.assertIsInstance(preferences, dict)
        self.assertIn('quality_thresholds', preferences)
        self.assertIn('content_preferences', preferences)
        self.assertIn('niche_preferences', preferences)

    def test_update_user_preferences(self):
        """Test updating user preferences"""
        update_data = {
            'quality_thresholds': {
                'min_quality_score': 8.0,
                'min_domain_authority': 40
            },
            'content_preferences': {
                'min_content_similarity': 0.5
            }
        }

        success = self.preferences_service.update_user_preferences(1, update_data)
        self.assertTrue(success)

        # Verify update
        updated_prefs = self.preferences_service.get_user_preferences(1)
        self.assertEqual(updated_prefs['quality_thresholds']['min_quality_score'], 8.0)

    def test_preference_template_application(self):
        """Test applying preference templates"""
        success = self.preferences_service.apply_preference_template(1, 'conservative')
        self.assertTrue(success)

        # Verify template was applied
        preferences = self.preferences_service.get_user_preferences(1)
        self.assertEqual(preferences['quality_thresholds']['min_quality_score'], 8.0)

    def test_link_velocity_validation(self):
        """Test link velocity validation"""
        velocity_check = self.preferences_service.validate_link_velocity(1)

        self.assertIsInstance(velocity_check, dict)
        self.assertIn('current_month_links', velocity_check)
        self.assertIn('max_links_per_month', velocity_check)
        self.assertIn('velocity_ok', velocity_check)

    def test_auto_approval_decision(self):
        """Test auto-approval decision logic"""
        # High score should be auto-approved if enabled
        should_approve = self.preferences_service.should_auto_approve(1, 0.95)
        # Default preferences don't have auto-approval enabled
        self.assertFalse(should_approve)

        # Low score should be auto-rejected
        should_reject = self.preferences_service.should_auto_reject(1, 0.2)
        self.assertTrue(should_reject)  # Default has auto-reject enabled

    def test_matching_weights_retrieval(self):
        """Test getting matching weights for user"""
        weights = self.preferences_service.get_matching_weights_for_user(1)

        self.assertIsInstance(weights, dict)
        self.assertIn('content_similarity', weights)
        self.assertIn('quality_score', weights)

        # Weights should sum to approximately 1.0
        total_weight = sum(weights.values())
        self.assertAlmostEqual(total_weight, 1.0, places=1)


class TestMatchingService(MatchingSystemTestCase):
    """Test main matching service functionality"""

    def test_find_matches_basic(self):
        """Test basic match finding"""
        matches = self.matching_service.find_matches(1, limit=10)

        self.assertIsInstance(matches, list)
        self.assertLessEqual(len(matches), 10)

        if matches:
            match = matches[0]
            self.assertIn('partner_website', match)
            self.assertIn('match_score', match)
            self.assertIn('reasons', match)

    def test_find_matches_with_preferences(self):
        """Test match finding with user preferences"""
        matches = self.matching_service.find_matches_with_preferences(1, limit=5)

        self.assertIsInstance(matches, list)

        if matches:
            match = matches[0]
            self.assertIn('preference_applied', match)
            self.assertTrue(match['preference_applied'])

    def test_find_quality_filtered_matches(self):
        """Test quality-filtered match finding"""
        matches = self.matching_service.find_quality_filtered_matches(
            1, limit=5, min_quality_score=7.0
        )

        self.assertIsInstance(matches, list)

        # All matches should meet quality threshold
        for match in matches:
            quality_metrics = match.get('quality_metrics', {})
            if quality_metrics:
                self.assertGreaterEqual(quality_metrics['overall_score'], 7.0)

    def test_match_score_calculation(self):
        """Test detailed match score calculation"""
        match_score = self.matching_service._calculate_match_score(
            self.website1, self.analysis1,
            self.website2, self.analysis2
        )

        self.assertIsInstance(match_score, dict)
        self.assertIn('total_score', match_score)
        self.assertIn('content_similarity', match_score)
        self.assertIn('category_match', match_score)
        self.assertIn('quality_score', match_score)

        # Score should be between 0 and 1
        self.assertGreaterEqual(match_score['total_score'], 0.0)
        self.assertLessEqual(match_score['total_score'], 1.0)

    def test_compatibility_breakdown(self):
        """Test detailed compatibility breakdown"""
        breakdown = self.matching_service.get_compatibility_breakdown(1, 2)

        self.assertIsInstance(breakdown, dict)
        # Should contain various compatibility metrics

    def test_niche_matching_insights(self):
        """Test niche matching insights"""
        insights = self.matching_service.get_niche_matching_insights(1)

        self.assertIsInstance(insights, dict)
        if insights:  # May be empty if no category
            self.assertIn('website_category', insights)

    def test_quality_insights(self):
        """Test quality insights"""
        insights = self.matching_service.get_quality_insights(1)

        self.assertIsInstance(insights, dict)
        if insights:
            self.assertIn('website_quality_report', insights)

    def test_blacklist_filtering(self):
        """Test that blacklisted domains are filtered out"""
        # Get potential partners
        partners = self.matching_service._get_potential_partners(self.website1)

        # Spam website should be filtered out
        partner_domains = [p.domain for p in partners]
        self.assertNotIn('spam-site.tk', partner_domains)

    def test_enhanced_match_statistics(self):
        """Test enhanced match statistics"""
        stats = self.matching_service.get_enhanced_match_statistics(1)

        self.assertIsInstance(stats, dict)
        # Should contain various statistics


class TestTransparencyService(MatchingSystemTestCase):
    """Test match scoring transparency functionality"""

    def test_match_score_explanation(self):
        """Test detailed match score explanation"""
        explanation = self.transparency_service.explain_match_score(1, 2)

        self.assertIsInstance(explanation.overall_score, float)
        self.assertIsInstance(explanation.component_explanations, list)
        self.assertIsInstance(explanation.decision_factors, list)
        self.assertIsInstance(explanation.improvement_suggestions, list)
        self.assertIn(explanation.confidence_level, ['very_high', 'high', 'medium', 'low', 'very_low'])

    def test_score_debugging_info(self):
        """Test score debugging information"""
        debug_info = self.transparency_service.get_score_debugging_info(1, 2)

        self.assertIsInstance(debug_info, dict)
        self.assertIn('component_breakdown', debug_info)
        self.assertIn('weight_distribution', debug_info)
        self.assertIn('score_calculation_steps', debug_info)
        self.assertIn('potential_issues', debug_info)

    def test_match_comparison(self):
        """Test comparing multiple matches"""
        target_ids = [2, 3]
        comparison = self.transparency_service.compare_matches(1, target_ids)

        self.assertIsInstance(comparison, dict)
        self.assertIn('comparisons', comparison)
        self.assertIn('ranked_targets', comparison)
        self.assertEqual(len(comparison['comparisons']), 2)


class TestDatabaseOptimization(MatchingSystemTestCase):
    """Test database optimization functionality"""

    def test_optimized_partner_retrieval(self):
        """Test optimized potential partner retrieval"""
        partners = self.query_optimizer.get_optimized_potential_partners(self.website1)

        self.assertIsInstance(partners, list)
        # Should not include the source website
        partner_ids = [p.id for p in partners]
        self.assertNotIn(self.website1.id, partner_ids)

    def test_cached_analysis_retrieval(self):
        """Test cached analysis retrieval"""
        analysis = self.query_optimizer.get_cached_latest_analysis(1)

        self.assertIsNotNone(analysis)
        self.assertEqual(analysis.website_id, 1)

    def test_batch_analysis_retrieval(self):
        """Test batch analysis retrieval"""
        website_ids = [1, 2, 3]
        analyses = self.query_optimizer.batch_get_analyses(website_ids)

        self.assertIsInstance(analyses, dict)
        self.assertEqual(len(analyses), 3)

        for website_id in website_ids:
            self.assertIn(website_id, analyses)

    def test_performance_metrics(self):
        """Test performance metrics retrieval"""
        metrics = self.query_optimizer.get_query_performance_metrics()

        self.assertIsInstance(metrics, dict)
        self.assertIn('cache_stats', metrics)
        self.assertIn('query_stats', metrics)


class TestAPIEndpoints(MatchingSystemTestCase):
    """Test API endpoints functionality"""

    def setUp(self):
        """Set up API test environment"""
        super().setUp()
        self.client = self.app.test_client()

        # Mock JWT authentication
        self.auth_headers = {
            'Authorization': 'Bearer test_token',
            'Content-Type': 'application/json'
        }

    @patch('app.api.matching_endpoints.get_jwt_identity')
    @patch('app.api.matching_endpoints.jwt_required')
    def test_find_matches_endpoint(self, mock_jwt_required, mock_get_jwt_identity):
        """Test find matches API endpoint"""
        mock_get_jwt_identity.return_value = 1
        mock_jwt_required.return_value = True

        data = {
            'website_id': 1,
            'limit': 10,
            'use_preferences': True
        }

        response = self.client.post(
            '/api/v1/matching/find',
            data=json.dumps(data),
            headers=self.auth_headers
        )

        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.data)
        self.assertEqual(response_data['status'], 'success')
        self.assertIn('data', response_data)

    @patch('app.api.matching_endpoints.get_jwt_identity')
    @patch('app.api.matching_endpoints.jwt_required')
    def test_get_preferences_endpoint(self, mock_jwt_required, mock_get_jwt_identity):
        """Test get preferences API endpoint"""
        mock_get_jwt_identity.return_value = 1
        mock_jwt_required.return_value = True

        response = self.client.get(
            '/api/v1/matching/preferences',
            headers=self.auth_headers
        )

        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.data)
        self.assertEqual(response_data['status'], 'success')

    @patch('app.api.matching_endpoints.get_jwt_identity')
    @patch('app.api.matching_endpoints.jwt_required')
    def test_update_preferences_endpoint(self, mock_jwt_required, mock_get_jwt_identity):
        """Test update preferences API endpoint"""
        mock_get_jwt_identity.return_value = 1
        mock_jwt_required.return_value = True

        data = {
            'quality_thresholds': {
                'min_quality_score': 8.0
            }
        }

        response = self.client.put(
            '/api/v1/matching/preferences',
            data=json.dumps(data),
            headers=self.auth_headers
        )

        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.data)
        self.assertEqual(response_data['status'], 'success')

    @patch('app.api.matching_endpoints.get_jwt_identity')
    @patch('app.api.matching_endpoints.jwt_required')
    def test_add_filter_endpoint(self, mock_jwt_required, mock_get_jwt_identity):
        """Test add filter API endpoint"""
        mock_get_jwt_identity.return_value = 1
        mock_jwt_required.return_value = True

        data = {
            'domain': 'test-filter.com',
            'filter_type': 'blacklist',
            'reason': 'Test filter'
        }

        response = self.client.post(
            '/api/v1/matching/filters',
            data=json.dumps(data),
            headers=self.auth_headers
        )

        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.data)
        self.assertEqual(response_data['status'], 'success')

    def test_api_validation_errors(self):
        """Test API validation error handling"""
        # Test missing required fields
        response = self.client.post(
            '/api/v1/matching/find',
            data=json.dumps({}),
            headers=self.auth_headers
        )

        self.assertEqual(response.status_code, 400)
        response_data = json.loads(response.data)
        self.assertEqual(response_data['status'], 'error')
        self.assertIn('Validation error', response_data['message'])


class TestPerformanceAndEdgeCases(MatchingSystemTestCase):
    """Test performance and edge cases"""

    def test_large_dataset_performance(self):
        """Test performance with larger dataset"""
        import time

        # Create additional test websites
        websites = []
        for i in range(50):
            website = Website(
                id=100 + i,
                user_id=2,
                domain=f'test-site-{i}.com',
                title=f'Test Site {i}',
                category='technology',
                domain_authority=30 + (i % 40),
                status='active'
            )
            websites.append(website)

        db.session.add_all(websites)
        db.session.commit()

        # Test matching performance
        start_time = time.time()
        matches = self.matching_service.find_matches(1, limit=20)
        end_time = time.time()

        execution_time = end_time - start_time

        # Should complete within reasonable time (adjust threshold as needed)
        self.assertLess(execution_time, 5.0)  # 5 seconds max
        self.assertIsInstance(matches, list)

    def test_empty_data_handling(self):
        """Test handling of empty or missing data"""
        # Test with website that has no analysis
        website_no_analysis = Website(
            id=999,
            user_id=1,
            domain='no-analysis.com',
            title='No Analysis',
            category='test',
            status='active'
        )
        db.session.add(website_no_analysis)
        db.session.commit()

        # Should handle gracefully
        matches = self.matching_service.find_matches(999, limit=5)
        self.assertIsInstance(matches, list)
        # May be empty, but shouldn't crash

    def test_invalid_input_handling(self):
        """Test handling of invalid inputs"""
        # Test with non-existent website ID
        matches = self.matching_service.find_matches(99999, limit=5)
        self.assertEqual(matches, [])

        # Test with invalid quality score
        quality_metrics = self.quality_assessor.assess_website_quality(None, None)
        self.assertIsInstance(quality_metrics.overall_score, float)
        self.assertEqual(quality_metrics.overall_score, 0.0)

    def test_extreme_values(self):
        """Test handling of extreme values"""
        # Test with very high domain authority
        high_da_website = Website(
            id=998,
            user_id=2,
            domain='high-da.com',
            title='High DA Site',
            category='technology',
            domain_authority=95,
            status='active'
        )
        db.session.add(high_da_website)
        db.session.commit()

        # Should handle extreme values gracefully
        matches = self.matching_service.find_matches(1, limit=5)
        self.assertIsInstance(matches, list)

    def test_concurrent_access_simulation(self):
        """Test concurrent access patterns"""
        import threading
        import time

        results = []
        errors = []

        def worker():
            try:
                matches = self.matching_service.find_matches(1, limit=5)
                results.append(len(matches))
            except Exception as e:
                errors.append(str(e))

        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker)
            threads.append(thread)

        # Start all threads
        for thread in threads:
            thread.start()

        # Wait for completion
        for thread in threads:
            thread.join()

        # Check results
        self.assertEqual(len(errors), 0)  # No errors should occur
        self.assertEqual(len(results), 5)  # All threads should complete

    def test_memory_usage_patterns(self):
        """Test memory usage patterns"""
        import gc

        # Force garbage collection before test
        gc.collect()

        # Perform multiple operations
        for i in range(10):
            matches = self.matching_service.find_matches(1, limit=10)
            quality_metrics = self.quality_assessor.assess_website_quality(self.website1, self.analysis1)
            niche_compatibility = self.niche_matcher.calculate_niche_compatibility('tech', 'business')

        # Force garbage collection after test
        gc.collect()

        # Test should complete without memory issues
        self.assertTrue(True)  # If we get here, no memory errors occurred

    def test_unicode_and_special_characters(self):
        """Test handling of unicode and special characters"""
        # Create website with unicode domain
        unicode_website = Website(
            id=997,
            user_id=2,
            domain='tëst-ünïcödë.com',
            title='Test Ünicöde Site 🚀',
            category='technology',
            domain_authority=30,
            status='active'
        )

        unicode_analysis = ContentAnalysis(
            website_id=997,
            quality_score=7.0,
            language='en',
            keywords={
                'primary_keywords': [
                    {'keyword': 'tëst këywörd', 'importance': 0.8},
                    {'keyword': 'spëcïal chärs', 'importance': 0.7}
                ]
            },
            analyzed_at=datetime.utcnow()
        )

        db.session.add_all([unicode_website, unicode_analysis])
        db.session.commit()

        # Should handle unicode gracefully
        matches = self.matching_service.find_matches(1, limit=5)
        self.assertIsInstance(matches, list)

        # Test filter with unicode
        filter_obj = self.filter_manager.add_domain_filter(
            domain='tëst-ünïcödë.com',
            filter_type='blacklist',
            scope='global',
            reason='Unicode test'
        )
        self.assertIsNotNone(filter_obj)

    def test_boundary_conditions(self):
        """Test boundary conditions"""
        # Test with limit of 0
        matches = self.matching_service.find_matches(1, limit=0)
        self.assertEqual(len(matches), 0)

        # Test with very high limit
        matches = self.matching_service.find_matches(1, limit=1000)
        self.assertIsInstance(matches, list)
        # Should not crash, even if limit is higher than available matches

        # Test quality score boundaries
        quality_metrics = self.quality_assessor._determine_quality_level(0.0)
        self.assertEqual(quality_metrics, "SPAM")

        quality_metrics = self.quality_assessor._determine_quality_level(10.0)
        self.assertEqual(quality_metrics, "EXCELLENT")

    def test_data_consistency(self):
        """Test data consistency across operations"""
        # Get matches multiple times
        matches1 = self.matching_service.find_matches(1, limit=10)
        matches2 = self.matching_service.find_matches(1, limit=10)

        # Results should be consistent (same order, same scores)
        if matches1 and matches2:
            self.assertEqual(len(matches1), len(matches2))
            # First match should be the same
            if len(matches1) > 0 and len(matches2) > 0:
                match1_id = matches1[0]['partner_website'].id
                match2_id = matches2[0]['partner_website'].id
                self.assertEqual(match1_id, match2_id)


class TestIntegrationScenarios(MatchingSystemTestCase):
    """Test complete integration scenarios"""

    def test_complete_matching_workflow(self):
        """Test complete matching workflow from start to finish"""
        # 1. Set user preferences
        preferences_data = {
            'quality_thresholds': {'min_quality_score': 7.0},
            'content_preferences': {'min_content_similarity': 0.3}
        }
        success = self.preferences_service.update_user_preferences(1, preferences_data)
        self.assertTrue(success)

        # 2. Add some filters
        self.filter_manager.add_domain_filter(
            domain='unwanted-site.com',
            filter_type='blacklist',
            scope='user',
            user_id=1
        )

        # 3. Find matches with preferences
        matches = self.matching_service.find_matches_with_preferences(1, limit=5)
        self.assertIsInstance(matches, list)

        # 4. Get detailed explanation for top match
        if matches:
            top_match = matches[0]
            partner_id = top_match['partner_website'].id

            explanation = self.transparency_service.explain_match_score(1, partner_id)
            self.assertIsInstance(explanation.overall_score, float)
            self.assertIsInstance(explanation.component_explanations, list)

        # 5. Get insights
        insights = self.matching_service.get_niche_matching_insights(1)
        self.assertIsInstance(insights, dict)

    def test_user_journey_simulation(self):
        """Simulate a complete user journey"""
        # New user starts with default preferences
        new_user = User(id=99, email='<EMAIL>', username='newuser', status='active')
        new_website = Website(
            id=99,
            user_id=99,
            domain='new-user-site.com',
            title='New User Site',
            category='technology',
            domain_authority=25,
            status='active'
        )
        new_analysis = ContentAnalysis(
            website_id=99,
            quality_score=6.5,
            language='en',
            keywords={'primary_keywords': [{'keyword': 'new content', 'importance': 0.8}]},
            analyzed_at=datetime.utcnow()
        )

        db.session.add_all([new_user, new_website, new_analysis])
        db.session.commit()

        # 1. Get initial matches
        initial_matches = self.matching_service.find_matches(99, limit=10)
        initial_count = len(initial_matches)

        # 2. Apply conservative template
        self.preferences_service.apply_preference_template(99, 'conservative')

        # 3. Get matches with new preferences
        conservative_matches = self.matching_service.find_matches_with_preferences(99, limit=10)
        conservative_count = len(conservative_matches)

        # Conservative should have fewer or equal matches due to higher standards
        self.assertLessEqual(conservative_count, initial_count)

        # 4. Add some filters
        self.filter_manager.add_keyword_filter(
            keyword='spam',
            filter_type='blacklist',
            scope='user',
            user_id=99
        )

        # 5. Final matches should respect all filters and preferences
        final_matches = self.matching_service.find_matches_with_preferences(99, limit=10)
        self.assertIsInstance(final_matches, list)


if __name__ == '__main__':
    # Run all tests
    unittest.main(verbosity=2)
