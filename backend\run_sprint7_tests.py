#!/usr/bin/env python3
"""
Sprint 7 Test Runner for LinkUp Backend
Runs all Sprint 7 unit tests with detailed reporting and coverage analysis
"""
import unittest
import sys
import os
import time
from datetime import datetime
from io import StringIO

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

# Try to import coverage for code coverage analysis
try:
    import coverage
    HAS_COVERAGE = True
except ImportError:
    HAS_COVERAGE = False
    print("Warning: coverage.py not installed. Install with: pip install coverage")

def run_sprint7_tests():
    """Run all Sprint 7 tests"""
    print("="*80)
    print("🚀 LINKUP SPRINT 7 - KEYWORD GAP ANALYSIS TEST SUITE")
    print("="*80)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Start coverage if available
    cov = None
    if HAS_COVERAGE:
        cov = coverage.Coverage(source=['app.services'])
        cov.start()
        print("✓ Code coverage tracking enabled")
    
    # Test modules for Sprint 7
    test_modules = [
        'tests.test_sprint7_comprehensive',
        'tests.test_sprint7_additional'
    ]
    
    # Load test suites
    test_loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()
    
    print("📋 LOADING SPRINT 7 TEST MODULES...")
    for module_name in test_modules:
        try:
            module_tests = test_loader.loadTestsFromName(module_name)
            test_suite.addTests(module_tests)
            print(f"✅ Loaded: {module_name}")
        except Exception as e:
            print(f"❌ Failed to load: {module_name} - {str(e)}")
    
    print()
    
    # Count total tests
    total_tests = test_suite.countTestCases()
    print(f"✓ Discovered {total_tests} test cases")
    
    # Run tests
    print("\n🧪 RUNNING SPRINT 7 TESTS...")
    print("-" * 80)
    
    start_time = time.time()
    
    # Create test runner with detailed output
    runner = unittest.TextTestRunner(
        verbosity=2,
        buffer=True,
        stream=sys.stdout
    )
    
    result = runner.run(test_suite)
    
    end_time = time.time()
    execution_time = end_time - start_time
    
    # Stop coverage
    if cov:
        cov.stop()
        cov.save()
    
    # Print results
    print_sprint7_results(result, execution_time)
    
    # Generate coverage report
    if cov:
        generate_coverage_report(cov)
    
    # Component status
    print_component_status(result)
    
    return result

def print_sprint7_results(result, execution_time):
    """Print formatted Sprint 7 test results"""
    print("\n" + "="*80)
    print("📊 SPRINT 7 TEST EXECUTION REPORT")
    print("="*80)
    
    # Basic stats
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    successful = total_tests - failures - errors - skipped
    
    success_rate = (successful / total_tests * 100) if total_tests > 0 else 0
    
    print(f"Total Tests:     {total_tests}")
    print(f"✅ Passed:       {successful}")
    print(f"❌ Failed:       {failures}")
    print(f"🚫 Errors:       {errors}")
    print(f"⏭️  Skipped:      {skipped}")
    print(f"⏱️  Execution:    {execution_time:.2f} seconds")
    print(f"🎯 Success Rate: {success_rate:.1f}%")
    print()
    
    # Status indicator
    if failures == 0 and errors == 0:
        print("🎉 ALL SPRINT 7 TESTS PASSED! 🎉")
        status = "PASS"
    else:
        print("❌ SOME SPRINT 7 TESTS FAILED ❌")
        status = "FAIL"
    
    # Detailed failure/error reporting
    if failures > 0:
        print(f"\n{'='*80}")
        print("❌ FAILURES")
        print("="*80)
        for i, (test, traceback) in enumerate(result.failures, 1):
            print(f"\n{i}. {test}")
            print("-" * 60)
            print(traceback)
    
    if errors > 0:
        print(f"\n{'='*80}")
        print("🚫 ERRORS")
        print("="*80)
        for i, (test, traceback) in enumerate(result.errors, 1):
            print(f"\n{i}. {test}")
            print("-" * 60)
            print(traceback)
    
    return status

def generate_coverage_report(cov):
    """Generate and display coverage report"""
    print(f"\n{'='*80}")
    print("📈 CODE COVERAGE REPORT")
    print("="*80)
    
    try:
        # Generate coverage report to console
        print("\nCoverage Summary:")
        cov.report(show_missing=True)
        
        # Generate HTML report
        try:
            cov.html_report(directory='htmlcov_sprint7')
            print(f"\n✓ HTML coverage report generated in 'htmlcov_sprint7/' directory")
            print("  Open htmlcov_sprint7/index.html in your browser to view detailed coverage")
        except Exception as e:
            print(f"\n⚠ Could not generate HTML report: {e}")
        
    except Exception as e:
        print(f"Error generating coverage report: {e}")

def print_component_status(result):
    """Print Sprint 7 component status"""
    print(f"\n{'='*80}")
    print("🔧 SPRINT 7 COMPONENT STATUS")
    print("="*80)
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0
    
    components = [
        "Competitor Content Analysis Engine",
        "Keyword Gap Identification Algorithms",
        "Content Opportunity Scoring System", 
        "Suggestion Generation System",
        "Keyword Research API Integration",
        "Content Optimization Recommendations",
        "Trending Topics Identification",
        "WordPress Admin Interface"
    ]
    
    for component in components:
        status = "✅ OPERATIONAL" if success_rate >= 80 else "⚠️  NEEDS ATTENTION"
        print(f"{component}: {status}")
    
    print()
    
    # Feature readiness checklist
    print("📋 SPRINT 7 FEATURE READINESS:")
    print("-" * 60)
    
    features = [
        ("Advanced Competitor Analysis", success_rate >= 80),
        ("Semantic Keyword Gap Detection", success_rate >= 80),
        ("Multi-factor Opportunity Scoring", success_rate >= 80),
        ("Actionable Content Suggestions", success_rate >= 80),
        ("Multi-provider API Integration", success_rate >= 80),
        ("Content Optimization Analysis", success_rate >= 80),
        ("Real-time Trend Identification", success_rate >= 80),
        ("User-friendly Admin Interface", success_rate >= 80)
    ]
    
    for feature, ready in features:
        status_icon = "✅" if ready else "❌"
        print(f"{status_icon} {feature}")
    
    print()
    
    # Overall Sprint 7 status
    print("🏆 SPRINT 7 OVERALL STATUS:")
    print("-" * 60)
    
    if success_rate >= 95:
        status = "🟢 EXCELLENT - Ready for Production"
        recommendation = "Sprint 7 features are fully operational and ready for deployment."
    elif success_rate >= 85:
        status = "🟡 GOOD - Minor Issues"
        recommendation = "Sprint 7 features are mostly operational with minor issues to address."
    elif success_rate >= 70:
        status = "🟠 FAIR - Needs Improvement"
        recommendation = "Sprint 7 features have significant issues that should be addressed."
    else:
        status = "🔴 POOR - Major Issues"
        recommendation = "Sprint 7 features have critical issues that must be resolved."
    
    print(f"Status: {status}")
    print(f"Recommendation: {recommendation}")
    print()

def validate_sprint7_components():
    """Validate that all Sprint 7 components are properly installed"""
    print("🔍 VALIDATING SPRINT 7 COMPONENTS...")
    print("-" * 60)
    
    required_modules = [
        'app.services.competitor_analysis_service',
        'app.services.keyword_gap_service',
        'app.services.content_opportunity_service',
        'app.services.suggestion_generation_service',
        'app.services.keyword_research_api_service',
        'app.services.content_optimization_service',
        'app.services.trending_topics_service'
    ]
    
    all_valid = True
    
    for module_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
        except ImportError as e:
            print(f"❌ {module_name} - {str(e)}")
            all_valid = False
    
    print()
    
    if all_valid:
        print("✅ All Sprint 7 components are properly installed!")
    else:
        print("❌ Some Sprint 7 components are missing. Please check installation.")
        return False
    
    return True

def main():
    """Main test runner function"""
    print("🔧 LinkUp Sprint 7 - Keyword Gap Analysis Test Runner")
    print()
    
    # Validate components first
    if not validate_sprint7_components():
        print("❌ Component validation failed. Exiting.")
        sys.exit(1)
    
    # Run Sprint 7 tests
    result = run_sprint7_tests()
    
    # Determine exit code
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    print()
    print("="*80)
    print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    if success:
        print("🎉 ALL SPRINT 7 TESTS COMPLETED SUCCESSFULLY!")
        print("🚀 Sprint 7 Keyword Gap Analysis features are ready!")
    else:
        print("⚠️  Some Sprint 7 tests failed. Please review and fix issues.")
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
