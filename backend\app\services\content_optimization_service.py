"""
Content Optimization Recommendations Service
Provides specific recommendations for optimizing existing content
"""
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
import math

# Handle optional dependencies
try:
    from textstat import flesch_reading_ease, flesch_kincaid_grade
    HAS_TEXTSTAT = True
except ImportError:
    HAS_TEXTSTAT = False
    def flesch_reading_ease(text): return 60.0
    def flesch_kincaid_grade(text): return 8.0

from app import db, cache
from app.models.website import Website
from app.models.analysis import ContentAnalysis
from app.services.keyword_research_api_service import KeywordResearchAPIService

logger = logging.getLogger(__name__)


class OptimizationType(Enum):
    """Types of content optimizations"""
    SEO_OPTIMIZATION = "seo_optimization"
    READABILITY_IMPROVEMENT = "readability_improvement"
    KEYWORD_OPTIMIZATION = "keyword_optimization"
    STRUCTURE_IMPROVEMENT = "structure_improvement"
    CONTENT_EXPANSION = "content_expansion"
    TECHNICAL_SEO = "technical_seo"
    USER_EXPERIENCE = "user_experience"
    CONVERSION_OPTIMIZATION = "conversion_optimization"


class Priority(Enum):
    """Priority levels for optimizations"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class OptimizationRecommendation:
    """Represents a content optimization recommendation"""
    recommendation_id: str
    optimization_type: OptimizationType
    priority: Priority
    title: str
    description: str
    current_issue: str
    recommended_action: str
    expected_impact: str
    implementation_difficulty: str
    estimated_effort_hours: int
    success_metrics: List[str]
    before_example: Optional[str]
    after_example: Optional[str]
    tools_needed: List[str]
    related_recommendations: List[str]
    created_at: datetime


@dataclass
class ContentAnalysisResult:
    """Results of content analysis"""
    content_url: str
    content_title: str
    content_length: int
    keyword_density: Dict[str, float]
    readability_score: float
    seo_score: float
    structure_score: float
    recommendations: List[OptimizationRecommendation]
    overall_score: float
    improvement_potential: float


class ContentOptimizationService:
    """Service for analyzing content and providing optimization recommendations"""
    
    def __init__(self):
        """Initialize the content optimization service"""
        self.keyword_research_service = KeywordResearchAPIService()
        self.cache_timeout = 3600 * 12  # 12 hours
        
        # Optimization thresholds
        self.thresholds = {
            'min_content_length': 300,
            'optimal_content_length': 1500,
            'max_keyword_density': 3.0,
            'min_keyword_density': 0.5,
            'min_readability_score': 60.0,
            'max_sentence_length': 20,
            'min_paragraph_count': 3,
            'max_paragraph_length': 150
        }
        
        # SEO best practices
        self.seo_best_practices = {
            'title_length': {'min': 30, 'max': 60},
            'meta_description_length': {'min': 120, 'max': 160},
            'h1_count': {'min': 1, 'max': 1},
            'h2_count': {'min': 2, 'max': 10},
            'internal_links': {'min': 2, 'max': 10},
            'external_links': {'min': 1, 'max': 5},
            'image_alt_text_coverage': {'min': 80}  # percentage
        }
    
    def analyze_content(self, content_url: str, content_text: str, target_keywords: List[str] = None) -> ContentAnalysisResult:
        """Analyze content and provide optimization recommendations"""
        try:
            # Check cache first
            cache_key = f"content_analysis_{hash(content_url)}_{hash(content_text)}"
            cached_result = cache.get(cache_key)
            if cached_result:
                return ContentAnalysisResult(**cached_result)
            
            # Extract content metadata
            content_metadata = self._extract_content_metadata(content_text)
            
            # Analyze content structure
            structure_analysis = self._analyze_content_structure(content_text)
            
            # Analyze SEO factors
            seo_analysis = self._analyze_seo_factors(content_text, content_metadata, target_keywords)
            
            # Analyze readability
            readability_analysis = self._analyze_readability(content_text)
            
            # Analyze keyword optimization
            keyword_analysis = self._analyze_keyword_optimization(content_text, target_keywords)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                content_text, content_metadata, structure_analysis, 
                seo_analysis, readability_analysis, keyword_analysis
            )
            
            # Calculate scores
            seo_score = self._calculate_seo_score(seo_analysis)
            structure_score = self._calculate_structure_score(structure_analysis)
            readability_score = readability_analysis.get('flesch_score', 60.0)
            
            overall_score = (seo_score * 0.4 + structure_score * 0.3 + readability_score * 0.3)
            improvement_potential = 100 - overall_score
            
            result = ContentAnalysisResult(
                content_url=content_url,
                content_title=content_metadata.get('title', 'Untitled'),
                content_length=len(content_text),
                keyword_density=keyword_analysis.get('keyword_density', {}),
                readability_score=readability_score,
                seo_score=seo_score,
                structure_score=structure_score,
                recommendations=recommendations,
                overall_score=round(overall_score, 2),
                improvement_potential=round(improvement_potential, 2)
            )
            
            # Cache the result
            cache.set(cache_key, result.__dict__, timeout=self.cache_timeout)
            
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing content: {str(e)}")
            return ContentAnalysisResult(
                content_url=content_url,
                content_title="Error",
                content_length=0,
                keyword_density={},
                readability_score=0.0,
                seo_score=0.0,
                structure_score=0.0,
                recommendations=[],
                overall_score=0.0,
                improvement_potential=0.0
            )
    
    def _extract_content_metadata(self, content_text: str) -> Dict[str, Any]:
        """Extract metadata from content"""
        metadata = {}
        
        # Extract title (assume first line or H1)
        lines = content_text.split('\n')
        for line in lines:
            line = line.strip()
            if line and (line.startswith('#') or len(line) > 10):
                metadata['title'] = line.replace('#', '').strip()
                break
        
        # Extract headings
        h1_pattern = r'^#\s+(.+)$'
        h2_pattern = r'^##\s+(.+)$'
        h3_pattern = r'^###\s+(.+)$'
        
        metadata['h1_tags'] = re.findall(h1_pattern, content_text, re.MULTILINE)
        metadata['h2_tags'] = re.findall(h2_pattern, content_text, re.MULTILINE)
        metadata['h3_tags'] = re.findall(h3_pattern, content_text, re.MULTILINE)
        
        # Extract links
        link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
        links = re.findall(link_pattern, content_text)
        
        internal_links = []
        external_links = []
        
        for link_text, link_url in links:
            if link_url.startswith('http'):
                external_links.append({'text': link_text, 'url': link_url})
            else:
                internal_links.append({'text': link_text, 'url': link_url})
        
        metadata['internal_links'] = internal_links
        metadata['external_links'] = external_links
        
        # Extract images
        image_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        images = re.findall(image_pattern, content_text)
        metadata['images'] = [{'alt': alt, 'src': src} for alt, src in images]
        
        return metadata
    
    def _analyze_content_structure(self, content_text: str) -> Dict[str, Any]:
        """Analyze content structure"""
        structure = {}
        
        # Paragraph analysis
        paragraphs = [p.strip() for p in content_text.split('\n\n') if p.strip()]
        structure['paragraph_count'] = len(paragraphs)
        structure['avg_paragraph_length'] = sum(len(p) for p in paragraphs) / max(len(paragraphs), 1)
        structure['long_paragraphs'] = len([p for p in paragraphs if len(p) > self.thresholds['max_paragraph_length']])
        
        # Sentence analysis
        sentences = re.split(r'[.!?]+', content_text)
        sentences = [s.strip() for s in sentences if s.strip()]
        structure['sentence_count'] = len(sentences)
        structure['avg_sentence_length'] = sum(len(s.split()) for s in sentences) / max(len(sentences), 1)
        structure['long_sentences'] = len([s for s in sentences if len(s.split()) > self.thresholds['max_sentence_length']])
        
        # Word analysis
        words = content_text.split()
        structure['word_count'] = len(words)
        structure['avg_word_length'] = sum(len(w) for w in words) / max(len(words), 1)
        
        # List analysis
        structure['bullet_lists'] = len(re.findall(r'^\s*[-*+]\s+', content_text, re.MULTILINE))
        structure['numbered_lists'] = len(re.findall(r'^\s*\d+\.\s+', content_text, re.MULTILINE))
        
        return structure
    
    def _analyze_seo_factors(self, content_text: str, metadata: Dict, target_keywords: List[str] = None) -> Dict[str, Any]:
        """Analyze SEO factors"""
        seo_analysis = {}
        
        # Title analysis
        title = metadata.get('title', '')
        seo_analysis['title_length'] = len(title)
        seo_analysis['title_has_keyword'] = False
        
        if target_keywords:
            for keyword in target_keywords:
                if keyword.lower() in title.lower():
                    seo_analysis['title_has_keyword'] = True
                    break
        
        # Heading analysis
        seo_analysis['h1_count'] = len(metadata.get('h1_tags', []))
        seo_analysis['h2_count'] = len(metadata.get('h2_tags', []))
        seo_analysis['h3_count'] = len(metadata.get('h3_tags', []))
        
        # Link analysis
        seo_analysis['internal_link_count'] = len(metadata.get('internal_links', []))
        seo_analysis['external_link_count'] = len(metadata.get('external_links', []))
        
        # Image analysis
        images = metadata.get('images', [])
        seo_analysis['image_count'] = len(images)
        seo_analysis['images_with_alt'] = len([img for img in images if img['alt']])
        seo_analysis['alt_text_coverage'] = (seo_analysis['images_with_alt'] / max(seo_analysis['image_count'], 1)) * 100
        
        # Content length analysis
        seo_analysis['content_length'] = len(content_text)
        seo_analysis['meets_min_length'] = seo_analysis['content_length'] >= self.thresholds['min_content_length']
        
        return seo_analysis
    
    def _analyze_readability(self, content_text: str) -> Dict[str, Any]:
        """Analyze content readability"""
        readability = {}
        
        if HAS_TEXTSTAT:
            readability['flesch_score'] = flesch_reading_ease(content_text)
            readability['grade_level'] = flesch_kincaid_grade(content_text)
        else:
            # Simplified readability calculation
            sentences = re.split(r'[.!?]+', content_text)
            words = content_text.split()
            
            avg_sentence_length = len(words) / max(len(sentences), 1)
            
            # Simplified Flesch score approximation
            readability['flesch_score'] = max(0, 206.835 - (1.015 * avg_sentence_length))
            readability['grade_level'] = min(avg_sentence_length / 5, 12)
        
        # Readability classification
        flesch_score = readability['flesch_score']
        if flesch_score >= 90:
            readability['level'] = 'very_easy'
        elif flesch_score >= 80:
            readability['level'] = 'easy'
        elif flesch_score >= 70:
            readability['level'] = 'fairly_easy'
        elif flesch_score >= 60:
            readability['level'] = 'standard'
        elif flesch_score >= 50:
            readability['level'] = 'fairly_difficult'
        elif flesch_score >= 30:
            readability['level'] = 'difficult'
        else:
            readability['level'] = 'very_difficult'
        
        return readability
    
    def _analyze_keyword_optimization(self, content_text: str, target_keywords: List[str] = None) -> Dict[str, Any]:
        """Analyze keyword optimization"""
        keyword_analysis = {}
        
        if not target_keywords:
            return keyword_analysis
        
        content_lower = content_text.lower()
        total_words = len(content_text.split())
        
        keyword_density = {}
        keyword_positions = {}
        
        for keyword in target_keywords:
            keyword_lower = keyword.lower()
            
            # Count keyword occurrences
            count = content_lower.count(keyword_lower)
            density = (count / max(total_words, 1)) * 100
            
            keyword_density[keyword] = round(density, 2)
            
            # Find keyword positions
            positions = []
            start = 0
            while True:
                pos = content_lower.find(keyword_lower, start)
                if pos == -1:
                    break
                positions.append(pos)
                start = pos + 1
            
            keyword_positions[keyword] = positions
        
        keyword_analysis['keyword_density'] = keyword_density
        keyword_analysis['keyword_positions'] = keyword_positions
        
        # Check keyword placement
        first_100_words = ' '.join(content_text.split()[:100]).lower()
        keyword_analysis['keyword_in_first_100'] = any(
            kw.lower() in first_100_words for kw in target_keywords
        )
        
        return keyword_analysis

    def _generate_recommendations(self, content_text: str, metadata: Dict, structure_analysis: Dict,
                                 seo_analysis: Dict, readability_analysis: Dict, keyword_analysis: Dict) -> List[OptimizationRecommendation]:
        """Generate optimization recommendations based on analysis"""
        recommendations = []

        # SEO Recommendations
        recommendations.extend(self._generate_seo_recommendations(content_text, metadata, seo_analysis, keyword_analysis))

        # Structure Recommendations
        recommendations.extend(self._generate_structure_recommendations(structure_analysis))

        # Readability Recommendations
        recommendations.extend(self._generate_readability_recommendations(readability_analysis, structure_analysis))

        # Keyword Optimization Recommendations
        recommendations.extend(self._generate_keyword_recommendations(keyword_analysis))

        # Technical SEO Recommendations
        recommendations.extend(self._generate_technical_seo_recommendations(metadata, seo_analysis))

        # Sort by priority
        priority_order = {Priority.CRITICAL: 0, Priority.HIGH: 1, Priority.MEDIUM: 2, Priority.LOW: 3}
        recommendations.sort(key=lambda x: priority_order[x.priority])

        return recommendations

    def _generate_seo_recommendations(self, content_text: str, metadata: Dict, seo_analysis: Dict, keyword_analysis: Dict) -> List[OptimizationRecommendation]:
        """Generate SEO-specific recommendations"""
        recommendations = []

        # Title optimization
        title_length = seo_analysis.get('title_length', 0)
        if title_length < self.seo_best_practices['title_length']['min']:
            recommendations.append(OptimizationRecommendation(
                recommendation_id=f"seo_title_short_{int(datetime.now().timestamp())}",
                optimization_type=OptimizationType.SEO_OPTIMIZATION,
                priority=Priority.HIGH,
                title="Title too short",
                description="Your title is shorter than the recommended minimum length",
                current_issue=f"Current title length: {title_length} characters",
                recommended_action=f"Expand title to at least {self.seo_best_practices['title_length']['min']} characters",
                expected_impact="Improved click-through rates and better keyword targeting",
                implementation_difficulty="Easy",
                estimated_effort_hours=1,
                success_metrics=["Increased CTR", "Better keyword rankings"],
                before_example=metadata.get('title', ''),
                after_example=f"{metadata.get('title', '')} - Complete Guide",
                tools_needed=["Content editor"],
                related_recommendations=[],
                created_at=datetime.now()
            ))

        # H1 tag optimization
        h1_count = seo_analysis.get('h1_count', 0)
        if h1_count == 0:
            recommendations.append(OptimizationRecommendation(
                recommendation_id=f"seo_h1_missing_{int(datetime.now().timestamp())}",
                optimization_type=OptimizationType.SEO_OPTIMIZATION,
                priority=Priority.CRITICAL,
                title="Missing H1 tag",
                description="Your content lacks an H1 heading tag",
                current_issue="No H1 tag found",
                recommended_action="Add a descriptive H1 tag with your primary keyword",
                expected_impact="Improved content structure and keyword targeting",
                implementation_difficulty="Easy",
                estimated_effort_hours=1,
                success_metrics=["Better content hierarchy", "Improved keyword rankings"],
                before_example="Content without H1",
                after_example="# Your Primary Keyword - Main Topic",
                tools_needed=["Content editor"],
                related_recommendations=[],
                created_at=datetime.now()
            ))

        # Content length optimization
        content_length = seo_analysis.get('content_length', 0)
        if content_length < self.thresholds['min_content_length']:
            recommendations.append(OptimizationRecommendation(
                recommendation_id=f"seo_content_short_{int(datetime.now().timestamp())}",
                optimization_type=OptimizationType.CONTENT_EXPANSION,
                priority=Priority.HIGH,
                title="Content too short",
                description="Your content is shorter than recommended for good SEO",
                current_issue=f"Current length: {content_length} characters",
                recommended_action=f"Expand content to at least {self.thresholds['min_content_length']} characters",
                expected_impact="Better search engine rankings and user engagement",
                implementation_difficulty="Medium",
                estimated_effort_hours=3,
                success_metrics=["Improved rankings", "Increased time on page"],
                before_example="Short content",
                after_example="Expanded, comprehensive content",
                tools_needed=["Content writer", "Research tools"],
                related_recommendations=[],
                created_at=datetime.now()
            ))

        return recommendations

    def _generate_structure_recommendations(self, structure_analysis: Dict) -> List[OptimizationRecommendation]:
        """Generate content structure recommendations"""
        recommendations = []

        # Paragraph length
        long_paragraphs = structure_analysis.get('long_paragraphs', 0)
        if long_paragraphs > 0:
            recommendations.append(OptimizationRecommendation(
                recommendation_id=f"structure_paragraphs_{int(datetime.now().timestamp())}",
                optimization_type=OptimizationType.STRUCTURE_IMPROVEMENT,
                priority=Priority.MEDIUM,
                title="Long paragraphs detected",
                description="Some paragraphs are too long for optimal readability",
                current_issue=f"{long_paragraphs} paragraphs exceed recommended length",
                recommended_action=f"Break long paragraphs into shorter ones (max {self.thresholds['max_paragraph_length']} characters)",
                expected_impact="Improved readability and user engagement",
                implementation_difficulty="Easy",
                estimated_effort_hours=1,
                success_metrics=["Better readability score", "Increased time on page"],
                before_example="Very long paragraph with multiple ideas...",
                after_example="Shorter paragraph.\n\nAnother shorter paragraph.",
                tools_needed=["Content editor"],
                related_recommendations=[],
                created_at=datetime.now()
            ))

        return recommendations

    def _generate_readability_recommendations(self, readability_analysis: Dict, structure_analysis: Dict) -> List[OptimizationRecommendation]:
        """Generate readability recommendations"""
        recommendations = []

        flesch_score = readability_analysis.get('flesch_score', 60)
        if flesch_score < self.thresholds['min_readability_score']:
            recommendations.append(OptimizationRecommendation(
                recommendation_id=f"readability_low_{int(datetime.now().timestamp())}",
                optimization_type=OptimizationType.READABILITY_IMPROVEMENT,
                priority=Priority.MEDIUM,
                title="Low readability score",
                description="Your content has a low readability score",
                current_issue=f"Current Flesch score: {flesch_score:.1f}",
                recommended_action="Simplify sentences, use shorter words, and improve structure",
                expected_impact="Better user engagement and comprehension",
                implementation_difficulty="Medium",
                estimated_effort_hours=2,
                success_metrics=["Improved readability score", "Increased time on page"],
                before_example="Complex sentence with difficult vocabulary",
                after_example="Simple sentence with easy words",
                tools_needed=["Content editor", "Readability checker"],
                related_recommendations=[],
                created_at=datetime.now()
            ))

        return recommendations

    def _generate_keyword_recommendations(self, keyword_analysis: Dict) -> List[OptimizationRecommendation]:
        """Generate keyword optimization recommendations"""
        recommendations = []

        keyword_density = keyword_analysis.get('keyword_density', {})

        for keyword, density in keyword_density.items():
            if density < self.thresholds['min_keyword_density']:
                recommendations.append(OptimizationRecommendation(
                    recommendation_id=f"keyword_low_{keyword}_{int(datetime.now().timestamp())}",
                    optimization_type=OptimizationType.KEYWORD_OPTIMIZATION,
                    priority=Priority.MEDIUM,
                    title=f"Low keyword density for '{keyword}'",
                    description=f"The keyword '{keyword}' appears infrequently in your content",
                    current_issue=f"Current density: {density}%",
                    recommended_action=f"Increase keyword usage to {self.thresholds['min_keyword_density']}-{self.thresholds['max_keyword_density']}%",
                    expected_impact="Better keyword targeting and rankings",
                    implementation_difficulty="Easy",
                    estimated_effort_hours=1,
                    success_metrics=["Improved keyword rankings"],
                    before_example="Content with low keyword usage",
                    after_example="Content with optimized keyword usage",
                    tools_needed=["Content editor"],
                    related_recommendations=[],
                    created_at=datetime.now()
                ))
            elif density > self.thresholds['max_keyword_density']:
                recommendations.append(OptimizationRecommendation(
                    recommendation_id=f"keyword_high_{keyword}_{int(datetime.now().timestamp())}",
                    optimization_type=OptimizationType.KEYWORD_OPTIMIZATION,
                    priority=Priority.HIGH,
                    title=f"Keyword stuffing detected for '{keyword}'",
                    description=f"The keyword '{keyword}' appears too frequently",
                    current_issue=f"Current density: {density}%",
                    recommended_action=f"Reduce keyword usage to under {self.thresholds['max_keyword_density']}%",
                    expected_impact="Avoid keyword stuffing penalties",
                    implementation_difficulty="Easy",
                    estimated_effort_hours=1,
                    success_metrics=["Avoid SEO penalties", "Natural keyword usage"],
                    before_example="Content with keyword stuffing",
                    after_example="Content with natural keyword usage",
                    tools_needed=["Content editor"],
                    related_recommendations=[],
                    created_at=datetime.now()
                ))

        return recommendations

    def _generate_technical_seo_recommendations(self, metadata: Dict, seo_analysis: Dict) -> List[OptimizationRecommendation]:
        """Generate technical SEO recommendations"""
        recommendations = []

        # Image alt text
        alt_text_coverage = seo_analysis.get('alt_text_coverage', 100)
        if alt_text_coverage < self.seo_best_practices['image_alt_text_coverage']['min']:
            recommendations.append(OptimizationRecommendation(
                recommendation_id=f"technical_alt_text_{int(datetime.now().timestamp())}",
                optimization_type=OptimizationType.TECHNICAL_SEO,
                priority=Priority.MEDIUM,
                title="Missing image alt text",
                description="Some images lack alt text descriptions",
                current_issue=f"Alt text coverage: {alt_text_coverage:.1f}%",
                recommended_action="Add descriptive alt text to all images",
                expected_impact="Better accessibility and image SEO",
                implementation_difficulty="Easy",
                estimated_effort_hours=1,
                success_metrics=["Improved accessibility", "Better image rankings"],
                before_example="<img src='image.jpg'>",
                after_example="<img src='image.jpg' alt='Descriptive alt text'>",
                tools_needed=["Content editor"],
                related_recommendations=[],
                created_at=datetime.now()
            ))

        return recommendations

    def _calculate_seo_score(self, seo_analysis: Dict) -> float:
        """Calculate overall SEO score"""
        score = 0
        max_score = 0

        # Title length (0-20 points)
        title_length = seo_analysis.get('title_length', 0)
        if self.seo_best_practices['title_length']['min'] <= title_length <= self.seo_best_practices['title_length']['max']:
            score += 20
        max_score += 20

        # H1 tag (0-15 points)
        if seo_analysis.get('h1_count', 0) == 1:
            score += 15
        max_score += 15

        # Content length (0-15 points)
        if seo_analysis.get('meets_min_length', False):
            score += 15
        max_score += 15

        # Internal links (0-10 points)
        internal_links = seo_analysis.get('internal_link_count', 0)
        if internal_links >= self.seo_best_practices['internal_links']['min']:
            score += 10
        max_score += 10

        # Alt text coverage (0-10 points)
        alt_coverage = seo_analysis.get('alt_text_coverage', 0)
        if alt_coverage >= self.seo_best_practices['image_alt_text_coverage']['min']:
            score += 10
        max_score += 10

        return (score / max_score) * 100 if max_score > 0 else 0

    def _calculate_structure_score(self, structure_analysis: Dict) -> float:
        """Calculate content structure score"""
        score = 0
        max_score = 0

        # Paragraph count (0-20 points)
        paragraph_count = structure_analysis.get('paragraph_count', 0)
        if paragraph_count >= self.thresholds['min_paragraph_count']:
            score += 20
        max_score += 20

        # Long paragraphs penalty (0-20 points)
        long_paragraphs = structure_analysis.get('long_paragraphs', 0)
        total_paragraphs = structure_analysis.get('paragraph_count', 1)
        if long_paragraphs / total_paragraphs < 0.3:  # Less than 30% long paragraphs
            score += 20
        max_score += 20

        # Sentence length (0-15 points)
        avg_sentence_length = structure_analysis.get('avg_sentence_length', 0)
        if avg_sentence_length <= self.thresholds['max_sentence_length']:
            score += 15
        max_score += 15

        # List usage (0-10 points)
        has_lists = (structure_analysis.get('bullet_lists', 0) + structure_analysis.get('numbered_lists', 0)) > 0
        if has_lists:
            score += 10
        max_score += 10

        return (score / max_score) * 100 if max_score > 0 else 0
