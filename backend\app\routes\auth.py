"""
Authentication routes for LinkUp Plugin
"""
from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, create_refresh_token, jwt_required, get_jwt_identity
from app.services.auth_service import AuthenticationService
from app.models.user import User
from app.utils.decorators import validate_json, handle_errors, log_api_usage
from app.utils.validators import validate_email, validate_password, validate_url
from app import db
import logging

logger = logging.getLogger(__name__)

bp = Blueprint('auth', __name__)


@bp.route('/register', methods=['POST'])
@validate_json('email', 'password')
@handle_errors
@log_api_usage
def register():
    """Register a new user"""
    data = request.get_json()
    
    # Validate input
    if not validate_email(data['email']):
        return jsonify({
            'success': False,
            'error': 'Invalid email address'
        }), 400
    
    password_validation = validate_password(data['password'])
    if not password_validation['valid']:
        return jsonify({
            'success': False,
            'error': 'Password validation failed',
            'details': password_validation['errors']
        }), 400
    
    # Optional website URL validation
    wp_site_url = data.get('wp_site_url')
    if wp_site_url and not validate_url(wp_site_url):
        return jsonify({
            'success': False,
            'error': 'Invalid website URL'
        }), 400
    
    # Register user
    result = AuthenticationService.register_user(
        email=data['email'],
        password=data['password'],
        first_name=data.get('first_name', ''),
        last_name=data.get('last_name', ''),
        company=data.get('company', ''),
        wp_site_url=wp_site_url
    )
    
    if len(result) == 2 and isinstance(result[1], str):
        # Registration failed
        user, error = result
        return jsonify({
            'success': False,
            'error': error
        }), 400
    
    # Registration successful
    user, api_key = result
    
    # Generate JWT tokens
    tokens = AuthenticationService.generate_tokens(user)
    
    return jsonify({
        'success': True,
        'data': {
            'user': user.to_dict(),
            'api_key': api_key,
            'tokens': tokens
        }
    }), 201


@bp.route('/login', methods=['POST'])
@validate_json('email', 'password')
@handle_errors
@log_api_usage
def login():
    """Login with email and password"""
    data = request.get_json()
    
    # Authenticate user
    result = AuthenticationService.authenticate_email_password(
        data['email'],
        data['password']
    )
    
    if len(result) == 2 and isinstance(result[1], str):
        # Authentication failed
        user, error = result
        return jsonify({
            'success': False,
            'error': 'Invalid credentials'
        }), 401
    
    # Authentication successful
    user, auth_method = result
    
    # Generate JWT tokens
    tokens = AuthenticationService.generate_tokens(user)
    
    return jsonify({
        'success': True,
        'data': {
            'user': user.to_dict(),
            'auth_method': auth_method,
            'tokens': tokens
        }
    })


@bp.route('/login/wordpress', methods=['POST'])
@validate_json('username', 'password', 'site_url')
@handle_errors
@log_api_usage
def login_wordpress():
    """Login with WordPress credentials"""
    data = request.get_json()
    
    # Validate site URL
    if not validate_url(data['site_url']):
        return jsonify({
            'success': False,
            'error': 'Invalid WordPress site URL'
        }), 400
    
    # Authenticate with WordPress
    result = AuthenticationService.authenticate_wordpress(
        data['username'],
        data['password'],
        data['site_url']
    )
    
    if len(result) == 2 and isinstance(result[1], str):
        # Authentication failed
        user, error = result
        error_messages = {
            'invalid_credentials': 'Invalid WordPress credentials',
            'connection_error': 'Could not connect to WordPress site',
            'api_error': 'WordPress API error'
        }
        return jsonify({
            'success': False,
            'error': error_messages.get(error, 'WordPress authentication failed')
        }), 401
    
    # Authentication successful
    user, auth_method = result
    
    # Generate JWT tokens
    tokens = AuthenticationService.generate_tokens(user)
    
    return jsonify({
        'success': True,
        'data': {
            'user': user.to_dict(),
            'auth_method': auth_method,
            'tokens': tokens
        }
    })


@bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
@handle_errors
def refresh():
    """Refresh access token"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user or not user.is_active:
        return jsonify({
            'success': False,
            'error': 'User not found or inactive'
        }), 401
    
    # Generate new access token
    access_token = create_access_token(identity=user.id)
    
    return jsonify({
        'success': True,
        'data': {
            'access_token': access_token
        }
    })


@bp.route('/me', methods=['GET'])
@jwt_required()
@handle_errors
def get_current_user():
    """Get current user information"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({
            'success': False,
            'error': 'User not found'
        }), 404
    
    return jsonify({
        'success': True,
        'data': {
            'user': user.to_dict()
        }
    })


@bp.route('/logout', methods=['POST'])
@jwt_required()
@handle_errors
def logout():
    """Logout user (invalidate token)"""
    # In a real implementation, you would add the token to a blacklist
    # For now, we'll just return success
    return jsonify({
        'success': True,
        'message': 'Successfully logged out'
    })


@bp.route('/change-password', methods=['POST'])
@jwt_required()
@validate_json('current_password', 'new_password')
@handle_errors
def change_password():
    """Change user password"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({
            'success': False,
            'error': 'User not found'
        }), 404
    
    data = request.get_json()
    
    # Verify current password
    if not user.check_password(data['current_password']):
        return jsonify({
            'success': False,
            'error': 'Current password is incorrect'
        }), 400
    
    # Validate new password
    password_validation = validate_password(data['new_password'])
    if not password_validation['valid']:
        return jsonify({
            'success': False,
            'error': 'New password validation failed',
            'details': password_validation['errors']
        }), 400
    
    # Update password
    user.set_password(data['new_password'])
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': 'Password changed successfully'
    })


@bp.route('/api-keys', methods=['GET'])
@jwt_required()
@handle_errors
def get_api_keys():
    """Get user's API keys"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({
            'success': False,
            'error': 'User not found'
        }), 404
    
    api_keys = [key.to_dict() for key in user.api_keys if key.is_active]
    
    return jsonify({
        'success': True,
        'data': {
            'api_keys': api_keys
        }
    })


@bp.route('/api-keys', methods=['POST'])
@jwt_required()
@validate_json('description')
@handle_errors
def create_api_key():
    """Create a new API key"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return jsonify({
            'success': False,
            'error': 'User not found'
        }), 404
    
    data = request.get_json()
    
    # Generate API key
    api_key, api_key_record = user.generate_api_key(
        description=data['description'],
        website_id=data.get('website_id')
    )
    
    return jsonify({
        'success': True,
        'data': {
            'api_key': api_key,
            'api_key_record': api_key_record.to_dict()
        }
    }), 201
