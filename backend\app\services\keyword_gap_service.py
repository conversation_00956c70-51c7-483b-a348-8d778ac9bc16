"""
Keyword Gap Analysis Service
Advanced algorithms for identifying keyword gaps and content opportunities
"""
import logging
import re
from typing import Dict, List, Optional, Any, Tuple, Set
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from dataclasses import dataclass
from enum import Enum

# Handle optional dependencies
try:
    import numpy as np
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.cluster import KMeans
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False
    # Fallback implementations
    class TfidfVectorizerFallback:
        def fit_transform(self, texts):
            return [[len(text.split()) for text in texts]]
    TfidfVectorizer = TfidfVectorizerFallback
    def cosine_similarity(a, b):
        return [[0.5]]

from app import db, cache
from app.models.website import Website
from app.models.analysis import ContentAnalysis
from app.services.competitor_analysis_service import CompetitorAnalysisService

logger = logging.getLogger(__name__)


class GapType(Enum):
    """Types of keyword gaps"""
    MISSING_KEYWORD = "missing_keyword"
    UNDEROPTIMIZED_KEYWORD = "underoptimized_keyword"
    OPPORTUNITY_KEYWORD = "opportunity_keyword"
    TRENDING_KEYWORD = "trending_keyword"
    LONG_TAIL_OPPORTUNITY = "long_tail_opportunity"
    SEMANTIC_GAP = "semantic_gap"


@dataclass
class KeywordGap:
    """Represents a keyword gap opportunity"""
    keyword: str
    gap_type: GapType
    opportunity_score: float
    difficulty_score: float
    search_volume_estimate: int
    competitor_usage: List[str]
    suggested_content_type: str
    priority_level: str
    reasoning: str
    related_keywords: List[str]
    semantic_cluster: str


@dataclass
class ContentGap:
    """Represents a content gap opportunity"""
    topic: str
    gap_score: float
    content_type: str
    target_keywords: List[str]
    competitor_examples: List[Dict]
    suggested_approach: str
    estimated_effort: str
    potential_impact: str


class KeywordGapService:
    """Service for advanced keyword gap analysis"""
    
    def __init__(self):
        """Initialize the keyword gap service"""
        self.competitor_service = CompetitorAnalysisService()
        self.cache_timeout = 3600 * 12  # 12 hours
        
        # Analysis parameters
        self.min_keyword_length = 3
        self.max_keyword_length = 50
        self.min_search_volume = 10
        self.max_difficulty_score = 80
        
        # Scoring weights
        self.scoring_weights = {
            'competitor_usage': 0.3,
            'search_volume': 0.25,
            'difficulty': 0.2,
            'relevance': 0.15,
            'trend_factor': 0.1
        }
        
        # Content type mapping
        self.content_type_mapping = {
            'how': 'tutorial',
            'what': 'guide',
            'why': 'explanation',
            'best': 'comparison',
            'vs': 'comparison',
            'review': 'review',
            'tips': 'listicle',
            'guide': 'guide',
            'tutorial': 'tutorial'
        }
    
    def analyze_keyword_gaps(self, website_id: int, competitor_domains: List[str] = None) -> Dict[str, Any]:
        """Perform comprehensive keyword gap analysis"""
        try:
            # Check cache first
            cache_key = f"keyword_gaps_{website_id}_{hash(str(competitor_domains))}"
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result
            
            website = Website.query.get(website_id)
            if not website:
                raise ValueError(f"Website {website_id} not found")
            
            # Get competitor analysis
            competitor_analysis = self.competitor_service.analyze_competitors(
                website_id, competitor_domains
            )
            
            if 'error' in competitor_analysis:
                return competitor_analysis
            
            # Perform gap analysis
            keyword_gaps = self._identify_keyword_gaps(website, competitor_analysis)
            content_gaps = self._identify_content_gaps(website, competitor_analysis)
            semantic_gaps = self._identify_semantic_gaps(website, competitor_analysis)
            long_tail_opportunities = self._identify_long_tail_opportunities(website, competitor_analysis)
            
            # Generate comprehensive report
            gap_analysis = {
                'website_id': website_id,
                'analysis_date': datetime.now().isoformat(),
                'competitor_count': competitor_analysis['analysis_summary']['competitors_analyzed'],
                'keyword_gaps': keyword_gaps,
                'content_gaps': content_gaps,
                'semantic_gaps': semantic_gaps,
                'long_tail_opportunities': long_tail_opportunities,
                'summary_metrics': self._calculate_summary_metrics(keyword_gaps, content_gaps),
                'recommendations': self._generate_recommendations(keyword_gaps, content_gaps)
            }
            
            # Cache the results
            cache.set(cache_key, gap_analysis, timeout=self.cache_timeout)
            
            return gap_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing keyword gaps: {str(e)}")
            return {'error': str(e)}
    
    def _identify_keyword_gaps(self, website: Website, competitor_analysis: Dict) -> List[KeywordGap]:
        """Identify missing and underoptimized keywords"""
        try:
            # Get source website keywords
            source_analysis = ContentAnalysis.query.filter_by(
                website_id=website.id
            ).order_by(ContentAnalysis.analyzed_at.desc()).first()
            
            source_keywords = set()
            if source_analysis and source_analysis.keywords:
                for kw in source_analysis.keywords.get('primary_keywords', []):
                    source_keywords.add(kw.get('keyword', '').lower())
            
            # Collect competitor keywords
            competitor_keywords = defaultdict(list)
            competitor_details = competitor_analysis.get('competitor_details', {})
            
            for domain, analysis in competitor_details.items():
                keyword_analysis = analysis.get('keyword_analysis', {})
                for kw in keyword_analysis.get('content_keywords', []):
                    keyword = kw['keyword'].lower()
                    if self._is_valid_keyword(keyword):
                        competitor_keywords[keyword].append({
                            'domain': domain,
                            'frequency': kw['frequency'],
                            'score': kw['score']
                        })
            
            # Identify gaps
            keyword_gaps = []
            
            for keyword, usage_data in competitor_keywords.items():
                if keyword not in source_keywords:
                    # Missing keyword
                    gap = self._create_keyword_gap(
                        keyword, GapType.MISSING_KEYWORD, usage_data, website
                    )
                    if gap:
                        keyword_gaps.append(gap)
                elif len(usage_data) > 1:
                    # Potentially underoptimized keyword
                    gap = self._create_keyword_gap(
                        keyword, GapType.UNDEROPTIMIZED_KEYWORD, usage_data, website
                    )
                    if gap and gap.opportunity_score > 30:
                        keyword_gaps.append(gap)
            
            # Sort by opportunity score
            keyword_gaps.sort(key=lambda x: x.opportunity_score, reverse=True)
            
            return keyword_gaps[:50]  # Top 50 gaps
            
        except Exception as e:
            logger.error(f"Error identifying keyword gaps: {str(e)}")
            return []
    
    def _identify_content_gaps(self, website: Website, competitor_analysis: Dict) -> List[ContentGap]:
        """Identify content topic gaps"""
        try:
            # Get source website topics
            source_analysis = ContentAnalysis.query.filter_by(
                website_id=website.id
            ).order_by(ContentAnalysis.analyzed_at.desc()).first()
            
            source_topics = set()
            if source_analysis and source_analysis.topics:
                for topic in source_analysis.topics.get('primary_topics', []):
                    source_topics.add(topic.get('topic', '').lower())
            
            # Collect competitor topics
            competitor_topics = defaultdict(list)
            competitor_details = competitor_analysis.get('competitor_details', {})
            
            for domain, analysis in competitor_details.items():
                content_analysis = analysis.get('content_analysis', {})
                for topic_data in content_analysis.get('content_topics', []):
                    topic = topic_data['topic'].lower()
                    competitor_topics[topic].append({
                        'domain': domain,
                        'frequency': topic_data['frequency']
                    })
            
            # Identify content gaps
            content_gaps = []
            
            for topic, usage_data in competitor_topics.items():
                if topic not in source_topics and len(usage_data) >= 2:
                    gap_score = self._calculate_content_gap_score(topic, usage_data)
                    
                    if gap_score > 20:  # Minimum threshold
                        content_gap = ContentGap(
                            topic=topic,
                            gap_score=gap_score,
                            content_type=self._suggest_content_type(topic),
                            target_keywords=self._extract_related_keywords(topic, competitor_analysis),
                            competitor_examples=usage_data,
                            suggested_approach=self._suggest_content_approach(topic),
                            estimated_effort=self._estimate_content_effort(topic, usage_data),
                            potential_impact=self._estimate_content_impact(gap_score)
                        )
                        content_gaps.append(content_gap)
            
            # Sort by gap score
            content_gaps.sort(key=lambda x: x.gap_score, reverse=True)
            
            return content_gaps[:20]  # Top 20 content gaps
            
        except Exception as e:
            logger.error(f"Error identifying content gaps: {str(e)}")
            return []
    
    def _identify_semantic_gaps(self, website: Website, competitor_analysis: Dict) -> List[KeywordGap]:
        """Identify semantic keyword opportunities using clustering"""
        try:
            if not HAS_SKLEARN:
                logger.warning("Scikit-learn not available, skipping semantic gap analysis")
                return []
            
            # Collect all competitor keywords
            all_keywords = []
            competitor_details = competitor_analysis.get('competitor_details', {})
            
            for domain, analysis in competitor_details.items():
                keyword_analysis = analysis.get('keyword_analysis', {})
                for kw in keyword_analysis.get('content_keywords', []):
                    if self._is_valid_keyword(kw['keyword']):
                        all_keywords.append(kw['keyword'].lower())
            
            if len(all_keywords) < 10:
                return []
            
            # Create TF-IDF vectors for clustering
            vectorizer = TfidfVectorizer(max_features=500, stop_words='english')
            tfidf_matrix = vectorizer.fit_transform(all_keywords)
            
            # Perform clustering
            n_clusters = min(10, len(all_keywords) // 5)
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            clusters = kmeans.fit_predict(tfidf_matrix)
            
            # Analyze clusters for gaps
            semantic_gaps = []
            source_keywords = self._get_source_keywords(website)
            
            for cluster_id in range(n_clusters):
                cluster_keywords = [all_keywords[i] for i, c in enumerate(clusters) if c == cluster_id]
                
                # Check if source website has representation in this cluster
                cluster_coverage = len([kw for kw in cluster_keywords if kw in source_keywords])
                coverage_ratio = cluster_coverage / len(cluster_keywords)
                
                if coverage_ratio < 0.3:  # Low coverage indicates a gap
                    # Find representative keywords for this cluster
                    for keyword in cluster_keywords[:3]:  # Top 3 from cluster
                        if keyword not in source_keywords:
                            gap = KeywordGap(
                                keyword=keyword,
                                gap_type=GapType.SEMANTIC_GAP,
                                opportunity_score=self._calculate_semantic_opportunity_score(keyword, cluster_keywords),
                                difficulty_score=self._estimate_keyword_difficulty(keyword),
                                search_volume_estimate=self._estimate_search_volume(keyword),
                                competitor_usage=[],
                                suggested_content_type=self._suggest_content_type(keyword),
                                priority_level=self._determine_priority_level(50),
                                reasoning=f"Semantic gap in cluster with {len(cluster_keywords)} related keywords",
                                related_keywords=cluster_keywords[:5],
                                semantic_cluster=f"cluster_{cluster_id}"
                            )
                            semantic_gaps.append(gap)
            
            return semantic_gaps[:15]  # Top 15 semantic gaps
            
        except Exception as e:
            logger.error(f"Error identifying semantic gaps: {str(e)}")
            return []
    
    def _identify_long_tail_opportunities(self, website: Website, competitor_analysis: Dict) -> List[KeywordGap]:
        """Identify long-tail keyword opportunities"""
        try:
            # Collect competitor long-tail keywords
            long_tail_keywords = defaultdict(list)
            competitor_details = competitor_analysis.get('competitor_details', {})
            
            for domain, analysis in competitor_details.items():
                keyword_analysis = analysis.get('keyword_analysis', {})
                for phrase_data in keyword_analysis.get('long_tail_keywords', []):
                    phrase = phrase_data['phrase'].lower()
                    if self._is_valid_long_tail_keyword(phrase):
                        long_tail_keywords[phrase].append({
                            'domain': domain,
                            'frequency': phrase_data['frequency'],
                            'word_count': phrase_data['word_count']
                        })
            
            # Get source long-tail keywords
            source_long_tail = self._get_source_long_tail_keywords(website)
            
            # Identify opportunities
            long_tail_opportunities = []
            
            for phrase, usage_data in long_tail_keywords.items():
                if phrase not in source_long_tail and len(usage_data) >= 2:
                    opportunity_score = self._calculate_long_tail_opportunity_score(phrase, usage_data)
                    
                    if opportunity_score > 25:
                        gap = KeywordGap(
                            keyword=phrase,
                            gap_type=GapType.LONG_TAIL_OPPORTUNITY,
                            opportunity_score=opportunity_score,
                            difficulty_score=self._estimate_long_tail_difficulty(phrase),
                            search_volume_estimate=self._estimate_long_tail_volume(phrase),
                            competitor_usage=[data['domain'] for data in usage_data],
                            suggested_content_type=self._suggest_long_tail_content_type(phrase),
                            priority_level=self._determine_priority_level(opportunity_score),
                            reasoning=f"Long-tail opportunity used by {len(usage_data)} competitors",
                            related_keywords=self._find_related_long_tail(phrase, long_tail_keywords),
                            semantic_cluster="long_tail"
                        )
                        long_tail_opportunities.append(gap)
            
            # Sort by opportunity score
            long_tail_opportunities.sort(key=lambda x: x.opportunity_score, reverse=True)
            
            return long_tail_opportunities[:25]  # Top 25 long-tail opportunities
            
        except Exception as e:
            logger.error(f"Error identifying long-tail opportunities: {str(e)}")
            return []

    def _is_valid_keyword(self, keyword: str) -> bool:
        """Check if keyword is valid for analysis"""
        if not keyword or len(keyword) < self.min_keyword_length:
            return False
        if len(keyword) > self.max_keyword_length:
            return False
        if keyword.isdigit():
            return False
        if len(keyword.split()) > 4:  # Too many words
            return False

        # Filter out common stop words and low-value terms
        stop_words = {
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
            'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
            'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy'
        }

        return keyword not in stop_words

    def _is_valid_long_tail_keyword(self, phrase: str) -> bool:
        """Check if long-tail phrase is valid for analysis"""
        words = phrase.split()
        if len(words) < 2 or len(words) > 6:
            return False
        if len(phrase) < 10 or len(phrase) > 100:
            return False

        # Must contain at least one meaningful word
        meaningful_words = [w for w in words if len(w) > 3 and w.isalpha()]
        return len(meaningful_words) >= 1

    def _create_keyword_gap(self, keyword: str, gap_type: GapType, usage_data: List[Dict], website: Website) -> Optional[KeywordGap]:
        """Create a KeywordGap object"""
        try:
            opportunity_score = self._calculate_opportunity_score(keyword, usage_data, gap_type)
            difficulty_score = self._estimate_keyword_difficulty(keyword)

            # Skip if opportunity score is too low
            if opportunity_score < 15:
                return None

            return KeywordGap(
                keyword=keyword,
                gap_type=gap_type,
                opportunity_score=opportunity_score,
                difficulty_score=difficulty_score,
                search_volume_estimate=self._estimate_search_volume(keyword),
                competitor_usage=[data['domain'] for data in usage_data],
                suggested_content_type=self._suggest_content_type(keyword),
                priority_level=self._determine_priority_level(opportunity_score),
                reasoning=self._generate_gap_reasoning(keyword, gap_type, usage_data),
                related_keywords=self._find_related_keywords(keyword),
                semantic_cluster=self._determine_semantic_cluster(keyword)
            )

        except Exception as e:
            logger.error(f"Error creating keyword gap: {str(e)}")
            return None

    def _calculate_opportunity_score(self, keyword: str, usage_data: List[Dict], gap_type: GapType) -> float:
        """Calculate opportunity score for a keyword"""
        try:
            # Base score from competitor usage
            competitor_count = len(usage_data)
            total_frequency = sum(data['frequency'] for data in usage_data)
            avg_frequency = total_frequency / max(competitor_count, 1)

            # Competitor usage score (0-40 points)
            usage_score = min(competitor_count * 8, 40)

            # Frequency score (0-30 points)
            frequency_score = min(avg_frequency * 2, 30)

            # Keyword characteristics score (0-20 points)
            char_score = 0
            if len(keyword.split()) == 1:
                char_score += 5  # Single word bonus
            if any(intent in keyword for intent in ['how', 'what', 'why', 'best']):
                char_score += 10  # Intent keyword bonus
            if len(keyword) > 8:
                char_score += 5  # Longer keyword bonus

            # Gap type modifier (0-10 points)
            type_modifier = {
                GapType.MISSING_KEYWORD: 10,
                GapType.UNDEROPTIMIZED_KEYWORD: 5,
                GapType.OPPORTUNITY_KEYWORD: 8,
                GapType.TRENDING_KEYWORD: 12,
                GapType.LONG_TAIL_OPPORTUNITY: 7,
                GapType.SEMANTIC_GAP: 6
            }.get(gap_type, 5)

            total_score = usage_score + frequency_score + char_score + type_modifier
            return round(total_score, 2)

        except Exception as e:
            logger.error(f"Error calculating opportunity score: {str(e)}")
            return 0.0

    def _estimate_keyword_difficulty(self, keyword: str) -> float:
        """Estimate keyword difficulty (simplified algorithm)"""
        try:
            # Base difficulty on keyword characteristics
            difficulty = 30  # Base difficulty

            # Single word keywords are generally harder
            if len(keyword.split()) == 1:
                difficulty += 20

            # Longer keywords are generally easier
            word_count = len(keyword.split())
            if word_count >= 3:
                difficulty -= (word_count - 2) * 5

            # Commercial intent keywords are harder
            commercial_terms = ['buy', 'price', 'cost', 'cheap', 'deal', 'sale', 'discount']
            if any(term in keyword.lower() for term in commercial_terms):
                difficulty += 15

            # Informational keywords are easier
            info_terms = ['how', 'what', 'why', 'guide', 'tutorial', 'tips']
            if any(term in keyword.lower() for term in info_terms):
                difficulty -= 10

            # Ensure difficulty is within bounds
            difficulty = max(10, min(90, difficulty))

            return round(difficulty, 1)

        except Exception as e:
            logger.error(f"Error estimating keyword difficulty: {str(e)}")
            return 50.0

    def _estimate_search_volume(self, keyword: str) -> int:
        """Estimate search volume (simplified algorithm)"""
        try:
            # Base volume estimation
            base_volume = 100

            # Adjust based on keyword length
            word_count = len(keyword.split())
            if word_count == 1:
                base_volume *= 5  # Single words have higher volume
            elif word_count == 2:
                base_volume *= 3
            elif word_count >= 4:
                base_volume *= 0.5  # Long-tail has lower volume

            # Adjust based on keyword type
            if any(term in keyword.lower() for term in ['how', 'what', 'why']):
                base_volume *= 2  # Question keywords have good volume

            if any(term in keyword.lower() for term in ['best', 'top', 'review']):
                base_volume *= 1.5  # Comparison keywords have good volume

            # Add some randomization to make it more realistic
            import random
            variation = random.uniform(0.7, 1.3)
            estimated_volume = int(base_volume * variation)

            return max(10, estimated_volume)

        except Exception as e:
            logger.error(f"Error estimating search volume: {str(e)}")
            return 100

    def _suggest_content_type(self, keyword: str) -> str:
        """Suggest content type based on keyword"""
        keyword_lower = keyword.lower()

        for trigger, content_type in self.content_type_mapping.items():
            if trigger in keyword_lower:
                return content_type

        # Default suggestions based on keyword characteristics
        if len(keyword.split()) == 1:
            return 'pillar_page'
        elif any(word in keyword_lower for word in ['compare', 'vs', 'versus']):
            return 'comparison'
        elif any(word in keyword_lower for word in ['list', 'top', 'best']):
            return 'listicle'
        else:
            return 'blog_post'

    def _determine_priority_level(self, opportunity_score: float) -> str:
        """Determine priority level based on opportunity score"""
        if opportunity_score >= 70:
            return 'high'
        elif opportunity_score >= 40:
            return 'medium'
        else:
            return 'low'

    def _generate_gap_reasoning(self, keyword: str, gap_type: GapType, usage_data: List[Dict]) -> str:
        """Generate reasoning for the keyword gap"""
        competitor_count = len(usage_data)
        avg_frequency = sum(data['frequency'] for data in usage_data) / max(competitor_count, 1)

        if gap_type == GapType.MISSING_KEYWORD:
            return f"Keyword used by {competitor_count} competitors with average frequency {avg_frequency:.1f}"
        elif gap_type == GapType.UNDEROPTIMIZED_KEYWORD:
            return f"Keyword present but underoptimized compared to {competitor_count} competitors"
        elif gap_type == GapType.OPPORTUNITY_KEYWORD:
            return f"High-opportunity keyword with moderate competition"
        else:
            return f"Identified through {gap_type.value} analysis"

    def _find_related_keywords(self, keyword: str) -> List[str]:
        """Find related keywords (simplified implementation)"""
        # This is a simplified implementation
        # In production, you might use word embeddings or keyword research APIs
        related = []

        # Add variations with common modifiers
        modifiers = ['best', 'top', 'how to', 'guide', 'tips', 'tutorial']
        for modifier in modifiers:
            if modifier not in keyword.lower():
                related.append(f"{modifier} {keyword}")

        # Add plural/singular variations
        if keyword.endswith('s') and len(keyword) > 4:
            related.append(keyword[:-1])
        elif not keyword.endswith('s'):
            related.append(f"{keyword}s")

        return related[:5]  # Limit to 5 related keywords

    def _determine_semantic_cluster(self, keyword: str) -> str:
        """Determine semantic cluster for keyword"""
        # Simplified clustering based on keyword characteristics
        if any(word in keyword.lower() for word in ['how', 'tutorial', 'guide']):
            return 'educational'
        elif any(word in keyword.lower() for word in ['best', 'top', 'review']):
            return 'comparison'
        elif any(word in keyword.lower() for word in ['buy', 'price', 'cost']):
            return 'commercial'
        elif any(word in keyword.lower() for word in ['what', 'why', 'when']):
            return 'informational'
        else:
            return 'general'

    def _get_source_keywords(self, website: Website) -> Set[str]:
        """Get keywords from source website"""
        source_keywords = set()

        source_analysis = ContentAnalysis.query.filter_by(
            website_id=website.id
        ).order_by(ContentAnalysis.analyzed_at.desc()).first()

        if source_analysis and source_analysis.keywords:
            for kw in source_analysis.keywords.get('primary_keywords', []):
                source_keywords.add(kw.get('keyword', '').lower())

        return source_keywords

    def _get_source_long_tail_keywords(self, website: Website) -> Set[str]:
        """Get long-tail keywords from source website"""
        # This would typically extract long-tail keywords from the source content
        # For now, return empty set as placeholder
        return set()

    def _calculate_content_gap_score(self, topic: str, usage_data: List[Dict]) -> float:
        """Calculate content gap score for a topic"""
        competitor_count = len(usage_data)
        total_frequency = sum(data['frequency'] for data in usage_data)

        # Base score from competitor usage
        base_score = competitor_count * 15

        # Frequency bonus
        frequency_bonus = min(total_frequency / max(competitor_count, 1), 20)

        return round(base_score + frequency_bonus, 2)

    def _extract_related_keywords(self, topic: str, competitor_analysis: Dict) -> List[str]:
        """Extract keywords related to a topic"""
        related_keywords = []

        # Search through competitor keywords for topic-related terms
        competitor_details = competitor_analysis.get('competitor_details', {})

        for domain, analysis in competitor_details.items():
            keyword_analysis = analysis.get('keyword_analysis', {})
            for kw in keyword_analysis.get('content_keywords', []):
                if topic.lower() in kw['keyword'].lower():
                    related_keywords.append(kw['keyword'])

        return list(set(related_keywords))[:10]  # Top 10 unique related keywords

    def _suggest_content_approach(self, topic: str) -> str:
        """Suggest content approach for a topic"""
        if any(word in topic.lower() for word in ['how', 'tutorial']):
            return "Create step-by-step tutorial content"
        elif any(word in topic.lower() for word in ['best', 'top']):
            return "Create comparison or listicle content"
        elif any(word in topic.lower() for word in ['what', 'why']):
            return "Create educational/explanatory content"
        else:
            return "Create comprehensive guide content"

    def _estimate_content_effort(self, topic: str, usage_data: List[Dict]) -> str:
        """Estimate effort required for content creation"""
        competitor_count = len(usage_data)

        if competitor_count >= 4:
            return "high"  # Many competitors = need comprehensive content
        elif competitor_count >= 2:
            return "medium"
        else:
            return "low"

    def _estimate_content_impact(self, gap_score: float) -> str:
        """Estimate potential impact of addressing content gap"""
        if gap_score >= 60:
            return "high"
        elif gap_score >= 30:
            return "medium"
        else:
            return "low"

    def _calculate_summary_metrics(self, keyword_gaps: List[KeywordGap], content_gaps: List[ContentGap]) -> Dict:
        """Calculate summary metrics for the gap analysis"""
        try:
            total_gaps = len(keyword_gaps) + len(content_gaps)

            if not keyword_gaps:
                return {'total_gaps': total_gaps, 'avg_opportunity_score': 0}

            # Calculate average opportunity scores
            avg_keyword_score = sum(gap.opportunity_score for gap in keyword_gaps) / len(keyword_gaps)
            avg_content_score = sum(gap.gap_score for gap in content_gaps) / max(len(content_gaps), 1)

            # Count by priority
            high_priority = len([gap for gap in keyword_gaps if gap.priority_level == 'high'])
            medium_priority = len([gap for gap in keyword_gaps if gap.priority_level == 'medium'])
            low_priority = len([gap for gap in keyword_gaps if gap.priority_level == 'low'])

            return {
                'total_gaps': total_gaps,
                'keyword_gaps_count': len(keyword_gaps),
                'content_gaps_count': len(content_gaps),
                'avg_keyword_opportunity_score': round(avg_keyword_score, 2),
                'avg_content_gap_score': round(avg_content_score, 2),
                'priority_distribution': {
                    'high': high_priority,
                    'medium': medium_priority,
                    'low': low_priority
                }
            }

        except Exception as e:
            logger.error(f"Error calculating summary metrics: {str(e)}")
            return {'total_gaps': 0, 'error': str(e)}

    def _generate_recommendations(self, keyword_gaps: List[KeywordGap], content_gaps: List[ContentGap]) -> List[Dict]:
        """Generate actionable recommendations based on gap analysis"""
        recommendations = []

        try:
            # Top keyword recommendations
            high_priority_keywords = [gap for gap in keyword_gaps if gap.priority_level == 'high'][:5]

            for gap in high_priority_keywords:
                recommendations.append({
                    'type': 'keyword_optimization',
                    'priority': 'high',
                    'action': f"Create content targeting '{gap.keyword}'",
                    'reasoning': gap.reasoning,
                    'suggested_content_type': gap.suggested_content_type,
                    'estimated_difficulty': gap.difficulty_score,
                    'opportunity_score': gap.opportunity_score
                })

            # Top content recommendations
            high_impact_content = [gap for gap in content_gaps if gap.potential_impact == 'high'][:3]

            for gap in high_impact_content:
                recommendations.append({
                    'type': 'content_creation',
                    'priority': 'high',
                    'action': f"Develop content around '{gap.topic}' topic",
                    'reasoning': f"Content gap with score {gap.gap_score}",
                    'suggested_approach': gap.suggested_approach,
                    'estimated_effort': gap.estimated_effort,
                    'target_keywords': gap.target_keywords[:5]
                })

            # Quick wins (low difficulty, high opportunity)
            quick_wins = [gap for gap in keyword_gaps
                         if gap.difficulty_score < 40 and gap.opportunity_score > 50][:3]

            for gap in quick_wins:
                recommendations.append({
                    'type': 'quick_win',
                    'priority': 'medium',
                    'action': f"Quick content creation for '{gap.keyword}'",
                    'reasoning': "Low difficulty, high opportunity keyword",
                    'suggested_content_type': gap.suggested_content_type,
                    'estimated_difficulty': gap.difficulty_score,
                    'opportunity_score': gap.opportunity_score
                })

            return recommendations[:10]  # Top 10 recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return []
