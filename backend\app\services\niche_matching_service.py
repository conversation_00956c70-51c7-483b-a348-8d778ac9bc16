"""
Comprehensive Niche/Category Matching Service
Implements hierarchical category system with parent-child relationships,
cross-niche compatibility scoring, and industry-specific matching rules
"""
import logging
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class CompatibilityLevel(Enum):
    """Compatibility levels between niches"""
    IDENTICAL = 1.0
    HIGHLY_COMPATIBLE = 0.9
    COMPATIBLE = 0.7
    SOMEWHAT_COMPATIBLE = 0.5
    COMPLEMENTARY = 0.6
    NEUTRAL = 0.3
    INCOMPATIBLE = 0.1


@dataclass
class NicheCategory:
    """Represents a niche category with hierarchical information"""
    id: str
    name: str
    parent_id: Optional[str] = None
    level: int = 0
    keywords: List[str] = None
    industry_tags: List[str] = None
    audience_demographics: Dict = None
    seasonal_patterns: Dict = None
    
    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []
        if self.industry_tags is None:
            self.industry_tags = []
        if self.audience_demographics is None:
            self.audience_demographics = {}
        if self.seasonal_patterns is None:
            self.seasonal_patterns = {}


class NicheMatchingService:
    """Comprehensive niche and category matching service"""
    
    def __init__(self):
        """Initialize the niche matching service"""
        self.categories = {}
        self.compatibility_matrix = {}
        self.industry_rules = {}
        self.complementary_pairs = {}
        
        # Initialize the category hierarchy
        self._initialize_category_hierarchy()
        self._initialize_compatibility_matrix()
        self._initialize_industry_rules()
        self._initialize_complementary_pairs()
    
    def _initialize_category_hierarchy(self):
        """Initialize the hierarchical category system"""
        # Level 0: Root categories
        root_categories = [
            NicheCategory("tech", "Technology", level=0, 
                         keywords=["technology", "tech", "digital", "software", "hardware"],
                         industry_tags=["B2B", "innovation", "startup"]),
            NicheCategory("business", "Business", level=0,
                         keywords=["business", "corporate", "enterprise", "company"],
                         industry_tags=["B2B", "professional", "corporate"]),
            NicheCategory("health", "Health & Wellness", level=0,
                         keywords=["health", "wellness", "medical", "fitness"],
                         industry_tags=["B2C", "healthcare", "lifestyle"]),
            NicheCategory("lifestyle", "Lifestyle", level=0,
                         keywords=["lifestyle", "living", "personal", "home"],
                         industry_tags=["B2C", "consumer", "personal"]),
            NicheCategory("education", "Education", level=0,
                         keywords=["education", "learning", "teaching", "academic"],
                         industry_tags=["B2B", "B2C", "institutional"]),
            NicheCategory("finance", "Finance", level=0,
                         keywords=["finance", "money", "investment", "banking"],
                         industry_tags=["B2B", "B2C", "financial"]),
            NicheCategory("entertainment", "Entertainment", level=0,
                         keywords=["entertainment", "media", "content", "creative"],
                         industry_tags=["B2C", "media", "creative"]),
            NicheCategory("travel", "Travel & Tourism", level=0,
                         keywords=["travel", "tourism", "vacation", "destination"],
                         industry_tags=["B2C", "hospitality", "leisure"])
        ]
        
        # Level 1: Sub-categories
        sub_categories = [
            # Technology sub-categories
            NicheCategory("tech_software", "Software Development", parent_id="tech", level=1,
                         keywords=["programming", "coding", "development", "apps"],
                         industry_tags=["B2B", "SaaS", "development"]),
            NicheCategory("tech_ai", "Artificial Intelligence", parent_id="tech", level=1,
                         keywords=["AI", "machine learning", "automation", "neural networks"],
                         industry_tags=["B2B", "innovation", "research"]),
            NicheCategory("tech_cybersecurity", "Cybersecurity", parent_id="tech", level=1,
                         keywords=["security", "cyber", "protection", "privacy"],
                         industry_tags=["B2B", "enterprise", "security"]),
            
            # Business sub-categories
            NicheCategory("business_marketing", "Marketing", parent_id="business", level=1,
                         keywords=["marketing", "advertising", "promotion", "branding"],
                         industry_tags=["B2B", "B2C", "agency"]),
            NicheCategory("business_finance", "Business Finance", parent_id="business", level=1,
                         keywords=["accounting", "bookkeeping", "financial planning"],
                         industry_tags=["B2B", "professional", "services"]),
            NicheCategory("business_hr", "Human Resources", parent_id="business", level=1,
                         keywords=["HR", "recruitment", "talent", "workplace"],
                         industry_tags=["B2B", "corporate", "services"]),
            
            # Health sub-categories
            NicheCategory("health_fitness", "Fitness", parent_id="health", level=1,
                         keywords=["fitness", "exercise", "workout", "gym"],
                         industry_tags=["B2C", "lifestyle", "wellness"]),
            NicheCategory("health_nutrition", "Nutrition", parent_id="health", level=1,
                         keywords=["nutrition", "diet", "food", "supplements"],
                         industry_tags=["B2C", "wellness", "lifestyle"]),
            NicheCategory("health_mental", "Mental Health", parent_id="health", level=1,
                         keywords=["mental health", "therapy", "wellness", "mindfulness"],
                         industry_tags=["B2C", "healthcare", "wellness"]),
            
            # Lifestyle sub-categories
            NicheCategory("lifestyle_home", "Home & Garden", parent_id="lifestyle", level=1,
                         keywords=["home", "garden", "interior", "decoration"],
                         industry_tags=["B2C", "consumer", "home"]),
            NicheCategory("lifestyle_fashion", "Fashion", parent_id="lifestyle", level=1,
                         keywords=["fashion", "style", "clothing", "trends"],
                         industry_tags=["B2C", "retail", "lifestyle"]),
            NicheCategory("lifestyle_food", "Food & Cooking", parent_id="lifestyle", level=1,
                         keywords=["food", "cooking", "recipes", "culinary"],
                         industry_tags=["B2C", "lifestyle", "consumer"]),
            
            # Education sub-categories
            NicheCategory("education_online", "Online Learning", parent_id="education", level=1,
                         keywords=["online learning", "e-learning", "courses", "training"],
                         industry_tags=["B2B", "B2C", "edtech"]),
            NicheCategory("education_k12", "K-12 Education", parent_id="education", level=1,
                         keywords=["school", "K-12", "elementary", "secondary"],
                         industry_tags=["B2B", "institutional", "education"]),
            NicheCategory("education_higher", "Higher Education", parent_id="education", level=1,
                         keywords=["university", "college", "higher education", "academic"],
                         industry_tags=["B2B", "institutional", "academic"]),
            
            # Finance sub-categories
            NicheCategory("finance_personal", "Personal Finance", parent_id="finance", level=1,
                         keywords=["personal finance", "budgeting", "savings", "investing"],
                         industry_tags=["B2C", "financial", "advisory"]),
            NicheCategory("finance_crypto", "Cryptocurrency", parent_id="finance", level=1,
                         keywords=["crypto", "blockchain", "bitcoin", "digital currency"],
                         industry_tags=["B2C", "fintech", "investment"]),
            NicheCategory("finance_real_estate", "Real Estate", parent_id="finance", level=1,
                         keywords=["real estate", "property", "housing", "investment"],
                         industry_tags=["B2B", "B2C", "investment"])
        ]
        
        # Store all categories
        all_categories = root_categories + sub_categories
        for category in all_categories:
            self.categories[category.id] = category
    
    def _initialize_compatibility_matrix(self):
        """Initialize compatibility scores between categories"""
        # Define compatibility relationships
        compatibility_rules = [
            # Technology compatibilities
            ("tech", "business", CompatibilityLevel.HIGHLY_COMPATIBLE),
            ("tech", "education", CompatibilityLevel.COMPATIBLE),
            ("tech", "finance", CompatibilityLevel.COMPATIBLE),
            ("tech_software", "business_marketing", CompatibilityLevel.COMPATIBLE),
            ("tech_ai", "business", CompatibilityLevel.HIGHLY_COMPATIBLE),
            ("tech_cybersecurity", "business", CompatibilityLevel.HIGHLY_COMPATIBLE),
            
            # Business compatibilities
            ("business", "finance", CompatibilityLevel.HIGHLY_COMPATIBLE),
            ("business", "education", CompatibilityLevel.COMPATIBLE),
            ("business_marketing", "lifestyle", CompatibilityLevel.COMPATIBLE),
            ("business_marketing", "entertainment", CompatibilityLevel.COMPATIBLE),
            
            # Health compatibilities
            ("health", "lifestyle", CompatibilityLevel.HIGHLY_COMPATIBLE),
            ("health_fitness", "lifestyle_food", CompatibilityLevel.COMPATIBLE),
            ("health_nutrition", "lifestyle_food", CompatibilityLevel.HIGHLY_COMPATIBLE),
            ("health_mental", "lifestyle", CompatibilityLevel.COMPATIBLE),
            
            # Education compatibilities
            ("education", "tech", CompatibilityLevel.COMPATIBLE),
            ("education", "business", CompatibilityLevel.COMPATIBLE),
            ("education_online", "tech", CompatibilityLevel.HIGHLY_COMPATIBLE),
            
            # Finance compatibilities
            ("finance", "business", CompatibilityLevel.HIGHLY_COMPATIBLE),
            ("finance", "tech", CompatibilityLevel.COMPATIBLE),
            ("finance_crypto", "tech", CompatibilityLevel.HIGHLY_COMPATIBLE),
            ("finance_real_estate", "lifestyle_home", CompatibilityLevel.COMPATIBLE),
            
            # Lifestyle compatibilities
            ("lifestyle", "health", CompatibilityLevel.HIGHLY_COMPATIBLE),
            ("lifestyle", "travel", CompatibilityLevel.COMPATIBLE),
            ("lifestyle_fashion", "entertainment", CompatibilityLevel.COMPATIBLE),
            ("lifestyle_food", "health_nutrition", CompatibilityLevel.HIGHLY_COMPATIBLE),
            
            # Entertainment compatibilities
            ("entertainment", "lifestyle", CompatibilityLevel.COMPATIBLE),
            ("entertainment", "tech", CompatibilityLevel.SOMEWHAT_COMPATIBLE),
            
            # Travel compatibilities
            ("travel", "lifestyle", CompatibilityLevel.COMPATIBLE),
            ("travel", "entertainment", CompatibilityLevel.SOMEWHAT_COMPATIBLE)
        ]
        
        # Build compatibility matrix
        for cat1_id, cat2_id, level in compatibility_rules:
            if cat1_id not in self.compatibility_matrix:
                self.compatibility_matrix[cat1_id] = {}
            if cat2_id not in self.compatibility_matrix:
                self.compatibility_matrix[cat2_id] = {}
            
            # Set bidirectional compatibility
            self.compatibility_matrix[cat1_id][cat2_id] = level.value
            self.compatibility_matrix[cat2_id][cat1_id] = level.value
    
    def _initialize_industry_rules(self):
        """Initialize industry-specific matching rules"""
        self.industry_rules = {
            "B2B": {
                "preferred_partners": ["B2B", "professional", "enterprise"],
                "avoid_partners": ["consumer", "personal"],
                "compatibility_bonus": 0.1
            },
            "B2C": {
                "preferred_partners": ["B2C", "consumer", "lifestyle"],
                "avoid_partners": ["enterprise", "institutional"],
                "compatibility_bonus": 0.1
            },
            "SaaS": {
                "preferred_partners": ["B2B", "tech", "business"],
                "avoid_partners": ["consumer", "lifestyle"],
                "compatibility_bonus": 0.15
            },
            "healthcare": {
                "preferred_partners": ["wellness", "lifestyle", "B2C"],
                "avoid_partners": ["entertainment", "gaming"],
                "compatibility_bonus": 0.1
            }
        }
    
    def _initialize_complementary_pairs(self):
        """Initialize complementary niche pairs that work well together"""
        self.complementary_pairs = {
            "tech_software": ["business_marketing", "education_online", "finance_crypto"],
            "business_marketing": ["tech_software", "lifestyle_fashion", "entertainment"],
            "health_fitness": ["lifestyle_food", "health_nutrition", "lifestyle_home"],
            "finance_personal": ["business", "education_online", "lifestyle"],
            "education_online": ["tech", "business", "finance_personal"],
            "lifestyle_food": ["health_nutrition", "health_fitness", "travel"],
            "travel": ["lifestyle", "entertainment", "lifestyle_food"],
            "tech_ai": ["business", "education", "finance"],
            "finance_crypto": ["tech", "finance_personal", "business"],
            "health_mental": ["lifestyle", "education", "health_fitness"]
        }
    
    def calculate_niche_compatibility(self, source_category: str, target_category: str) -> Dict[str, float]:
        """Calculate comprehensive niche compatibility score"""
        try:
            result = {
                'base_compatibility': 0.0,
                'hierarchical_bonus': 0.0,
                'industry_bonus': 0.0,
                'complementary_bonus': 0.0,
                'total_score': 0.0,
                'explanation': []
            }
            
            # 1. Base compatibility from matrix
            base_score = self._get_base_compatibility(source_category, target_category)
            result['base_compatibility'] = base_score
            
            # 2. Hierarchical relationship bonus
            hierarchical_bonus = self._calculate_hierarchical_bonus(source_category, target_category)
            result['hierarchical_bonus'] = hierarchical_bonus
            
            # 3. Industry-specific bonus
            industry_bonus = self._calculate_industry_bonus(source_category, target_category)
            result['industry_bonus'] = industry_bonus
            
            # 4. Complementary relationship bonus
            complementary_bonus = self._calculate_complementary_bonus(source_category, target_category)
            result['complementary_bonus'] = complementary_bonus
            
            # Calculate total score
            total_score = min(1.0, base_score + hierarchical_bonus + industry_bonus + complementary_bonus)
            result['total_score'] = total_score
            
            # Generate explanation
            result['explanation'] = self._generate_compatibility_explanation(
                source_category, target_category, result
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating niche compatibility: {str(e)}")
            return {'total_score': 0.0, 'explanation': ['Error in calculation']}
    
    def get_category_hierarchy(self, category_id: str) -> Dict:
        """Get the full hierarchy path for a category"""
        if category_id not in self.categories:
            return {}
        
        category = self.categories[category_id]
        hierarchy = [category]
        
        # Walk up the hierarchy
        current = category
        while current.parent_id:
            parent = self.categories.get(current.parent_id)
            if parent:
                hierarchy.insert(0, parent)
                current = parent
            else:
                break
        
        return {
            'path': [{'id': cat.id, 'name': cat.name, 'level': cat.level} for cat in hierarchy],
            'root': hierarchy[0].id if hierarchy else None,
            'depth': len(hierarchy)
        }

    def _get_base_compatibility(self, source_category: str, target_category: str) -> float:
        """Get base compatibility score from the matrix"""
        if source_category == target_category:
            return CompatibilityLevel.IDENTICAL.value

        # Check direct compatibility
        if source_category in self.compatibility_matrix:
            if target_category in self.compatibility_matrix[source_category]:
                return self.compatibility_matrix[source_category][target_category]

        # Check parent-child relationships
        source_cat = self.categories.get(source_category)
        target_cat = self.categories.get(target_category)

        if source_cat and target_cat:
            # Same parent category
            if source_cat.parent_id == target_cat.parent_id and source_cat.parent_id:
                return CompatibilityLevel.COMPATIBLE.value

            # Parent-child relationship
            if source_cat.parent_id == target_category or target_cat.parent_id == source_category:
                return CompatibilityLevel.HIGHLY_COMPATIBLE.value

        return CompatibilityLevel.NEUTRAL.value

    def _calculate_hierarchical_bonus(self, source_category: str, target_category: str) -> float:
        """Calculate bonus based on hierarchical relationships"""
        source_hierarchy = self.get_category_hierarchy(source_category)
        target_hierarchy = self.get_category_hierarchy(target_category)

        if not source_hierarchy or not target_hierarchy:
            return 0.0

        # Same root category bonus
        if source_hierarchy['root'] == target_hierarchy['root']:
            return 0.1

        # Check if categories share any hierarchy levels
        source_path_ids = [cat['id'] for cat in source_hierarchy['path']]
        target_path_ids = [cat['id'] for cat in target_hierarchy['path']]

        common_ancestors = set(source_path_ids) & set(target_path_ids)
        if common_ancestors:
            return 0.05

        return 0.0

    def _calculate_industry_bonus(self, source_category: str, target_category: str) -> float:
        """Calculate bonus based on industry-specific rules"""
        source_cat = self.categories.get(source_category)
        target_cat = self.categories.get(target_category)

        if not source_cat or not target_cat:
            return 0.0

        bonus = 0.0

        # Check industry tag compatibility
        for source_tag in source_cat.industry_tags:
            if source_tag in self.industry_rules:
                rule = self.industry_rules[source_tag]

                # Check if target has preferred partners
                target_tags = set(target_cat.industry_tags)
                preferred_tags = set(rule['preferred_partners'])
                avoid_tags = set(rule['avoid_partners'])

                if target_tags & preferred_tags:
                    bonus += rule['compatibility_bonus']
                elif target_tags & avoid_tags:
                    bonus -= rule['compatibility_bonus']

        return max(-0.2, min(0.2, bonus))  # Cap bonus/penalty

    def _calculate_complementary_bonus(self, source_category: str, target_category: str) -> float:
        """Calculate bonus for complementary relationships"""
        if source_category in self.complementary_pairs:
            if target_category in self.complementary_pairs[source_category]:
                return 0.15

        if target_category in self.complementary_pairs:
            if source_category in self.complementary_pairs[target_category]:
                return 0.15

        return 0.0

    def _generate_compatibility_explanation(self, source_category: str, target_category: str,
                                          result: Dict) -> List[str]:
        """Generate human-readable explanation for compatibility score"""
        explanations = []

        # Base compatibility explanation
        base_score = result['base_compatibility']
        if base_score >= 0.9:
            explanations.append("Highly compatible niches")
        elif base_score >= 0.7:
            explanations.append("Compatible niches")
        elif base_score >= 0.5:
            explanations.append("Somewhat compatible niches")
        else:
            explanations.append("Different niches with limited compatibility")

        # Hierarchical bonus explanation
        if result['hierarchical_bonus'] > 0:
            explanations.append("Bonus for related category hierarchy")

        # Industry bonus explanation
        if result['industry_bonus'] > 0:
            explanations.append("Bonus for compatible industry focus")
        elif result['industry_bonus'] < 0:
            explanations.append("Penalty for incompatible industry focus")

        # Complementary bonus explanation
        if result['complementary_bonus'] > 0:
            explanations.append("Bonus for complementary niche relationship")

        return explanations

    def find_best_niche_matches(self, source_category: str, limit: int = 10) -> List[Dict]:
        """Find the best niche matches for a given category"""
        matches = []

        for target_category in self.categories.keys():
            if target_category == source_category:
                continue

            compatibility = self.calculate_niche_compatibility(source_category, target_category)

            if compatibility['total_score'] > 0.3:  # Minimum threshold
                target_cat = self.categories[target_category]
                matches.append({
                    'category_id': target_category,
                    'category_name': target_cat.name,
                    'compatibility_score': compatibility['total_score'],
                    'compatibility_breakdown': compatibility,
                    'keywords': target_cat.keywords,
                    'industry_tags': target_cat.industry_tags
                })

        # Sort by compatibility score
        matches.sort(key=lambda x: x['compatibility_score'], reverse=True)
        return matches[:limit]

    def get_cross_niche_opportunities(self, categories: List[str]) -> Dict:
        """Identify cross-niche opportunities for multiple categories"""
        opportunities = {
            'high_compatibility': [],
            'complementary_pairs': [],
            'expansion_suggestions': [],
            'avoid_combinations': []
        }

        # Check all pairs of categories
        for i, cat1 in enumerate(categories):
            for cat2 in categories[i+1:]:
                compatibility = self.calculate_niche_compatibility(cat1, cat2)
                score = compatibility['total_score']

                if score >= 0.8:
                    opportunities['high_compatibility'].append({
                        'category_pair': (cat1, cat2),
                        'score': score,
                        'explanation': compatibility['explanation']
                    })
                elif compatibility.get('complementary_bonus', 0) > 0:
                    opportunities['complementary_pairs'].append({
                        'category_pair': (cat1, cat2),
                        'score': score,
                        'explanation': compatibility['explanation']
                    })
                elif score < 0.3:
                    opportunities['avoid_combinations'].append({
                        'category_pair': (cat1, cat2),
                        'score': score,
                        'reason': 'Low compatibility'
                    })

        # Generate expansion suggestions
        for category in categories:
            best_matches = self.find_best_niche_matches(category, limit=3)
            for match in best_matches:
                if match['category_id'] not in categories:
                    opportunities['expansion_suggestions'].append({
                        'from_category': category,
                        'suggested_category': match['category_id'],
                        'suggested_name': match['category_name'],
                        'compatibility_score': match['compatibility_score']
                    })

        return opportunities

    def get_category_insights(self, category_id: str) -> Dict:
        """Get comprehensive insights about a category"""
        if category_id not in self.categories:
            return {}

        category = self.categories[category_id]
        hierarchy = self.get_category_hierarchy(category_id)
        best_matches = self.find_best_niche_matches(category_id, limit=5)

        # Find subcategories
        subcategories = [
            {'id': cat.id, 'name': cat.name}
            for cat in self.categories.values()
            if cat.parent_id == category_id
        ]

        # Find complementary categories
        complementary = []
        if category_id in self.complementary_pairs:
            for comp_id in self.complementary_pairs[category_id]:
                if comp_id in self.categories:
                    comp_cat = self.categories[comp_id]
                    complementary.append({'id': comp_id, 'name': comp_cat.name})

        return {
            'category': {
                'id': category.id,
                'name': category.name,
                'level': category.level,
                'keywords': category.keywords,
                'industry_tags': category.industry_tags
            },
            'hierarchy': hierarchy,
            'subcategories': subcategories,
            'best_matches': best_matches,
            'complementary_categories': complementary,
            'total_categories_in_system': len(self.categories)
        }
