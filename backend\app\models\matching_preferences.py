"""
Matching Preferences Models for LinkUp Plugin
Manages user-configurable matching preferences and settings
"""
from datetime import datetime
from typing import Dict, Optional
from app import db
from sqlalchemy import Index


class MatchingPreferences(db.Model):
    """Model for user matching preferences"""
    
    __tablename__ = 'matching_preferences'
    
    # Primary key
    id = db.Column(db.Integer, primary_key=True)
    
    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, unique=True)
    website_id = db.Column(db.Integer, db.ForeignKey('websites.id'), nullable=True)  # Website-specific preferences
    
    # Quality thresholds
    min_quality_score = db.Column(db.Float, default=6.0)
    min_domain_authority = db.Column(db.Integer, default=20)
    max_spam_score = db.Column(db.Float, default=3.0)
    min_trust_signals = db.Column(db.Integer, default=3)
    
    # Content preferences
    min_content_similarity = db.Column(db.Float, default=0.3)
    preferred_content_types = db.Column(db.JSON)  # ['blog', 'news', 'educational', etc.]
    content_freshness_weight = db.Column(db.Float, default=0.05)
    
    # Niche and category preferences
    preferred_niches = db.Column(db.JSON)  # List of preferred niche categories
    excluded_niches = db.Column(db.JSON)   # List of excluded niche categories
    cross_niche_matching = db.Column(db.Boolean, default=True)
    niche_compatibility_threshold = db.Column(db.Float, default=0.5)
    
    # Geographic preferences
    geographic_targeting = db.Column(db.Boolean, default=False)
    preferred_countries = db.Column(db.JSON)  # List of ISO country codes
    excluded_countries = db.Column(db.JSON)   # List of excluded countries
    same_timezone_preference = db.Column(db.Boolean, default=False)
    
    # Language preferences
    language_matching = db.Column(db.Boolean, default=True)
    preferred_languages = db.Column(db.JSON)  # List of language codes
    multilingual_matching = db.Column(db.Boolean, default=False)
    
    # Link velocity and timing
    max_links_per_month = db.Column(db.Integer, default=10)
    min_days_between_links = db.Column(db.Integer, default=7)
    preferred_link_timing = db.Column(db.String(50), default='gradual')  # 'immediate', 'gradual', 'scheduled'
    
    # Authority and traffic preferences
    min_partner_authority = db.Column(db.Integer, default=20)
    max_partner_authority = db.Column(db.Integer, default=100)
    traffic_compatibility_weight = db.Column(db.Float, default=0.07)
    authority_matching_strategy = db.Column(db.String(50), default='similar')  # 'similar', 'higher', 'any'
    
    # Advanced matching weights (custom user weights)
    custom_weights = db.Column(db.JSON)  # Custom weight overrides
    use_custom_weights = db.Column(db.Boolean, default=False)
    
    # Notification preferences
    notify_new_matches = db.Column(db.Boolean, default=True)
    notify_quality_issues = db.Column(db.Boolean, default=True)
    notification_frequency = db.Column(db.String(20), default='daily')  # 'immediate', 'daily', 'weekly'
    
    # Auto-approval settings
    auto_approve_high_quality = db.Column(db.Boolean, default=False)
    auto_approve_threshold = db.Column(db.Float, default=0.9)
    auto_reject_low_quality = db.Column(db.Boolean, default=True)
    auto_reject_threshold = db.Column(db.Float, default=0.4)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='matching_preferences')
    website = db.relationship('Website', backref='matching_preferences')
    
    # Indexes
    __table_args__ = (
        Index('idx_user_preferences', 'user_id'),
        Index('idx_website_preferences', 'website_id'),
    )
    
    def __repr__(self):
        return f'<MatchingPreferences user_id={self.user_id}>'
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'website_id': self.website_id,
            
            # Quality thresholds
            'quality_thresholds': {
                'min_quality_score': self.min_quality_score,
                'min_domain_authority': self.min_domain_authority,
                'max_spam_score': self.max_spam_score,
                'min_trust_signals': self.min_trust_signals
            },
            
            # Content preferences
            'content_preferences': {
                'min_content_similarity': self.min_content_similarity,
                'preferred_content_types': self.preferred_content_types or [],
                'content_freshness_weight': self.content_freshness_weight
            },
            
            # Niche preferences
            'niche_preferences': {
                'preferred_niches': self.preferred_niches or [],
                'excluded_niches': self.excluded_niches or [],
                'cross_niche_matching': self.cross_niche_matching,
                'niche_compatibility_threshold': self.niche_compatibility_threshold
            },
            
            # Geographic preferences
            'geographic_preferences': {
                'geographic_targeting': self.geographic_targeting,
                'preferred_countries': self.preferred_countries or [],
                'excluded_countries': self.excluded_countries or [],
                'same_timezone_preference': self.same_timezone_preference
            },
            
            # Language preferences
            'language_preferences': {
                'language_matching': self.language_matching,
                'preferred_languages': self.preferred_languages or [],
                'multilingual_matching': self.multilingual_matching
            },
            
            # Link velocity
            'link_velocity': {
                'max_links_per_month': self.max_links_per_month,
                'min_days_between_links': self.min_days_between_links,
                'preferred_link_timing': self.preferred_link_timing
            },
            
            # Authority preferences
            'authority_preferences': {
                'min_partner_authority': self.min_partner_authority,
                'max_partner_authority': self.max_partner_authority,
                'traffic_compatibility_weight': self.traffic_compatibility_weight,
                'authority_matching_strategy': self.authority_matching_strategy
            },
            
            # Advanced settings
            'advanced_settings': {
                'custom_weights': self.custom_weights or {},
                'use_custom_weights': self.use_custom_weights
            },
            
            # Notifications
            'notification_settings': {
                'notify_new_matches': self.notify_new_matches,
                'notify_quality_issues': self.notify_quality_issues,
                'notification_frequency': self.notification_frequency
            },
            
            # Auto-approval
            'auto_approval': {
                'auto_approve_high_quality': self.auto_approve_high_quality,
                'auto_approve_threshold': self.auto_approve_threshold,
                'auto_reject_low_quality': self.auto_reject_low_quality,
                'auto_reject_threshold': self.auto_reject_threshold
            },
            
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_or_create_for_user(cls, user_id: int, website_id: Optional[int] = None):
        """Get existing preferences or create default ones for a user"""
        preferences = cls.query.filter_by(user_id=user_id, website_id=website_id).first()
        
        if not preferences:
            preferences = cls(
                user_id=user_id,
                website_id=website_id
            )
            db.session.add(preferences)
            db.session.commit()
        
        return preferences
    
    def update_preferences(self, preferences_data: Dict) -> bool:
        """Update preferences from dictionary data"""
        try:
            # Update quality thresholds
            if 'quality_thresholds' in preferences_data:
                qt = preferences_data['quality_thresholds']
                self.min_quality_score = qt.get('min_quality_score', self.min_quality_score)
                self.min_domain_authority = qt.get('min_domain_authority', self.min_domain_authority)
                self.max_spam_score = qt.get('max_spam_score', self.max_spam_score)
                self.min_trust_signals = qt.get('min_trust_signals', self.min_trust_signals)
            
            # Update content preferences
            if 'content_preferences' in preferences_data:
                cp = preferences_data['content_preferences']
                self.min_content_similarity = cp.get('min_content_similarity', self.min_content_similarity)
                self.preferred_content_types = cp.get('preferred_content_types', self.preferred_content_types)
                self.content_freshness_weight = cp.get('content_freshness_weight', self.content_freshness_weight)
            
            # Update niche preferences
            if 'niche_preferences' in preferences_data:
                np = preferences_data['niche_preferences']
                self.preferred_niches = np.get('preferred_niches', self.preferred_niches)
                self.excluded_niches = np.get('excluded_niches', self.excluded_niches)
                self.cross_niche_matching = np.get('cross_niche_matching', self.cross_niche_matching)
                self.niche_compatibility_threshold = np.get('niche_compatibility_threshold', self.niche_compatibility_threshold)
            
            # Update geographic preferences
            if 'geographic_preferences' in preferences_data:
                gp = preferences_data['geographic_preferences']
                self.geographic_targeting = gp.get('geographic_targeting', self.geographic_targeting)
                self.preferred_countries = gp.get('preferred_countries', self.preferred_countries)
                self.excluded_countries = gp.get('excluded_countries', self.excluded_countries)
                self.same_timezone_preference = gp.get('same_timezone_preference', self.same_timezone_preference)
            
            # Update language preferences
            if 'language_preferences' in preferences_data:
                lp = preferences_data['language_preferences']
                self.language_matching = lp.get('language_matching', self.language_matching)
                self.preferred_languages = lp.get('preferred_languages', self.preferred_languages)
                self.multilingual_matching = lp.get('multilingual_matching', self.multilingual_matching)
            
            # Update link velocity
            if 'link_velocity' in preferences_data:
                lv = preferences_data['link_velocity']
                self.max_links_per_month = lv.get('max_links_per_month', self.max_links_per_month)
                self.min_days_between_links = lv.get('min_days_between_links', self.min_days_between_links)
                self.preferred_link_timing = lv.get('preferred_link_timing', self.preferred_link_timing)
            
            # Update authority preferences
            if 'authority_preferences' in preferences_data:
                ap = preferences_data['authority_preferences']
                self.min_partner_authority = ap.get('min_partner_authority', self.min_partner_authority)
                self.max_partner_authority = ap.get('max_partner_authority', self.max_partner_authority)
                self.traffic_compatibility_weight = ap.get('traffic_compatibility_weight', self.traffic_compatibility_weight)
                self.authority_matching_strategy = ap.get('authority_matching_strategy', self.authority_matching_strategy)
            
            # Update advanced settings
            if 'advanced_settings' in preferences_data:
                ads = preferences_data['advanced_settings']
                self.custom_weights = ads.get('custom_weights', self.custom_weights)
                self.use_custom_weights = ads.get('use_custom_weights', self.use_custom_weights)
            
            # Update notification settings
            if 'notification_settings' in preferences_data:
                ns = preferences_data['notification_settings']
                self.notify_new_matches = ns.get('notify_new_matches', self.notify_new_matches)
                self.notify_quality_issues = ns.get('notify_quality_issues', self.notify_quality_issues)
                self.notification_frequency = ns.get('notification_frequency', self.notification_frequency)
            
            # Update auto-approval settings
            if 'auto_approval' in preferences_data:
                aa = preferences_data['auto_approval']
                self.auto_approve_high_quality = aa.get('auto_approve_high_quality', self.auto_approve_high_quality)
                self.auto_approve_threshold = aa.get('auto_approve_threshold', self.auto_approve_threshold)
                self.auto_reject_low_quality = aa.get('auto_reject_low_quality', self.auto_reject_low_quality)
                self.auto_reject_threshold = aa.get('auto_reject_threshold', self.auto_reject_threshold)
            
            self.updated_at = datetime.utcnow()
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def get_effective_weights(self) -> Dict[str, float]:
        """Get effective matching weights (custom or default)"""
        if self.use_custom_weights and self.custom_weights:
            return self.custom_weights
        
        # Return default weights
        return {
            'content_similarity': 0.25,
            'category_match': 0.15,
            'quality_score': 0.15,
            'domain_authority': 0.12,
            'language_match': 0.08,
            'freshness': self.content_freshness_weight,
            'mutual_benefit': 0.08,
            'traffic_compatibility': self.traffic_compatibility_weight,
            'niche_authority': 0.05
        }
