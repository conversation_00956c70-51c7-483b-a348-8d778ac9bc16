"""
Comprehensive Unit Tests for Sprint 7 Features
Tests all Sprint 7 services: competitor analysis, keyword gaps, content opportunities, 
suggestions, keyword research APIs, content optimization, and trending topics
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.competitor_analysis_service import CompetitorAnalysisService
from app.services.keyword_gap_service import KeywordGapService, KeywordGap, GapType
from app.services.content_opportunity_service import ContentOpportunityService
from app.services.suggestion_generation_service import SuggestionGenerationService
from app.services.keyword_research_api_service import KeywordResearchAPIService, APIProvider, KeywordData
from app.services.content_optimization_service import ContentOptimizationService
from app.services.trending_topics_service import TrendingTopicsService, TrendSource, TrendTimeframe


class TestCompetitorAnalysisService(unittest.TestCase):
    """Test cases for CompetitorAnalysisService"""
    
    def setUp(self):
        self.service = CompetitorAnalysisService()
        self.mock_website = Mock()
        self.mock_website.id = 1
        self.mock_website.domain = 'example.com'
        self.mock_website.category = 'technology'
        self.mock_website.domain_authority = 50
    
    def test_init(self):
        """Test service initialization"""
        self.assertIsInstance(self.service, CompetitorAnalysisService)
        self.assertEqual(self.service.max_competitors, 10)
        self.assertEqual(self.service.max_pages_per_competitor, 50)
        self.assertGreater(len(self.service.content_selectors), 0)
    
    @patch('app.models.website.Website.query')
    @patch('app.services.competitor_analysis_service.cache')
    def test_analyze_competitors_cached(self, mock_cache, mock_query):
        """Test competitor analysis with cached results"""
        # Setup
        mock_query.get.return_value = self.mock_website
        cached_result = {'cached': True, 'analysis_summary': {'competitors_analyzed': 5}}
        mock_cache.get.return_value = cached_result
        
        # Execute
        result = self.service.analyze_competitors(1)
        
        # Assert
        self.assertEqual(result, cached_result)
        mock_cache.get.assert_called_once()
    
    @patch('app.models.website.Website.query')
    @patch('app.services.competitor_analysis_service.cache')
    def test_analyze_competitors_no_website(self, mock_cache, mock_query):
        """Test competitor analysis with invalid website ID"""
        # Setup
        mock_query.get.return_value = None
        mock_cache.get.return_value = None
        
        # Execute
        result = self.service.analyze_competitors(999)
        
        # Assert
        self.assertIn('error', result)
        self.assertIn('Website 999 not found', result['error'])
    
    def test_discover_competitors(self):
        """Test competitor discovery"""
        with patch('app.models.website.Website.query') as mock_query:
            # Setup mock similar websites
            mock_similar = Mock()
            mock_similar.domain = 'competitor1.com'
            mock_query.filter.return_value.order_by.return_value.limit.return_value.all.return_value = [mock_similar]
            
            # Execute
            competitors = self.service._discover_competitors(self.mock_website)
            
            # Assert
            self.assertIsInstance(competitors, list)
            self.assertIn('competitor1.com', competitors)
    
    def test_get_category_competitors(self):
        """Test getting category-specific competitors"""
        # Test technology category
        tech_competitors = self.service._get_category_competitors('technology')
        self.assertIn('techcrunch.com', tech_competitors)
        self.assertIn('wired.com', tech_competitors)
        
        # Test unknown category
        unknown_competitors = self.service._get_category_competitors('unknown_category')
        self.assertEqual(unknown_competitors, [])
    
    def test_extract_content_topics(self):
        """Test content topic extraction"""
        content = "This is about artificial intelligence and machine learning. AI is transforming technology."
        topics = self.service._extract_content_topics(content)
        
        self.assertIsInstance(topics, list)
        self.assertTrue(any('artificial' in topic['topic'] for topic in topics))
        self.assertTrue(any('intelligence' in topic['topic'] for topic in topics))
    
    def test_assess_content_quality(self):
        """Test content quality assessment"""
        content_data = {
            'metadata': {
                'description': 'Test description',
                'h1_tags': ['Main Heading'],
                'h2_tags': ['Sub Heading 1', 'Sub Heading 2']
            },
            'main_content': 'A' * 1000,  # 1000 character content
            'additional_content': [{'content': 'B' * 500}]  # 500 character additional content
        }
        
        quality = self.service._assess_content_quality(content_data)
        
        self.assertIn('has_meta_description', quality)
        self.assertIn('has_h1_tags', quality)
        self.assertIn('has_h2_tags', quality)
        self.assertIn('average_content_length', quality)
        self.assertIn('content_depth_score', quality)
        
        self.assertTrue(quality['has_meta_description'])
        self.assertTrue(quality['has_h1_tags'])
        self.assertTrue(quality['has_h2_tags'])
        self.assertEqual(quality['average_content_length'], 750)  # (1000 + 500) / 2


class TestKeywordGapService(unittest.TestCase):
    """Test cases for KeywordGapService"""
    
    def setUp(self):
        self.service = KeywordGapService()
        self.mock_website = Mock()
        self.mock_website.id = 1
        self.mock_website.domain = 'example.com'
    
    def test_init(self):
        """Test service initialization"""
        self.assertIsInstance(self.service, KeywordGapService)
        self.assertEqual(self.service.min_keyword_length, 3)
        self.assertEqual(self.service.max_keyword_length, 50)
        self.assertIn('competitor_usage', self.service.scoring_weights)
    
    @patch('app.services.keyword_gap_service.cache')
    @patch('app.models.website.Website.query')
    def test_analyze_keyword_gaps_cached(self, mock_query, mock_cache):
        """Test keyword gap analysis with cached results"""
        # Setup
        mock_query.get.return_value = self.mock_website
        cached_result = {'cached': True, 'keyword_gaps': []}
        mock_cache.get.return_value = cached_result
        
        # Execute
        result = self.service.analyze_keyword_gaps(1)
        
        # Assert
        self.assertEqual(result, cached_result)
    
    def test_is_valid_keyword(self):
        """Test keyword validation"""
        # Valid keywords
        self.assertTrue(self.service._is_valid_keyword('seo'))
        self.assertTrue(self.service._is_valid_keyword('content marketing'))
        self.assertTrue(self.service._is_valid_keyword('artificial intelligence'))
        
        # Invalid keywords
        self.assertFalse(self.service._is_valid_keyword(''))
        self.assertFalse(self.service._is_valid_keyword('ab'))  # Too short
        self.assertFalse(self.service._is_valid_keyword('123'))  # Numbers only
        self.assertFalse(self.service._is_valid_keyword('the'))  # Stop word
        self.assertFalse(self.service._is_valid_keyword('a' * 60))  # Too long
    
    def test_is_valid_long_tail_keyword(self):
        """Test long-tail keyword validation"""
        # Valid long-tail keywords
        self.assertTrue(self.service._is_valid_long_tail_keyword('how to optimize seo'))
        self.assertTrue(self.service._is_valid_long_tail_keyword('best content marketing tools'))
        
        # Invalid long-tail keywords
        self.assertFalse(self.service._is_valid_long_tail_keyword('seo'))  # Too short
        self.assertFalse(self.service._is_valid_long_tail_keyword('a b c d e f g'))  # Too many words
        self.assertFalse(self.service._is_valid_long_tail_keyword('ab cd'))  # Too short overall
    
    def test_calculate_opportunity_score(self):
        """Test opportunity score calculation"""
        keyword = 'test keyword'
        usage_data = [
            {'domain': 'competitor1.com', 'frequency': 10},
            {'domain': 'competitor2.com', 'frequency': 15}
        ]
        gap_type = GapType.MISSING_KEYWORD
        
        score = self.service._calculate_opportunity_score(keyword, usage_data, gap_type)
        
        self.assertIsInstance(score, float)
        self.assertGreaterEqual(score, 0)
        self.assertLessEqual(score, 100)
    
    def test_estimate_keyword_difficulty(self):
        """Test keyword difficulty estimation"""
        # Single word keyword (should be harder)
        single_word_difficulty = self.service._estimate_keyword_difficulty('seo')
        
        # Multi-word keyword (should be easier)
        multi_word_difficulty = self.service._estimate_keyword_difficulty('how to optimize seo content')
        
        # Commercial keyword (should be harder)
        commercial_difficulty = self.service._estimate_keyword_difficulty('buy seo tools')
        
        # Informational keyword (should be easier)
        info_difficulty = self.service._estimate_keyword_difficulty('how to learn seo')
        
        self.assertGreater(single_word_difficulty, multi_word_difficulty)
        self.assertGreater(commercial_difficulty, info_difficulty)
        self.assertGreaterEqual(single_word_difficulty, 10)
        self.assertLessEqual(single_word_difficulty, 90)
    
    def test_estimate_search_volume(self):
        """Test search volume estimation"""
        # Single word should have higher volume
        single_volume = self.service._estimate_search_volume('seo')
        
        # Long-tail should have lower volume
        long_tail_volume = self.service._estimate_search_volume('how to optimize seo for wordpress websites')
        
        # Question keywords should have good volume
        question_volume = self.service._estimate_search_volume('what is seo')
        
        self.assertGreater(single_volume, long_tail_volume)
        self.assertGreater(question_volume, long_tail_volume)
        self.assertGreaterEqual(single_volume, 10)
    
    def test_suggest_content_type(self):
        """Test content type suggestions"""
        self.assertEqual(self.service._suggest_content_type('how to optimize seo'), 'tutorial')
        self.assertEqual(self.service._suggest_content_type('best seo tools'), 'listicle')
        self.assertEqual(self.service._suggest_content_type('seo vs sem'), 'comparison')
        self.assertEqual(self.service._suggest_content_type('seo guide'), 'guide')
        self.assertEqual(self.service._suggest_content_type('seo'), 'pillar_page')
    
    def test_determine_priority_level(self):
        """Test priority level determination"""
        self.assertEqual(self.service._determine_priority_level(80), 'high')
        self.assertEqual(self.service._determine_priority_level(50), 'medium')
        self.assertEqual(self.service._determine_priority_level(30), 'low')
    
    def test_find_related_keywords(self):
        """Test related keyword finding"""
        related = self.service._find_related_keywords('seo')
        
        self.assertIsInstance(related, list)
        self.assertLessEqual(len(related), 5)
        self.assertTrue(any('best seo' in keyword for keyword in related))
    
    def test_determine_semantic_cluster(self):
        """Test semantic cluster determination"""
        self.assertEqual(self.service._determine_semantic_cluster('how to learn seo'), 'educational')
        self.assertEqual(self.service._determine_semantic_cluster('best seo tools'), 'comparison')
        self.assertEqual(self.service._determine_semantic_cluster('buy seo software'), 'commercial')
        self.assertEqual(self.service._determine_semantic_cluster('what is seo'), 'informational')
        self.assertEqual(self.service._determine_semantic_cluster('seo optimization'), 'general')


class TestContentOpportunityService(unittest.TestCase):
    """Test cases for ContentOpportunityService"""
    
    def setUp(self):
        self.service = ContentOpportunityService()
        self.mock_website = Mock()
        self.mock_website.id = 1
        self.mock_website.domain = 'example.com'
        self.mock_website.category = 'technology'
    
    def test_init(self):
        """Test service initialization"""
        self.assertIsInstance(self.service, ContentOpportunityService)
        self.assertIn('search_volume', self.service.scoring_weights)
        self.assertIn('blog_post', self.service.effort_estimates)
        self.assertIn('blog_post', self.service.length_recommendations)
    
    @patch('app.services.content_opportunity_service.cache')
    @patch('app.models.website.Website.query')
    def test_score_content_opportunities_cached(self, mock_query, mock_cache):
        """Test content opportunity scoring with cached results"""
        # Setup
        mock_query.get.return_value = self.mock_website
        cached_result = {'cached': True, 'opportunities': []}
        mock_cache.get.return_value = cached_result
        
        # Execute
        result = self.service.score_content_opportunities(1)
        
        # Assert
        self.assertEqual(result, cached_result)
    
    def test_calculate_business_relevance(self):
        """Test business relevance calculation"""
        # Category-relevant keyword
        category_score = self.service._calculate_business_relevance('technology trends', self.mock_website)
        
        # Commercial keyword
        commercial_score = self.service._calculate_business_relevance('buy technology tools', self.mock_website)
        
        # Informational keyword
        info_score = self.service._calculate_business_relevance('how to use technology', self.mock_website)
        
        # Generic keyword
        generic_score = self.service._calculate_business_relevance('random keyword', self.mock_website)
        
        self.assertGreater(category_score, generic_score)
        self.assertGreater(commercial_score, generic_score)
        self.assertGreater(info_score, generic_score)
    
    def test_calculate_trend_factor(self):
        """Test trend factor calculation"""
        # Trending keyword
        trending_score = self.service._calculate_trend_factor('ai automation 2024')
        
        # Regular keyword
        regular_score = self.service._calculate_trend_factor('regular keyword')
        
        self.assertGreaterEqual(trending_score, regular_score)
        self.assertLessEqual(trending_score, 10)
    
    def test_calculate_user_intent_score(self):
        """Test user intent score calculation"""
        # Clear intent keywords
        how_to_score = self.service._calculate_user_intent_score('how to optimize seo')
        best_score = self.service._calculate_user_intent_score('best seo tools')
        review_score = self.service._calculate_user_intent_score('seo tool review')
        
        # Unclear intent
        unclear_score = self.service._calculate_user_intent_score('random keyword')
        
        self.assertEqual(how_to_score, 10)
        self.assertEqual(best_score, 9)
        self.assertEqual(review_score, 8)
        self.assertEqual(unclear_score, 5)
    
    def test_estimate_traffic_potential(self):
        """Test traffic potential estimation"""
        # Create mock keyword gap
        gap = Mock()
        gap.search_volume_estimate = 1000
        gap.difficulty_score = 30  # Easy keyword
        
        traffic = self.service._estimate_traffic_potential(gap)
        
        self.assertIsInstance(traffic, int)
        self.assertGreaterEqual(traffic, 1)
        
        # Test with difficult keyword
        gap.difficulty_score = 80  # Hard keyword
        difficult_traffic = self.service._estimate_traffic_potential(gap)
        
        self.assertLess(difficult_traffic, traffic)
    
    def test_determine_competition_level(self):
        """Test competition level determination"""
        self.assertEqual(self.service._determine_competition_level(80), 'high')
        self.assertEqual(self.service._determine_competition_level(50), 'medium')
        self.assertEqual(self.service._determine_competition_level(20), 'low')


class TestKeywordResearchAPIService(unittest.TestCase):
    """Test cases for KeywordResearchAPIService"""
    
    def setUp(self):
        self.service = KeywordResearchAPIService()
    
    def test_init(self):
        """Test service initialization"""
        self.assertIsInstance(self.service, KeywordResearchAPIService)
        self.assertIn(APIProvider.MOCK_API, self.service.rate_limits)
        self.assertIn(APIProvider.MOCK_API, self.service.api_configs)
    
    def test_check_rate_limit(self):
        """Test rate limit checking"""
        # Should allow requests initially
        self.assertTrue(self.service._check_rate_limit(APIProvider.MOCK_API))
        
        # Update rate limit
        self.service._update_rate_limit(APIProvider.MOCK_API)
        
        # Should still allow more requests for mock API (high limits)
        self.assertTrue(self.service._check_rate_limit(APIProvider.MOCK_API))
    
    def test_get_mock_keyword_data(self):
        """Test mock keyword data generation"""
        keyword_data = self.service._get_mock_keyword_data('test keyword', 'US', 'en')
        
        self.assertIsInstance(keyword_data, KeywordData)
        self.assertEqual(keyword_data.keyword, 'test keyword')
        self.assertGreater(keyword_data.search_volume, 0)
        self.assertGreaterEqual(keyword_data.keyword_difficulty, 20)
        self.assertLessEqual(keyword_data.keyword_difficulty, 80)
        self.assertGreater(len(keyword_data.related_keywords), 0)
        self.assertGreater(len(keyword_data.questions), 0)
    
    def test_get_mock_keyword_suggestions(self):
        """Test mock keyword suggestions generation"""
        suggestions = self.service._get_mock_keyword_suggestions('seo', 20)
        
        self.assertIsInstance(suggestions, list)
        self.assertLessEqual(len(suggestions), 20)
        self.assertTrue(all(isinstance(kw, KeywordData) for kw in suggestions))
        self.assertTrue(any('seo' in kw.keyword for kw in suggestions))
    
    def test_get_mock_trending_keywords(self):
        """Test mock trending keywords generation"""
        trending = self.service._get_mock_trending_keywords('technology', '7d')
        
        self.assertIsInstance(trending, list)
        self.assertTrue(all(isinstance(kw, KeywordData) for kw in trending))
        self.assertTrue(any('technology' in kw.keyword for kw in trending))
        self.assertTrue(any('2024' in kw.keyword for kw in trending))
    
    def test_get_mock_question_keywords(self):
        """Test mock question keywords generation"""
        questions = self.service._get_mock_question_keywords('seo')
        
        self.assertIsInstance(questions, list)
        self.assertLessEqual(len(questions), 20)
        self.assertTrue(all('?' in question for question in questions))
        self.assertTrue(any('seo' in question.lower() for question in questions))


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestCompetitorAnalysisService))
    test_suite.addTest(unittest.makeSuite(TestKeywordGapService))
    test_suite.addTest(unittest.makeSuite(TestContentOpportunityService))
    test_suite.addTest(unittest.makeSuite(TestKeywordResearchAPIService))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*50}")
    print(f"SPRINT 7 COMPREHENSIVE TEST RESULTS")
    print(f"{'='*50}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print(f"\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
